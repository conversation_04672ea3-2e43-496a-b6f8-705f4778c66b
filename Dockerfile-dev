# Use official image
FROM node:18-alpine

#install yarn using npm conditionally if not already installed
# Check if yarn is already installed
RUN if ! command yarn -v &> /dev/null; then \
    npm install -g yarn; \
  else \
    echo "Yarn is already installed"; \
  fi


# Set working directory
WORKDIR /app

# Install pnpm globally (optional)
#RUN npm install -g yarn

# Copy project files
COPY . .

# Copy env.dev to .env
COPY .env.dev .env

# Install dependencies
RUN yarn


# Expose app port
EXPOSE 3000

# Run the app
CMD ["yarn", "dev"]
