desc {
    font-size: 32px;
    line-height: 1;
    color: #4D5BCE;
    font-family: 'Fira Code';
}

/* AnimatedBanner Skeleton Loading */
.animated-banner-skeleton .skeleton-text {
    background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    opacity: 0.8;
}

.skeleton-image {
    background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    opacity: 0.6;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive skeleton adjustments for AnimatedBanner */
@media (max-width: 768px) {
    .animated-banner-skeleton .skeleton-text {
        height: 10px;
    }

    .skeleton-image {
        width: 100px !important;
        height: 120px !important;
    }
}

@media (min-width: 1024px) and (max-width: 1536px) {
    .skeleton-image {
        width: 112px !important;
        height: 128px !important;
    }

    .animated-banner-skeleton .skeleton-text {
        height: 10px;
    }
}