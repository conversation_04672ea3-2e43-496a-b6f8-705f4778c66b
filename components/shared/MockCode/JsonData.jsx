/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/MockCode/JsonData.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, July 6th 2023, 9:18:45 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
import React from 'react';
import FrameHeader from './FrameHeader';

const JsonData = ({ jsonData, isContact }) => {
  const keys = Object.keys(jsonData);
  const values = Object.values(jsonData);

  return (
    <div className={isContact ? "mock-code-contact-bg" : "mock-code-bg"}>
      <FrameHeader />
      <div className="mock-code-json-body">
        <h4>
          <span className="mock-code-json-bracket">{"{"}</span>
          {keys.map((key, index) => (
            <div key={index} className="mock-code-body">
              <span className="mock-code-json-key">"{key}"</span>
              <span className="mock-code-json-colon">:</span>
              <span className="mock-code-json-value">"{values[index]}"</span>
              <span className="mock-code-json-comma">{","}</span>
            </div>
          ))}
          <span className="mock-code-json-bracket">{"}"}</span>
        </h4>
      </div>
    </div>
  );
};

export default JsonData;
