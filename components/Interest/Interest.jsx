/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/Bio/Bio.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Tuesday, July 25th 2023, 11:35:39 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */

import './index.css';
import CodeContainer from '@/components/AboutMeSideMenu/CommentedText';

const Interest = ({data}) => {
const staticData = {
  logo: "",
  title: "",
  description: ``,

  }
  const myData = data[0] || staticData;
  return (
        <CodeContainer text={myData.description}></CodeContainer>
  );
};

export default Interest;
