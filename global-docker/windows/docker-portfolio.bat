@echo off
REM Batch script for managing Docker portfolio containers on Windows
REM Usage: docker-portfolio.bat [command]

if "%1"=="" goto help
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="logs" goto logs
if "%1"=="status" goto status
if "%1"=="rebuild" goto rebuild
if "%1"=="help" goto help
goto unknown

:start
echo Starting Docker containers...
docker compose up --build -d
if %errorlevel% equ 0 (
    echo.
    echo Services are now running at:
    echo - Admin Backend: http://localhost:8080
    echo - Admin Frontend: http://localhost:3000
    echo - Portfolio: http://localhost:5000
    echo.
    echo To view logs, run: docker-portfolio.bat logs
    echo To stop containers, run: docker-portfolio.bat stop
) else (
    echo Failed to start containers. Check Docker Desktop is running.
)
goto end

:stop
echo Stopping Docker containers...
docker compose down
if %errorlevel% equ 0 (
    echo All containers stopped successfully.
) else (
    echo Failed to stop containers.
)
goto end

:restart
echo Restarting Docker containers...
call :stop
timeout /t 2 /nobreak >nul
call :start
goto end

:logs
echo Showing logs from all containers...
echo Press Ctrl+C to exit logs
docker compose logs -f
goto end

:status
echo Container status:
docker compose ps
goto end

:rebuild
echo Rebuilding containers...
docker compose down
docker compose build --no-cache
docker compose up -d
if %errorlevel% equ 0 (
    echo Containers rebuilt successfully.
) else (
    echo Failed to rebuild containers.
)
goto end

:help
echo Docker Portfolio Management Script for Windows
echo Usage: docker-portfolio.bat [command]
echo.
echo Available commands:
echo   start     Start all containers
echo   stop      Stop all containers
echo   restart   Restart all containers
echo   logs      Show logs from all containers
echo   status    Show container status
echo   rebuild   Rebuild all containers
echo   help      Show this help message
echo.
echo Examples:
echo   docker-portfolio.bat start
echo   docker-portfolio.bat logs
echo   docker-portfolio.bat rebuild
echo.
echo For more advanced features, use the PowerShell script:
echo   .\docker-portfolio.ps1 help
goto end

:unknown
echo Unknown command: %1
echo.
goto help

:end
