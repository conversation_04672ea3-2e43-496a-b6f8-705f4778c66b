# Docker Compose configuration for all portfolio projects
services:
  # Admin Backend (NestJS)
  admin-backend:
    build:
      context: ./admin-backend
      dockerfile: Dockerfile-dev
    container_name: portfolio-admin-backend
    ports:
      - "8080:8080"
    env_file:
      - ./admin-backend/.env.dev
    environment:
      - NODE_ENV=development
      - CORS_ORIGIN=http://host.docker.internal:3000
      - CORS_ORIGIN=http://host.docker.internal.:5000
    volumes:
      - ./admin-backend:/app
      - /app/node_modules
    command: yarn start:dev
    restart: unless-stopped
    networks:
      - portfolio-network

  # Admin Frontend (Next.js)
  admin-frontend:
    build:
      context: ./admin-frontend
      dockerfile: Dockerfile-dev
    container_name: portfolio-admin-frontend
    ports:
      - "3000:3000"
    env_file:
      - ./admin-frontend/.env.dev
    environment:
      - NODE_ENV=development
    volumes:
      - ./admin-frontend:/app
      - /app/node_modules
      - /app/.next
    command: yarn dev
    restart: unless-stopped
    depends_on:
      - admin-backend
    networks:
      - portfolio-network

  # Portfolio Frontend (Next.js)
  portfolio:
    build:
      context: ./portfolio
      dockerfile: Dockerfile-dev
    container_name: portfolio-frontend
    ports:
      - "5000:5000"
    env_file:
      - ./portfolio/.env.dev
    environment:
      - NODE_ENV=development
    volumes:
      - ./portfolio:/app
      - /app/node_modules
      - /app/.next
    command: yarn dev
    restart: unless-stopped
    networks:
      - portfolio-network

# Define a network for all services to communicate
networks:
  portfolio-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
