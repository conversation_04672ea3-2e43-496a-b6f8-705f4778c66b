version: '3.8'

services:
  nextjs:
    container_name: nextjs
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_APP_URL=http://host.docker.internal:3000
      - NEXT_PUBLIC_SERVER_URL=http://host.docker.internal:8080/api/v1
      - API_URL=http://host.docker.internal:8080/api/
      - API_VERSION=v1
    #    volumes:
    #      - ./:/app
    #      - /app/node_modules
    #      - /app/.next
    volumes:
      - ./:/app/
#      - /nextjs/public:/app/public
    restart: always
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
