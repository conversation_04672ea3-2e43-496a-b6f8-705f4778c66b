#SERVER
API_URL=http://ec2-13-233-121-63.ap-south-1.compute.amazonaws.com/api/
API_VERSION=v1

# -----------------------------------------------------------------------------
# App
# -----------------------------------------------------------------------------
BASEPATH=
NEXT_PUBLIC_APP_URL=http://ec2-43-204-238-211.ap-south-1.compute.amazonaws.com/${BASEPATH}
NEXT_PUBLIC_SERVER_URL=http://ec2-13-233-121-63.ap-south-1.compute.amazonaws.com/api/v1
NEXT_PUBLIC_DOCS_URL=https://demos.themeselection.com/materio-mui-nextjs-admin-template/documentation
NEXT_PUBLIC_PRO_URL=https://demos.themeselection.com/materio-mui-nextjs-admin-template/demo-1
NEXT_PUBLIC_REPO_NAME=materio-mui-nextjs-admin-template-free
NEXTAUTH_SECRET=dasdwewsd232
NEXTAUTH_URL=http://ec2-43-204-238-211.ap-south-1.compute.amazonaws.com

# EmailJS Configuration (Public Key only for frontend)
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_emailjs_public_key

# Cloudinary Configuration (Public settings only for frontend)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dztzlaqdr
