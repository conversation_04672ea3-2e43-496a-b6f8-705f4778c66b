{"name": "next-app-test", "version": "0.1.0", "license": "Commercial", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "build:icons": "tsx src/assets/iconify-icons/bundle-icons-css.ts", "postinstall": "npm run build:icons"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/lab": "5.0.0-alpha.170", "@mui/material": "^5.15.19", "@mui/material-nextjs": "^5.15.11", "@mui/x-date-pickers": "^8.3.0", "apexcharts": "^3.49.1", "classnames": "^2.5.1", "date-fns": "^4.1.0", "next": "^14.2.3", "next-auth": "^4.24.11", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-dom": "^18.3.1", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.10", "react-perfect-scrollbar": "^1.5.8", "react-use": "^17.5.0", "server-only": "^0.0.1"}, "devDependencies": {"@iconify/json": "^2.2.218", "@iconify/tools": "^4.0.4", "@iconify/types": "^2.0.0", "@iconify/utils": "^2.1.24", "@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "^14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "postcss": "^8.4.38", "postcss-styled-syntax": "^0.6.4", "prettier": "^3.3.2", "stylelint": "^16.6.1", "stylelint-use-logical-spec": "^5.0.1", "tailwindcss": "^3.4.4", "tailwindcss-logical": "^3.0.1", "tsx": "^4.15.2", "typescript": "^5.4.5"}, "resolutions": {"rimraf": "^5.0.7"}, "overrides": {"react": "^18.3.1", "rimraf": "^5.0.7"}}