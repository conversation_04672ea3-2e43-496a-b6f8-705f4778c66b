import '@mui/material/styles';
import type { Theme as MuiTheme, ThemeOptions as MuiThemeOptions } from '@mui/material/styles';

// Import the Components type from MUI if available, otherwise use any
declare module '@mui/material/styles' {
  interface Theme extends MuiTheme {
    spacing: (...factors: number[]) => string;
    direction: 'ltr' | 'rtl';
    colorSchemes: any;
    components: any;
    typography: MuiTheme['typography'];
  }
  interface ThemeOptions extends MuiThemeOptions {
    spacing?: (...factors: number[]) => string;
    direction?: 'ltr' | 'rtl';
    colorSchemes?: any;
    components?: any;
    typography?: MuiThemeOptions['typography'];
  }
}
