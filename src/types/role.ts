export interface RoleEntity {
  id?: string
  name: string
  description?: string
  permissions: string[]
  level: 'admin' | 'user' | 'moderator' | 'viewer'
  priority: number
  isActive: boolean
  isDefault: boolean
  createdBy?: string
  updatedBy?: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export interface CreateRoleDto {
  name: string
  description?: string
  permissions?: string[]
  level?: 'admin' | 'user' | 'moderator' | 'viewer'
  priority?: number
  isActive?: boolean
  isDefault?: boolean
  createdBy?: string
}

export interface UpdateRoleDto {
  name?: string
  description?: string
  permissions?: string[]
  level?: 'admin' | 'user' | 'moderator' | 'viewer'
  priority?: number
  isActive?: boolean
  isDefault?: boolean
  updatedBy?: string
}

export interface AssignPermissionsDto {
  roleId: string
  permissions: string[]
  updatedBy?: string
}

// Permission constants (matching backend)
export const PERMISSIONS = {
  // User Management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_LIST: 'user:list',

  // Role Management
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
  ROLE_LIST: 'role:list',
  ROLE_ASSIGN: 'role:assign',

  // Bio Management
  BIO_CREATE: 'bio:create',
  BIO_READ: 'bio:read',
  BIO_UPDATE: 'bio:update',
  BIO_DELETE: 'bio:delete',
  BIO_LIST: 'bio:list',

  // Company Management
  COMPANY_CREATE: 'company:create',
  COMPANY_READ: 'company:read',
  COMPANY_UPDATE: 'company:update',
  COMPANY_DELETE: 'company:delete',
  COMPANY_LIST: 'company:list',

  // Project Management
  PROJECT_CREATE: 'project:create',
  PROJECT_READ: 'project:read',
  PROJECT_UPDATE: 'project:update',
  PROJECT_DELETE: 'project:delete',
  PROJECT_LIST: 'project:list',

  // Skill Management
  SKILL_CREATE: 'skill:create',
  SKILL_READ: 'skill:read',
  SKILL_UPDATE: 'skill:update',
  SKILL_DELETE: 'skill:delete',
  SKILL_LIST: 'skill:list',

  // Education Management
  EDUCATION_CREATE: 'education:create',
  EDUCATION_READ: 'education:read',
  EDUCATION_UPDATE: 'education:update',
  EDUCATION_DELETE: 'education:delete',
  EDUCATION_LIST: 'education:list',

  // Contact Management
  CONTACT_CREATE: 'contact:create',
  CONTACT_READ: 'contact:read',
  CONTACT_UPDATE: 'contact:update',
  CONTACT_DELETE: 'contact:delete',
  CONTACT_LIST: 'contact:list',

  // Social Media Management
  SOCIAL_MEDIA_CREATE: 'social_media:create',
  SOCIAL_MEDIA_READ: 'social_media:read',
  SOCIAL_MEDIA_UPDATE: 'social_media:update',
  SOCIAL_MEDIA_DELETE: 'social_media:delete',
  SOCIAL_MEDIA_LIST: 'social_media:list',

  // Objective Management
  OBJECTIVE_CREATE: 'objective:create',
  OBJECTIVE_READ: 'objective:read',
  OBJECTIVE_UPDATE: 'objective:update',
  OBJECTIVE_DELETE: 'objective:delete',
  OBJECTIVE_LIST: 'objective:list',

  // Interest Management
  INTEREST_CREATE: 'interest:create',
  INTEREST_READ: 'interest:read',
  INTEREST_UPDATE: 'interest:update',
  INTEREST_DELETE: 'interest:delete',
  INTEREST_LIST: 'interest:list',

  // Configuration Management
  CONFIG_CREATE: 'config:create',
  CONFIG_READ: 'config:read',
  CONFIG_UPDATE: 'config:update',
  CONFIG_DELETE: 'config:delete',
  CONFIG_LIST: 'config:list',

  // Analytics & Logs
  ANALYTICS_READ: 'analytics:read',
  VISITOR_LOGS_READ: 'visitor_logs:read',

  // System Administration
  SYSTEM_ADMIN: 'system:admin',
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_RESTORE: 'system:restore'
} as const

export const ROLE_LEVELS = {
  ADMIN: 'admin',
  MODERATOR: 'moderator',
  USER: 'user',
  VIEWER: 'viewer'
} as const

export type PermissionType = (typeof PERMISSIONS)[keyof typeof PERMISSIONS]
export type RoleLevelType = (typeof ROLE_LEVELS)[keyof typeof ROLE_LEVELS]
