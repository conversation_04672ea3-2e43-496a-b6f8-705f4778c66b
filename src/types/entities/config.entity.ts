import type { UserEntity } from '@/types/entities/user.entity'

export type ConfigEntity = {
  id?: string
  theme: string
  language: string
  websiteUrl?: string // New field for unique website URL
  active: boolean
  isDefault: boolean
  isEmailReceivedOn: boolean
  isSmsReceivedOn: boolean
  isPushNotificationReceivedOn: boolean
  isVisitorLogOn: boolean
  isResumeVisibilityOn: boolean
  githubURL?: string
  leetcodeURL?: string
  linkedInURL?: string
  image?: string

  // EmailJS Configuration
  emailjsServiceId?: string
  emailjsTemplateId?: string
  emailjsPublicKey?: string
  emailjsPrivateKey?: string

  // Cloudinary Configuration
  cloudinaryCloudName?: string
  cloudinaryApiKey?: string
  cloudinaryApiSecret?: string

  user?: UserEntity | string // Can be UserEntity object (when receiving) or string ID (when sending)
  activeTill?: Date
  createdAt?: Date
  updatedAt?: Date
}
