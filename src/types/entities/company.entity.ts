import type { SocialMediaEntity } from '@/types/entities/social-media.entity'

export type CompanyEntity = {
  user: any
  id: string
  name: string
  designation: string
  startedAt: Date
  endedAt?: Date | null
  workArea?: string
  workDescription?: string
  companyDescription?: string
  phone?: string
  companyWebsite?: string
  logo?: string
  salary?: number
  currency?: string
  isCurrent: boolean
  isActive: boolean
  active: boolean
  isPromoted: boolean
  socialMedia?: SocialMediaEntity[]
  address?: string
  country?: string
  createdAt?: Date
  updatedAt?: Date
}
