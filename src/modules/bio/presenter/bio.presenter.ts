import { BioEntity, UserEntity } from '@app/repository';

export class BioPresenter {
  id?: string;
  logo: string;
  description: string;
  active: boolean = true;
  user?: UserEntity;
  createdAt?: Date;
  updatedAt?: Date;
  constructor(type: BioEntity) {
    this.id = type.id;
    this.logo = type.logo;
    this.description = type.description;
    this.active = type.active;
    this.user = type.user;
    this.createdAt = type.createdAt;
    this.updatedAt = type.updatedAt;
  }
}
