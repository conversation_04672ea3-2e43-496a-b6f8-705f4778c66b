import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { BioService } from './bio.service';
import { CreateBioDto, GetBioPaginationDto, UpdateBioDto } from './dto';
import {
  CurrentUser,
  JwtAuthGuard,
  PERMISSIONS,
  PermissionsGuard,
  RequirePermissions,
  RequireRoles,
  ROLE,
} from '@app/common';
import { UserEntity } from '@app/repository';
import { Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller({
  path: 'bio',
  version: '1',
})
export class BioController {
  constructor(private readonly bioService: BioService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR, ROLE.USER)
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.BIO_READ, PERMISSIONS.BIO_CREATE)
  @UseInterceptors(FileInterceptor('logo'))
  create(
    @CurrentUser() user: UserEntity,
    @Body() createBioDto: CreateBioDto,
    @UploadedFile() logo?: Express.Multer.File,
  ) {
    return this.bioService.create(createBioDto, user, logo);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.BIO_READ)
  findAll(
    @CurrentUser() user: any,
    @Query() paginationDto: GetBioPaginationDto,
  ) {
    return this.bioService.findAll(user, paginationDto);
  }

  @Get('data')
  async findAllForPortfolio(@Req() request: Request) {
    return this.bioService.findAllByWebsiteUrl(request);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.BIO_READ)
  findOne(@Param('id') id: string) {
    return this.bioService.findOne(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  @RequireRoles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MODERATOR, ROLE.USER)
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.BIO_READ, PERMISSIONS.BIO_UPDATE)
  @UseInterceptors(FileInterceptor('logo'))
  update(
    @Param('id') id: string,
    @Body() updateBioDto: UpdateBioDto,
    @UploadedFile() logo?: Express.Multer.File,
  ) {
    return this.bioService.update(id, updateBioDto, logo);
  }

  @UseGuards(JwtAuthGuard)
  @UseGuards(PermissionsGuard)
  @RequirePermissions(PERMISSIONS.BIO_READ, PERMISSIONS.BIO_DELETE)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.bioService.remove(id);
  }
}
