import { Module } from '@nestjs/common';
import { BioService } from './bio.service';
import { BioController } from './bio.controller';
import { JwtService } from '@nestjs/jwt';
import { RepositoryModule } from '@app/repository';
import { CloudinaryModule } from '@app/common';
import { UserConfigModule } from '../config/config.module';

@Module({
  imports: [RepositoryModule, CloudinaryModule, UserConfigModule],
  controllers: [BioController],
  providers: [BioService, JwtService],
})
export class BioModule {}
