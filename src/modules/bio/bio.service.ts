import { Injectable, Logger } from '@nestjs/common';
import { CreateBioDto } from './dto/create-bio.dto';
import { UpdateBioDto } from './dto/update-bio.dto';
import { GetBioPaginationDto } from './dto';
import { IDataServices, UserEntity } from '@app/repository';
import {
  create,
  deleteById,
  getAll,
  getAllForTenant,
  getById,
  updateById,
} from './services';
import { ConfigService } from '../config/config.service';
import {
  CloudinaryFolder,
  CloudinaryService,
  uploadFileToCloudinary,
} from '@app/common';

@Injectable()
export class BioService {
  private readonly logger = new Logger(BioService.name);
  private cloudinaryAvailable = true;

  constructor(
    private readonly db: IDataServices,
    private readonly cloudinaryService: CloudinaryService,
    private readonly configService: ConfigService,
  ) {}

  async create(
    data: CreateBioDto,
    user: UserEntity,
    logo?: Express.Multer.File,
  ) {
    let logoUrl: string | undefined;
    if (logo) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          logo.buffer,
          this.cloudinaryService,
          CloudinaryFolder.COMPANIES,
        );
        logoUrl = uploadResult.url;
      } catch (error) {
        this.logger.error('Error uploading logo to Cloudinary:', error);
        // Continue without the logo URL if upload fails
      }
    }
    return create(data, this.db, user, logoUrl);
  }

  findAll(user = null, paginationDto?: GetBioPaginationDto) {
    return getAll(this.db, user, paginationDto);
  }

  findOne(id: string) {
    return getById(id, this.db);
  }

  async update(id: string, data: UpdateBioDto, logo?: Express.Multer.File) {
    let logoUrl: string | undefined;

    if (logo && this.cloudinaryAvailable) {
      try {
        const uploadResult = await uploadFileToCloudinary(
          logo.buffer,
          this.cloudinaryService,
          CloudinaryFolder.COMPANIES,
        );
        logoUrl = uploadResult.url;
      } catch (error) {
        this.logger.error(
          'Error uploading logo to Cloudinary for update:',
          error,
        );
        // Continue without the logo URL if upload fails
      }
    } else if (logo && !this.cloudinaryAvailable) {
      logoUrl = 'https://via.placeholder.com/150?text=Company+Logo';
    }

    return updateById(id, data, this.db, logoUrl);
  }

  remove(id: string) {
    return deleteById(id, this.db);
  }

  async findAllByWebsiteUrl(request: any) {
    return getAllForTenant(request, this.db, this.configService);
  }
}
