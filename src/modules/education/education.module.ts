import { Module } from '@nestjs/common';
import { EducationService } from './education.service';
import { EducationController } from './education.controller';
import { JwtService } from '@nestjs/jwt';
import { RepositoryModule } from '@app/repository';
import { CloudinaryModule } from '@app/common/config/cloudinary';
import { UserConfigModule } from '../config/config.module';

@Module({
  imports: [RepositoryModule, CloudinaryModule, UserConfigModule],
  controllers: [EducationController],
  providers: [EducationService, JwtService],
})
export class EducationModule {}
