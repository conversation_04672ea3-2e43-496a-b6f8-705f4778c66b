import { IDataServices } from '@app/repository';
import { CompanyPresenter } from '../presenter';
import { extractTenantUserFromRequest } from '@app/common';
import { ConfigService } from '../../config/config.service';
import { Logger } from '@nestjs/common';

export const getAllForTenant = async (
  request: any,
  db: IDataServices,
  configService: ConfigService,
) => {
  const logger = new Logger('getAllForTenant');

  try {
    // Extract tenant user from request
    const tenantUser = await extractTenantUserFromRequest(
      request,
      configService,
    );

    if (!tenantUser) {
      logger.warn('No tenant user found for request');
      return [];
    }

    logger.log(
      `Fetching companies for tenant user: ${tenantUser.id || tenantUser}`,
    );

    // Fetch companies for this specific user
    const companies = await db.companies.findAll(
      {
        user: tenantUser,
        active: true,
      },
      {
        options: {
          sort: {
            startedAt: -1,
          },
        },
      },
    );

    logger.log(`Found ${companies.length} companies for tenant user`);

    // Apply presenter and return only active companies
    return companies
      .filter((company) => company.active)
      .map((company) => new CompanyPresenter(company));
  } catch (error) {
    logger.error('Error in getAllForTenant:', error);
    return [];
  }
};
