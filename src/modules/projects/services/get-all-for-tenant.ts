import { IDataServices } from '@app/repository';
import { ProjectPresenter } from '../presenter';
import { extractTenantUserFromRequest } from '@app/common';
import { ConfigService } from '../../config/config.service';
import { Logger } from '@nestjs/common';

export const getAllForTenant = async (
  request: any,
  db: IDataServices,
  configService: ConfigService,
) => {
  const logger = new Logger('getAllForTenant');

  try {
    // Extract tenant user from request
    const tenantUser = await extractTenantUserFromRequest(
      request,
      configService,
    );

    if (!tenantUser) {
      logger.warn('No tenant user found for request');
      return [];
    }

    logger.log(
      `Fetching projects for tenant user: ${tenantUser.id || tenantUser}`,
    );

    // Fetch projects for this specific user
    const projects = await db.projects.findAll(
      {
        user: tenantUser,
        active: true,
      },
      {
        options: {
          sort: {
            startedAt: -1,
          },
        },
      },
    );

    logger.log(`Found ${projects.length} projects for tenant user`);

    // Apply presenter and return only active projects
    return projects
      .filter((project) => project.active)
      .map((project) => new ProjectPresenter(project));
  } catch (error) {
    logger.error('Error in getAllForTenant:', error);
    return [];
  }
};
