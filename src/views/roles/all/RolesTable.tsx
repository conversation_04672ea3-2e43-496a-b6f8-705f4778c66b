'use client'

// React Imports
import { useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import Switch from '@mui/material/Switch'
import Tooltip from '@mui/material/Tooltip'
import Typography from '@mui/material/Typography'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

// Type Imports
import type { RoleEntity } from '@/types/role'

interface RolesTableProps {
  roles: RoleEntity[]
  loading: boolean
  error: string | null
  onDeleteSuccess: (roleId: string, roleName: string) => void
  onStatusChange: (roleId: string, newStatus: boolean) => void
}

const RolesTable = ({ roles, loading, error, onDeleteSuccess, onStatusChange }: RolesTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean
    roleId: string | null
    roleName: string
  }>({
    open: false,
    roleId: null,
    roleName: ''
  })

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (roleId: string | undefined, roleName: string) => {
    if (!roleId) return

    setDeleteDialog({
      open: true,
      roleId,
      roleName
    })
  }

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!deleteDialog.roleId) return

    try {
      const response = await fetch(`/api/roles/${deleteDialog.roleId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete role')
      }

      onDeleteSuccess(deleteDialog.roleId, deleteDialog.roleName)
    } catch (error) {
      console.error('Error deleting role:', error)
    } finally {
      setDeleteDialog({ open: false, roleId: null, roleName: '' })
    }
  }

  // Handle status toggle
  const handleStatusToggle = async (roleId: string | undefined, currentStatus: boolean) => {
    if (!roleId) return

    try {
      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !currentStatus })
      })

      if (!response.ok) {
        throw new Error('Failed to update role status')
      }

      onStatusChange(roleId, !currentStatus)
    } catch (error) {
      console.error('Error updating role status:', error)
    }
  }

  // Get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'admin':
        return 'error'
      case 'moderator':
        return 'warning'
      case 'user':
        return 'primary'
      case 'viewer':
        return 'secondary'
      default:
        return 'default'
    }
  }

  if (loading) {
    return <Typography>Loading roles...</Typography>
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Role Name</th>
              <th>Level</th>
              <th>Priority</th>
              <th>Permissions</th>
              <th>Status</th>
              <th>Default</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {roles && roles.length > 0 ? (
              roles.map(role => (
                <tr key={role.id}>
                  <td className='!plb-1'>
                    <div className='flex flex-col'>
                      <Typography className='font-medium' color='text.primary'>
                        {role.name}
                      </Typography>
                      {role.description && (
                        <Typography variant='body2' color='text.secondary'>
                          {role.description}
                        </Typography>
                      )}
                    </div>
                  </td>
                  <td className='!plb-1'>
                    {/*@ts-ignore*/}
                    <Chip
                      label={role.level.charAt(0).toUpperCase() + role.level.slice(1)}
                      color={getLevelColor(role.level) as any}
                      variant='outlined'
                      size='small'
                    />
                  </td>
                  <td className='!plb-1'>
                    <Typography>{role.priority}</Typography>
                  </td>
                  <td className='!plb-1'>
                    <Typography variant='body2'>
                      {role.permissions.length} permission{role.permissions.length !== 1 ? 's' : ''}
                    </Typography>
                  </td>
                  <td className='!plb-1'>
                    <Switch
                      checked={role.isActive}
                      onChange={() => handleStatusToggle(role.id, role.isActive)}
                      size='small'
                    />
                  </td>
                  <td className='!plb-1'>
                    {/*@ts-ignore*/}
                    {role.isDefault && <Chip label='Default' color='info' variant='outlined' size='small' />}
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-2'>
                      <Tooltip title='View'>
                        <IconButton size='small' component={Link} href={`/roles?tab=role&id=${role.id}&mode=view`}>
                          <i className='ri-eye-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton size='small' component={Link} href={`/roles?tab=role&id=${role.id}&mode=edit`}>
                          <i className='ri-pencil-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton size='small' color='error' onClick={() => handleDeleteClick(role.id, role.name)}>
                          <i className='ri-delete-bin-line'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className='text-center py-4'>
                  <Typography>No roles found</Typography>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, roleId: null, roleName: '' })}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the role "{deleteDialog.roleName}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, roleId: null, roleName: '' })}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default RolesTable
