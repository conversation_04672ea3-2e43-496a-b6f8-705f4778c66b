'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Component Imports
import RolesTable from './RolesTable'

// Type Imports
import type { RoleEntity } from '@/types/role'

const AllRoles = () => {
  // States
  const [roles, setRoles] = useState<RoleEntity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch roles
  const fetchRoles = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('Fetching roles from /api/roles...')
      const response = await fetch('/api/roles')

      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)

      if (!response.ok) {
        const errorText = await response.text()

        console.error('Error response:', errorText)
        throw new Error(`Failed to fetch roles: ${response.statusText}`)
      }

      const data = await response.json()

      console.log('Received data:', data)
      setRoles(data.data || data)
    } catch (err) {
      console.error('Error fetching roles:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch roles')
    } finally {
      setLoading(false)
    }
  }

  // Handle delete success
  const handleDeleteSuccess = (deletedRoleId: string, roleName: string) => {
    setRoles(prevRoles => prevRoles.filter(role => role.id !== deletedRoleId))
    setNotification({
      show: true,
      message: `Role "${roleName}" deleted successfully`,
      type: 'success'
    })
  }

  // Handle status change
  const handleStatusChange = (roleId: string, newStatus: boolean) => {
    setRoles(prevRoles => prevRoles.map(role => (role.id === roleId ? { ...role, isActive: newStatus } : role)))
    setNotification({
      show: true,
      message: `Role status updated successfully`,
      type: 'success'
    })
  }

  // Initial fetch
  useEffect(() => {
    fetchRoles()
  }, [])

  return (
    <Card>
      <CardHeader
        title='All Roles'
        action={
          <Button
            variant='contained'
            startIcon={<i className='ri-add-line'></i>}
            component={Link}
            href='/roles?tab=role'
          >
            Add Role
          </Button>
        }
      />
      <CardContent>
        <RolesTable
          roles={roles}
          loading={loading}
          error={error}
          onDeleteSuccess={handleDeleteSuccess}
          onStatusChange={handleStatusChange}
        />
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default AllRoles
