'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import Typography from '@mui/material/Typography'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Type Imports
import type { RoleEntity } from '@/types/role'

const RoleView = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const roleId = searchParams.get('id')

  // States
  const [role, setRole] = useState<RoleEntity | null>(null)
  const [loading, setLoading] = useState(true)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch role data
  useEffect(() => {
    if (roleId) {
      fetchRole()
    }
  }, [roleId])

  const fetchRole = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/roles/${roleId}`)

      if (!response.ok) {
        throw new Error('Failed to fetch role')
      }

      const data = await response.json()
      const roleData = data.data || data

      setRole(roleData)
    } catch (error) {
      console.error('Error fetching role:', error)
      setNotification({
        show: true,
        message: 'Failed to fetch role data',
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // Format date
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'N/A'

    return new Date(date).toLocaleDateString()
  }

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading role details...</Typography>
        </CardContent>
      </Card>
    )
  }

  if (!role) {
    return (
      <Card>
        <CardContent>
          <Typography color='error'>Role not found</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Role Details' subheader='View role information and permissions' />
      <CardContent>
        <Grid container spacing={6}>
          {/* Basic Information */}
          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Role Name' value={role.name || ''} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Level' value={role.level || ''} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Priority' value={role.priority || ''} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Status' value={role.isActive ? 'Active' : 'Inactive'} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Default Role' value={role.isDefault ? 'Yes' : 'No'} disabled />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Created Date' value={formatDate(role.createdAt)} disabled />
          </Grid>

          {/* Description */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label='Description'
              value={role.description || 'No description provided'}
              disabled
              multiline
              rows={3}
            />
          </Grid>

          {/* Permissions */}
          <Grid item xs={12}>
            <Typography variant='h6' className='mb-4'>
              Permissions ({role.permissions?.length || 0})
            </Typography>
            <div className='flex flex-wrap gap-2'>
              {role.permissions && role.permissions.length > 0 ? (
                role.permissions.map((permission, index) => (
                  // @ts-ignore
                  <Chip key={index} label={permission} variant='outlined' color='primary' size='small' />
                ))
              ) : (
                <Typography color='text.secondary'>No permissions assigned</Typography>
              )}
            </div>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/roles?tab=role&id=${roleId}&mode=edit`)}
          >
            Edit Role
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/roles?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default RoleView
