'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import FormControlLabel from '@mui/material/FormControlLabel'
import Switch from '@mui/material/Switch'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import FormGroup from '@mui/material/FormGroup'
import Checkbox from '@mui/material/Checkbox'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'

// Type Imports
import type { CreateRoleDto, RoleEntity, UpdateRoleDto } from '@/types/role'
import { PERMISSIONS, ROLE_LEVELS } from '@/types/role'

const RoleForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()

  // Get URL params
  const roleId = searchParams.get('id')
  const mode = searchParams.get('mode') // 'view', 'edit', or null (create)

  // Determine form mode
  const isEditMode = mode === 'edit' && roleId
  const isViewMode = mode === 'view' && roleId
  const isCreateMode = !mode && !roleId

  // States
  const [formData, setFormData] = useState<RoleEntity>({
    name: '',
    description: '',
    permissions: [],
    level: 'user',
    priority: 0,
    isActive: true,
    isDefault: false
  })

  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(false)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'warning' | 'info'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch role data for edit/view mode
  useEffect(() => {
    if (roleId && (isEditMode || isViewMode)) {
      fetchRole()
    }
  }, [roleId, isEditMode, isViewMode])

  const fetchRole = async () => {
    try {
      setFetchLoading(true)
      const response = await fetch(`/api/roles/${roleId}`)

      if (!response.ok) {
        throw new Error('Failed to fetch role')
      }

      const data = await response.json()

      setFormData(data.data || data)
    } catch (error) {
      console.error('Error fetching role:', error)
      setNotification({
        show: true,
        message: 'Failed to fetch role data',
        type: 'error'
      })
    } finally {
      setFetchLoading(false)
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (isViewMode) return

    try {
      setLoading(true)

      const url = isEditMode ? `/api/roles/${roleId}` : '/api/roles'
      const method = isEditMode ? 'PATCH' : 'POST'

      const payload: CreateRoleDto | UpdateRoleDto = isEditMode
        ? {
            name: formData.name,
            description: formData.description,
            permissions: formData.permissions,
            level: formData.level,
            priority: formData.priority,
            isActive: formData.isActive,
            isDefault: formData.isDefault
          }
        : {
            name: formData.name,
            description: formData.description,
            permissions: formData.permissions,
            level: formData.level,
            priority: formData.priority,
            isActive: formData.isActive,
            isDefault: formData.isDefault
          }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error(`Failed to ${isEditMode ? 'update' : 'create'} role`)
      }

      setNotification({
        show: true,
        message: `Role ${isEditMode ? 'updated' : 'created'} successfully`,
        type: 'success'
      })

      // Redirect to all roles after successful creation/update
      setTimeout(() => {
        router.push('/roles?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)
      setNotification({
        show: true,
        message: `Failed to ${isEditMode ? 'update' : 'create'} role`,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof RoleEntity, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle permission toggle
  const handlePermissionToggle = (permission: string) => {
    if (isViewMode) return

    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }))
  }

  // Handle select all permissions
  const handleSelectAllPermissions = () => {
    if (isViewMode) return

    const allPermissions = Object.values(PERMISSIONS)

    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.length === allPermissions.length ? [] : allPermissions
    }))
  }

  // Get default priority for level
  const getDefaultPriorityForLevel = (level: string) => {
    switch (level) {
      case 'admin':
        return 100
      case 'moderator':
        return 75
      case 'user':
        return 50
      case 'viewer':
        return 25
      default:
        return 0
    }
  }

  // Handle level change
  const handleLevelChange = (level: string) => {
    if (isViewMode) return

    const defaultPriority = getDefaultPriorityForLevel(level)

    setFormData(prev => ({
      ...prev,
      level: level as any,
      priority: defaultPriority,

      // For admin level, automatically set all permissions
      permissions: level === 'admin' ? Object.values(PERMISSIONS) : prev.permissions
    }))
  }

  if (fetchLoading) {
    return (
      <Card>
        <CardContent className='flex justify-center items-center py-8'>
          <CircularProgress />
        </CardContent>
      </Card>
    )
  }

  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Role' : isViewMode ? 'View Role' : 'Add New Role'}
        subheader={isEditMode ? 'Update role information' : isViewMode ? 'View role details' : 'Create a new role'}
      />
      <CardContent>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={6}>
            {/* Basic Information */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Role Name'
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                disabled={Boolean(isViewMode)}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Level</InputLabel>
                <Select
                  value={formData.level}
                  label='Level'
                  onChange={e => handleLevelChange(e.target.value)}
                  disabled={Boolean(isViewMode)}
                >
                  {Object.values(ROLE_LEVELS).map(level => (
                    <MenuItem key={level} value={level}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                value={formData.description}
                onChange={e => handleInputChange('description', e.target.value)}
                disabled={Boolean(isViewMode)}
                multiline
                rows={3}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Priority'
                type='number'
                value={formData.priority}
                onChange={e => handleInputChange('priority', parseInt(e.target.value) || 0)}
                disabled={Boolean(isViewMode)}
                helperText='Higher numbers indicate higher priority'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Box className='flex gap-4'>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={e => handleInputChange('isActive', e.target.checked)}
                      disabled={Boolean(isViewMode)}
                    />
                  }
                  label='Active'
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isDefault}
                      onChange={e => handleInputChange('isDefault', e.target.checked)}
                      disabled={Boolean(isViewMode)}
                    />
                  }
                  label='Default Role'
                />
              </Box>
            </Grid>

            {/* Permissions Section */}
            <Grid item xs={12}>
              <Typography variant='h6' className='mb-4'>
                Permissions
                {!isViewMode && (
                  <Button size='small' onClick={handleSelectAllPermissions} className='ml-4'>
                    {formData.permissions.length === Object.values(PERMISSIONS).length ? 'Deselect All' : 'Select All'}
                  </Button>
                )}
              </Typography>

              <FormGroup>
                <Grid container spacing={2}>
                  {Object.entries(PERMISSIONS).map(([key, permission]) => (
                    <Grid item xs={12} sm={6} md={4} key={permission}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={formData.permissions.includes(permission)}
                            onChange={() => handlePermissionToggle(permission)}
                            disabled={Boolean(isViewMode)}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant='body2' className='font-medium'>
                              {key.replace(/_/g, ' ')}
                            </Typography>
                            <Typography variant='caption' color='text.secondary'>
                              {permission}
                            </Typography>
                          </Box>
                        }
                      />
                    </Grid>
                  ))}
                </Grid>
              </FormGroup>
            </Grid>

            {/* Action Buttons */}
            {!isViewMode && (
              <Grid item xs={12}>
                <Box className='flex gap-4'>
                  <Button
                    type='submit'
                    variant='contained'
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : null}
                  >
                    {loading ? 'Saving...' : isEditMode ? 'Update Role' : 'Create Role'}
                  </Button>
                  <Button variant='outlined' onClick={() => router.push('/roles?tab=all')}>
                    Cancel
                  </Button>
                </Box>
              </Grid>
            )}

            {isViewMode && (
              <Grid item xs={12}>
                <Box className='flex gap-4'>
                  <Button variant='contained' onClick={() => router.push(`/roles?tab=role&id=${roleId}&mode=edit`)}>
                    Edit Role
                  </Button>
                  <Button variant='outlined' onClick={() => router.push('/roles?tab=all')}>
                    Back to List
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default RoleForm
