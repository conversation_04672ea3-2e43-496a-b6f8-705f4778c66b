'use client'

import React, { useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Switch from '@mui/material/Switch'
import Pagination from '@mui/material/Pagination'
import TextField from '@mui/material/TextField'
import MenuItem from '@mui/material/MenuItem'
import Box from '@mui/material/Box'

// Type Imports
import type { ContactEntity } from '@/types/entities'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

type ContactTableProps = {
  contacts: ContactEntity[]
  loading: boolean
  error: string | null
  groupByUser?: boolean
  selectedUserId?: string | null
  onDeleteSuccess: (deletedContactId: string) => void
  onStatusChange?: (id: string, active: boolean) => Promise<void>

  // Pagination props
  page?: number
  limit?: number
  total?: number
  totalPages?: number
  onPageChange?: (event: React.ChangeEvent<unknown>, newPage: number) => void
  onLimitChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const ContactTable = ({
  contacts,
  loading,
  error,
  groupByUser = true,
  selectedUserId = null,
  onDeleteSuccess,
  onStatusChange,

  // Pagination props
  page = 1,
  limit = 10,
  total = 0,
  totalPages = 0,
  onPageChange,
  onLimitChange
}: ContactTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; contactId: string | null; contactName: string }>({
    open: false,
    contactId: null,
    contactName: ''
  })

  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)

  // State for tracking contacts being updated
  const [updatingContacts, setUpdatingContacts] = useState<Record<string, boolean>>({})

  // State for local toggle status (for visual feedback when API fails)
  const [localToggleStatus, setLocalToggleStatus] = useState<Record<string, boolean>>({})

  // Filter contacts by selected user
  const filteredContacts = React.useMemo(() => {
    if (!contacts || contacts.length === 0) return []

    if (selectedUserId && selectedUserId !== 'all') {
      return contacts.filter(contact => contact.user?.id === selectedUserId)
    }

    return contacts
  }, [contacts, selectedUserId])

  // Group contacts by user
  const groupedContacts = React.useMemo(() => {
    if (!filteredContacts || filteredContacts.length === 0) return {}

    return filteredContacts.reduce(
      (groups, contact) => {
        const userId = contact.user?.id || 'unknown'

        if (!groups[userId]) {
          groups[userId] = {
            user: contact.user || { id: 'unknown', name: 'Unknown User', email: '', password: '' },
            contacts: []
          }
        }

        groups[userId].contacts.push(contact)

        return groups
      },
      {} as Record<string, { user: any; contacts: ContactEntity[] }>
    )
  }, [filteredContacts])

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (contactId: string, contactName: string) => {
    setDeleteDialog({
      open: true,
      contactId,
      contactName
    })
  }

  // Handle closing delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      contactId: null,
      contactName: ''
    })
    setDeleteError(null) // Reset error state when closing dialog
  }

  // Handle confirming deletion
  const handleConfirmDelete = async () => {
    if (!deleteDialog.contactId) return

    const contactIdToDelete = deleteDialog.contactId

    try {
      setDeleteLoading(true)
      setDeleteError(null)

      const response = await fetch(`/api/contact/${contactIdToDelete}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to delete contact')
      }

      // Close dialog and notify parent of successful deletion with contact ID
      handleCloseDeleteDialog()
      onDeleteSuccess(contactIdToDelete)
    } catch (err) {
      console.error('Error deleting contact:', err)
      setDeleteError(err instanceof Error ? err.message : 'An error occurred while deleting the contact')
    } finally {
      setDeleteLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='flex justify-center p-6'>
        <CircularProgress />
      </div>
    )
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: boolean) => {
    if (!onStatusChange) return

    try {
      // Set the contact as updating
      setUpdatingContacts(prev => ({ ...prev, [id]: true }))

      // Update local toggle state immediately for visual feedback
      setLocalToggleStatus(prev => ({
        ...prev,
        [id]: !currentStatus
      }))

      // Call the parent handler to update the status
      await onStatusChange(id, !currentStatus)
    } catch (error) {
      console.error('Error updating contact status:', error)

      // Keep the local toggle state even if API fails
    } finally {
      // Remove the updating state
      setUpdatingContacts(prev => {
        const newState = { ...prev }

        delete newState[id]

        return newState
      })
    }
  }

  // Get the effective active status (use local state if available, otherwise use contact.active)
  const getActiveStatus = (contact: ContactEntity) => {
    return localToggleStatus[contact.id as string] !== undefined
      ? localToggleStatus[contact.id as string]
      : contact.active
  }

  // @ts-ignore
  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Contact</th>
              <th>Type</th>
              <th>Link</th>
              <th>Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {groupByUser ? (
              // Grouped view
              Object.keys(groupedContacts).length > 0 ? (
                Object.entries(groupedContacts).map(([userId, { user, contacts: userContacts }]) => (
                  <React.Fragment key={userId}>
                    {/* User Header Row */}
                    <tr className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-l-4 border-blue-500'>
                      <td colSpan={5} className='!plb-4 !pli-6'>
                        <div className='flex items-center gap-4'>
                          {user.profileImage ? (
                            <img
                              src={user.profileImage}
                              alt={user.name}
                              width={40}
                              height={40}
                              className='rounded-full object-cover ring-2 ring-blue-200 dark:ring-blue-600'
                            />
                          ) : (
                            <div
                              className='flex items-center justify-center bg-blue-100 text-blue-600 rounded-full ring-2 ring-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:ring-blue-600'
                              style={{ width: 40, height: 40, fontSize: '16px', fontWeight: 600 }}
                            >
                              {user.name?.charAt(0) || 'U'}
                            </div>
                          )}
                          <div className='flex-1'>
                            <Typography variant='h6' className='font-bold text-gray-800 dark:text-gray-100'>
                              {user.name || 'Unknown User'}
                            </Typography>
                            <Typography variant='body2' className='text-blue-600 dark:text-blue-400 font-medium'>
                              {userContacts.length} contact record{userContacts.length !== 1 ? 's' : ''}
                            </Typography>
                          </div>
                          <div className='text-right'>
                            <Typography variant='caption' className='text-gray-500 dark:text-gray-400'>
                              {user.email}
                            </Typography>
                          </div>
                        </div>
                      </td>
                    </tr>
                    {/* Contact Records for this User */}
                    {userContacts.map((contact, index) => (
                      <tr
                        key={contact.id}
                        className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      >
                        <td className='!plb-1'>
                          <div className='flex items-center gap-3 ml-8'>
                            {contact.logo ? (
                              <img
                                src={contact.logo}
                                alt={contact.name}
                                width={32}
                                height={32}
                                className='rounded-lg object-cover shadow-sm border border-gray-200'
                              />
                            ) : (
                              <div
                                className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                                style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                              >
                                {contact.name.charAt(0)}
                              </div>
                            )}
                            <div>
                              <Typography className='font-semibold text-gray-900'>{contact.name}</Typography>
                              <Typography variant='caption' className='text-gray-600'>
                                Contact
                              </Typography>
                            </div>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <div
                            className={`px-3 py-1 rounded-full text-xs font-medium inline-block bg-blue-100 text-blue-700`}
                          >
                            {contact.type}
                          </div>
                        </td>
                        <td className='!plb-1'>
                          {contact.link ? (
                            <a
                              href={contact.link}
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-blue-600 hover:text-blue-800 font-medium'
                            >
                              Visit Link
                            </a>
                          ) : (
                            <Typography variant='body2' className='text-gray-500'>
                              No link
                            </Typography>
                          )}
                        </td>
                        <td className='!plb-1'>
                          {onStatusChange ? (
                            <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                              <Tooltip title={getActiveStatus(contact) ? 'Deactivate' : 'Activate'}>
                                <span>
                                  <Switch
                                    checked={getActiveStatus(contact)}
                                    onChange={() => handleStatusToggle(contact.id as string, getActiveStatus(contact))}
                                    disabled={updatingContacts[contact.id as string]}
                                    color='primary'
                                    size='medium'
                                  />
                                </span>
                              </Tooltip>
                              <div
                                style={{
                                  width: '24px',
                                  display: 'inline-flex',
                                  justifyContent: 'center',
                                  marginLeft: '8px'
                                }}
                              >
                                {updatingContacts[contact.id as string] && <CircularProgress size={18} />}
                              </div>
                            </div>
                          ) : (
                            <div className='flex items-center justify-center'>
                              <div
                                className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(contact) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                              >
                                {getActiveStatus(contact) ? 'Active' : 'Inactive'}
                              </div>
                            </div>
                          )}
                        </td>
                        <td className='!plb-1'>
                          <div className='flex gap-1 justify-center'>
                            <Tooltip title='View'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/contact?tab=contact&id=${contact.id}&mode=view`}
                                className='hover:bg-blue-50'
                              >
                                <i className='ri-eye-line text-blue-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Edit'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/contact?tab=contact&id=${contact.id}&mode=edit`}
                                className='hover:bg-orange-50'
                              >
                                <i className='ri-pencil-line text-orange-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Delete'>
                              <IconButton
                                size='small'
                                onClick={() => handleDeleteClick(contact.id as string, contact.name)}
                                className='hover:bg-red-50'
                              >
                                <i className='ri-delete-bin-line text-red-600'></i>
                              </IconButton>
                            </Tooltip>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className='text-center py-12'>
                    <div className='flex flex-col items-center gap-3'>
                      <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
                        <i className='ri-contacts-line text-2xl text-gray-400'></i>
                      </div>
                      <Typography variant='h6' className='text-gray-500'>
                        No contact records found
                      </Typography>
                      <Typography variant='body2' className='text-gray-400'>
                        {selectedUserId && selectedUserId !== 'all'
                          ? 'No contact records found for the selected user'
                          : 'Start by adding your first contact record'}
                      </Typography>
                    </div>
                  </td>
                </tr>
              )
            ) : // List view (ungrouped)
            filteredContacts && filteredContacts.length > 0 ? (
              filteredContacts.map((contact, index) => (
                <tr
                  key={contact.id}
                  className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      {contact.logo ? (
                        <img
                          src={contact.logo}
                          alt={contact.name}
                          width={32}
                          height={32}
                          className='rounded-lg object-cover shadow-sm border border-gray-200'
                        />
                      ) : (
                        <div
                          className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                          style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                        >
                          {contact.name.charAt(0)}
                        </div>
                      )}
                      <div>
                        <Typography className='font-semibold text-gray-900'>{contact.name}</Typography>
                        <Typography variant='caption' className='text-gray-600'>
                          Contact
                        </Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div
                      className={`px-3 py-1 rounded-full text-xs font-medium inline-block bg-blue-100 text-blue-700`}
                    >
                      {contact.type}
                    </div>
                  </td>
                  <td className='!plb-1'>
                    {contact.link ? (
                      <a
                        href={contact.link}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-600 hover:text-blue-800 font-medium'
                      >
                        Visit Link
                      </a>
                    ) : (
                      <Typography variant='body2' className='text-gray-500'>
                        No link
                      </Typography>
                    )}
                  </td>
                  <td className='!plb-1'>
                    {onStatusChange ? (
                      <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                        <Tooltip title={getActiveStatus(contact) ? 'Deactivate' : 'Activate'}>
                          <span>
                            <Switch
                              checked={getActiveStatus(contact)}
                              onChange={() => handleStatusToggle(contact.id as string, getActiveStatus(contact))}
                              disabled={updatingContacts[contact.id as string]}
                              color='primary'
                              size='medium'
                            />
                          </span>
                        </Tooltip>
                        <div
                          style={{ width: '24px', display: 'inline-flex', justifyContent: 'center', marginLeft: '8px' }}
                        >
                          {updatingContacts[contact.id as string] && <CircularProgress size={18} />}
                        </div>
                      </div>
                    ) : (
                      <div className='flex items-center justify-center'>
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(contact) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                        >
                          {getActiveStatus(contact) ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                    )}
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-1 justify-center'>
                      <Tooltip title='View'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/contact?tab=contact&id=${contact.id}&mode=view`}
                          className='hover:bg-blue-50'
                        >
                          <i className='ri-eye-line text-blue-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/contact?tab=contact&id=${contact.id}&mode=edit`}
                          className='hover:bg-orange-50'
                        >
                          <i className='ri-pencil-line text-orange-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          onClick={() => handleDeleteClick(contact.id as string, contact.name)}
                          className='hover:bg-red-50'
                        >
                          <i className='ri-delete-bin-line text-red-600'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className='text-center py-12'>
                  <div className='flex flex-col items-center gap-3'>
                    <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
                      <i className='ri-contacts-line text-2xl text-gray-400'></i>
                    </div>
                    <Typography variant='h6' className='text-gray-500'>
                      No contact records found
                    </Typography>
                    <Typography variant='body2' className='text-gray-400'>
                      {selectedUserId && selectedUserId !== 'all'
                        ? 'No contact records found for the selected user'
                        : 'Start by adding your first contact record'}
                    </Typography>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {total > 0 && totalPages > 0 && onPageChange && onLimitChange && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3, px: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant='body2' color='text.secondary'>
              Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} contacts
            </Typography>
            <TextField
              select
              size='small'
              label='Per page'
              value={limit}
              onChange={onLimitChange}
              disabled={loading}
              sx={{ minWidth: 100 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </TextField>
          </Box>
          {/*@ts-ignore*/}
          <Pagination
            count={totalPages}
            page={page}
            onChange={onPageChange}
            disabled={loading}
            color='primary'
            shape='rounded'
            showFirstButton
            showLastButton
            size='medium'
          />
        </Box>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
      >
        <DialogTitle id='delete-dialog-title'>Delete Contact</DialogTitle>
        <DialogContent>
          <DialogContentText id='delete-dialog-description'>
            Are you sure you want to delete the contact <strong>{deleteDialog.contactName}</strong>? This action cannot
            be undone.
          </DialogContentText>
          {deleteError && (
            <Typography color='error' className='mt-4'>
              {deleteError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color='error'
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : null}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default ContactTable
