'use client'

// React Imports
import React, { useEffect, useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Type Imports
import type { ContactEntity } from '@/types/entities'

// Component Imports
import ContactTable from './ContactTable'

const AllContacts = () => {
  // States
  const [contacts, setContacts] = useState<ContactEntity[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Pagination states
  const [page, setPage] = useState<number>(1)
  const [limit, setLimit] = useState<number>(10)
  const [total, setTotal] = useState<number>(0)
  const [totalPages, setTotalPages] = useState<number>(0)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch contacts function - only fetch current user's contacts
  const fetchContacts = async (currentPage = page, currentLimit = limit) => {
    try {
      setLoading(true)

      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: currentLimit.toString()
      })

      const response = await fetch(`/api/contact?${queryParams.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch contacts')
      }

      const data = await response.json()

      // Handle new paginated response structure
      if (data.data && Array.isArray(data.data)) {
        setContacts(data.data)
        setTotal(data.total || 0)
        setTotalPages(data.totalPages || 0)
        setPage(data.page || currentPage)
      } else {
        // Fallback for non-paginated response (backward compatibility)
        setContacts(Array.isArray(data) ? data : [])
        setTotal(Array.isArray(data) ? data.length : 0)
        setTotalPages(1)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching contacts')
    } finally {
      setLoading(false)
    }
  }

  // Fetch contacts on component mount
  useEffect(() => {
    fetchContacts()
  }, [])

  // Handle pagination changes
  const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage)
    fetchContacts(newPage, limit)
  }

  const handleLimitChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10)

    setLimit(newLimit)
    setPage(1) // Reset to first page when changing limit
    fetchContacts(1, newLimit)
  }

  // Handle successful deletion
  const handleDeleteSuccess = (deletedContactId: string) => {
    setNotification({
      show: true,
      message: 'Contact deleted successfully',
      type: 'success'
    })

    // Update local state by removing the deleted contact
    setContacts(prevContacts => prevContacts.filter(contact => contact.id !== deletedContactId))

    // Update total count
    setTotal(prevTotal => Math.max(0, prevTotal - 1))
  }

  // Handle contact status change
  const handleStatusChange = async (id: string, active: boolean) => {
    try {
      // Create FormData for the API call
      const formData = new FormData()

      formData.append('active', active.toString())

      // Make API call to update the contact
      const response = await fetch(`/api/contact/${id}`, {
        method: 'PATCH',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to update contact status')
      }

      // Show success notification
      setNotification({
        show: true,
        message: `Contact ${active ? 'activated' : 'deactivated'} successfully`,
        type: 'success'
      })

      // Update the local state with the new status
      setContacts(prevContacts => prevContacts.map(contact => (contact.id === id ? { ...contact, active } : contact)))
    } catch (error) {
      // Show error notification
      setNotification({
        show: true,
        message: error instanceof Error ? error.message : 'Failed to update contact status',
        type: 'error'
      })

      // Don't rethrow the error - let the UI handle it gracefully
      // The ContactTable component will keep the toggle in the new state
    }
  }

  return (
    <Card>
      <CardHeader
        title={
          <div className='flex items-center gap-2'>
            <i className='ri-contacts-line text-2xl text-blue-600'></i>
            <span>My Contact Records</span>
          </div>
        }
        action={
          <Button
            variant='contained'
            startIcon={<i className='ri-add-line'></i>}
            component={Link}
            href='/contact?tab=contact'
          >
            Add Contact
          </Button>
        }
      />
      <CardContent>
        <ContactTable
          contacts={contacts}
          loading={loading}
          error={error}
          groupByUser={false}
          selectedUserId={null}
          onDeleteSuccess={handleDeleteSuccess}
          onStatusChange={handleStatusChange}
          page={page}
          limit={limit}
          total={total}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
        />
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default AllContacts
