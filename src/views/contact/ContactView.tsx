'use client'


// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

import { useContactForm } from './hooks/useContactForm'

const ContactView = () => {
  const { formData, imgSrc, loading, fetchLoading, notification, isViewMode, contactId, setNotification, router } =
    useContactForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title='View Contact' />
        <CardContent>
          <Typography>Loading contact data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='View Contact' subheader='View contact details' />
      <Divider />
      <CardContent>
        <div className='flex max-sm:flex-col items-center gap-6 mb-6'>
          <img
            height={100}
            width={100}
            className='rounded'
            src={imgSrc || '/images/avatars/default.png'}
            alt='Contact Logo'
          />
          <div className='flex flex-col'>
            <Typography variant='h6'>{formData.name}</Typography>
            <Typography variant='body2' color='text.secondary'>
              {formData.type}
            </Typography>
          </div>
        </div>

        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Description
            </Typography>
            <Typography variant='body1'>{formData.description || 'No description provided'}</Typography>
          </Grid>

          {formData.link && (
            <Grid item xs={12}>
              <Typography variant='subtitle1' fontWeight='bold'>
                Link
              </Typography>
              <Typography variant='body1'>
                <a href={formData.link} target='_blank' rel='noopener noreferrer' className='text-primary'>
                  {formData.link}
                </a>
              </Typography>
            </Grid>
          )}
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => {
              router.push(`/contact?id=${contactId}&mode=edit`)
            }}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/contact')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default ContactView
