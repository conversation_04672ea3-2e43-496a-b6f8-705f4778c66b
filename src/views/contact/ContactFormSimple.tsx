'use client'

import { useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import Divider from '@mui/material/Divider'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'

// Type Imports
import type { ContactEntity } from '@/types/entities'

// Empty contact template for new contacts
const emptyContact: ContactEntity = {
  type: '',
  logo: '',
  name: '',
  description: '',
  link: '',
  user: undefined,
  active: false
}

const ContactFormSimple = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const contactId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isEditMode = mode === 'edit' || (!mode && contactId)

  // States
  const [formData, setFormData] = useState<ContactEntity>(emptyContact)
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch contact data if in edit mode
  useState(() => {
    if (isEditMode && contactId) {
      const fetchContact = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/contact/${contactId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch contact')
          }

          const data = await response.json()

          // Set form data
          setFormData(data)

          // Set image if available
          if (data.logo && data.logo.length > 0) {
            setImgSrc(data.logo[0])
          }
        } catch (err) {
          console.error('Error fetching contact:', err)
          setNotification({
            show: true,
            message: err instanceof Error ? err.message : 'Failed to load contact data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchContact()
    }
  })

  // Handle form field changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      setLogoFile(files[0])

      // Update file input value
      setFileInput(event.target.value)

      // Create preview
      reader.onload = () => {
        setImgSrc(reader.result as string)
      }

      reader.readAsDataURL(files[0])
    }
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form reset
  const handleReset = () => {
    setFormData(isEditMode && contactId ? formData : emptyContact)

    if (!isEditMode) {
      handleFileInputReset()
    }
  }

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name) {
      newErrors.name = 'Name is required'
    }

    if (!formData.type) {
      newErrors.type = 'Type is required'
    }

    setErrors(newErrors)

    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)

      // Create FormData object
      const formDataObj = new FormData()

      // Process form data
      Object.keys(formData).forEach(key => {
        // Skip logo field as we'll handle it separately
        if (key === 'logo') return

        // Add other fields to FormData
        // @ts-ignore
        formDataObj.append(key, formData[key] as string)
      })

      // Add logo file if available
      if (logoFile) {
        formDataObj.append('logo', logoFile)
      }

      // Set up request URL and method
      let url = '/api/contact'
      let method = 'POST'

      // If editing, use PATCH method and include ID in URL
      if (isEditMode && contactId) {
        url = `/api/contact/${contactId}`
        method = 'PATCH'
      }

      // Send the request
      const res = await fetch(url, {
        method,
        body: formDataObj
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save contact')
      }

      // Handle successful response
      await res.json()

      // Show success notification
      setNotification({
        show: true,
        message: isEditMode ? 'Contact updated successfully' : 'Contact created successfully',
        type: 'success'
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/contact')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract error message
      let errorMessage = 'Failed to save contact'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Contact' : 'Add New Contact'} />
        <CardContent className='flex justify-center items-center p-8'>
          <CircularProgress />
          <Typography className='ml-4'>Loading contact data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Contact' : 'Add New Contact'}
        subheader={isEditMode ? 'Update contact information' : 'Create a new contact'}
      />
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className='flex max-sm:flex-col items-center gap-6 mb-6'>
            <img
              height={100}
              width={100}
              className='rounded'
              src={imgSrc || '/images/avatars/default.png'}
              alt='Contact Logo'
            />
            <div className='flex flex-grow flex-col gap-4'>
              <div className='flex flex-col sm:flex-row gap-4'>
                <Button component='label' size='small' variant='contained' htmlFor='contact-logo-upload'>
                  Upload Logo
                  <input
                    hidden
                    type='file'
                    value={fileInput}
                    accept='image/png, image/jpeg'
                    onChange={handleFileInputChange}
                    id='contact-logo-upload'
                  />
                </Button>
                <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                  Reset
                </Button>
              </div>
              <Typography>Allowed JPG, GIF or PNG. Max size of 800K</Typography>
            </div>
          </div>

          <Grid container spacing={5}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name='name'
                label='Name'
                value={formData.name}
                onChange={handleInputChange}
                error={Boolean(errors.name)}
                helperText={errors.name || ''}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name='type'
                label='Type'
                value={formData.type}
                onChange={handleInputChange}
                error={Boolean(errors.type)}
                helperText={errors.type || ''}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name='link'
                label='Link'
                value={formData.link}
                onChange={handleInputChange}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name='description'
                label='Description'
                value={formData.description}
                onChange={handleInputChange}
                multiline
                rows={4}
                margin='normal'
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update' : 'Save'}
            </Button>
            <Button variant='outlined' color='secondary' onClick={handleReset} disabled={loading} type='button'>
              Reset
            </Button>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => router.push('/contact')}
              disabled={loading}
              type='button'
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default ContactFormSimple
