'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { useForm } from 'react-hook-form'
import type { SubmitHandler } from 'react-hook-form'

import type { ContactEntity } from '@/types/entities'

// Empty contact template for new contacts
export const emptyContact: ContactEntity = {
  type: '',
  // @ts-ignore
  logo: [],
  name: '',
  description: '',
  link: ''
}

export const useContactForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const contactId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && contactId)

  // States
  const [formData, setFormData] = useState<ContactEntity>(emptyContact)
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)
  const [isSuccessfullySaved, setIsSuccessfullySaved] = useState<boolean>(false)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<ContactEntity>({
    defaultValues: emptyContact,
    mode: 'onBlur' // Change validation mode to prevent continuous validation
  })

  // Fetch contact data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && contactId) {
      const fetchContact = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/contact/${contactId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch contact')
          }

          const data = await response.json()

          // Update all state in a single batch to prevent multiple re-renders
          setFormData(data)

          // Set image if available
          if (data.logo && data.logo.length > 0) {
            setImgSrc(data.logo[0])
          }

          // Reset form with fetched data - use setTimeout to ensure it happens after render
          setTimeout(() => {
            reset(data, {
              keepDirty: false,
              keepErrors: false,
              keepIsSubmitted: false,
              keepTouched: false,
              keepIsValid: false,
              keepSubmitCount: false
            })
          }, 0)
        } catch (err) {
          console.error('Error fetching contact:', err)
          setNotification({
            show: true,
            message: err instanceof Error ? err.message : 'Failed to load contact data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchContact()
    }
  }, [contactId, isEditMode, isViewMode])

  const onSubmit: SubmitHandler<ContactEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Process form data
      Object.keys(data).forEach(key => {
        // Skip logo field as we'll handle it separately
        if (key === 'logo') return

        // Add other fields to FormData
        // @ts-ignore
        formData.append(key, data[key] as string)
      })

      // Add logo file if available
      if (logoFile) {
        formData.append('logo', logoFile)
      }

      // Set up request URL and method
      let url = '/api/contact'
      let method = 'POST'

      // If editing, use PATCH method and include ID in URL
      if (isEditMode && contactId) {
        url = `/api/contact/${contactId}`
        method = 'PATCH'
      }

      // Send the request
      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save contact')
      }

      // Handle successful response
      await res.json()

      // Mark as successfully saved to hide the form
      setIsSuccessfullySaved(true)

      // Show success notification
      setNotification({
        show: true,
        message: isEditMode ? 'Contact updated successfully' : 'Contact created successfully',
        type: 'success'
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/contact')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract error message
      let errorMessage = 'Failed to save contact'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFormChange = (field: keyof ContactEntity, value: any) => {
    setFormData(prevData => ({ ...prevData, [field]: value }))
  }

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = event.target

    if (files && files.length !== 0) {
      // Update file input value
      setFileInput(event.target.value)

      // Store the file for FormData
      setLogoFile(files[0])

      // Create preview without opening cropper
      const reader = new FileReader()

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  return {
    formData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    isSuccessfullySaved,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    contactId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
