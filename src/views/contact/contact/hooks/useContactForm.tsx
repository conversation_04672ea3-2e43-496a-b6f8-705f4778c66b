'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { useForm } from 'react-hook-form'
import type { SubmitHandler } from 'react-hook-form'

import type { ContactEntity } from '@/types/entities'

// Empty contact template for new contacts
export const emptyContact: ContactEntity = {
  type: '',
  logo: '',
  name: '',
  description: '',
  link: '',
  active: false,
  user: undefined
}

export const useContactForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const contactId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && contactId)

  // States
  const [formData, setFormData] = useState<ContactEntity>(emptyContact)
  const [originalData, setOriginalData] = useState<ContactEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)
  const [isSuccessfullySaved, setIsSuccessfullySaved] = useState<boolean>(false)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<ContactEntity>()

  // Fetch contact data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && contactId) {
      const fetchContact = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/contact/${contactId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch contact')
          }

          const data = await response.json()

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)
          reset(data)

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }
        } catch (err) {
          console.error('Error fetching contact:', err)
          setNotification({
            show: true,
            message: 'Failed to load contact data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchContact()
    }
  }, [contactId, isEditMode, isViewMode, reset])

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(file)
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof ContactEntity, value: any) => {
    // Update the form data state
    setFormData(prevData => {
      const newData = { ...prevData, [field]: value }

      return newData
    })
  }

  // Handle form submission
  const onSubmit: SubmitHandler<ContactEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Add all form fields to FormData
      for (const key in data) {
        if (key !== 'logo') {
          formData.append(key, String(data[key as keyof ContactEntity]))
        }
      }

      // Add logo file if selected
      if (logoFile) {
        formData.append('logo', logoFile)
        // @ts-ignore
      } else if (formData.logo) {
        // If we have an existing logo URL and no new file, pass the URL
        // @ts-ignore
        formData.append('logo', formData.logo)
      }

      let url = '/api/contact'
      let method = 'POST'

      if (isEditMode && contactId) {
        url = `/api/contact/${contactId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save contact')
      }

      const resp = await res.json()

      // Mark as successfully saved to hide the form
      setIsSuccessfullySaved(true)

      setNotification({
        show: true,
        message: isEditMode ? 'Contact updated successfully' : 'Contact created successfully',
        type: 'success'
      })

      // Redirect to contact list after successful save
      setTimeout(() => {
        router.push('/contact?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save contact'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    setFormData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    isSuccessfullySaved,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    contactId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
