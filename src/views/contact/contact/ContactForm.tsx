'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import FormHelperText from '@mui/material/FormHelperText'

import SimpleCropper from '@/components/SimpleCropper'
import { emptyContact, useContactForm } from './hooks/useContactForm'

const ContactForm = () => {
  const {
    formData,
    setFormData,
    fileInput,
    imgSrc,
    loading,
    fetchLoading,
    isSuccessfullySaved,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useContactForm()

  // Contact types
  const contactTypes = ['Email', 'Phone', 'Social Media', 'Website', 'Address', 'Other']

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Contact' : 'Add New Contact'} />
        <CardContent>
          <Typography>Loading contact data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Contact' : 'Add New Contact'}
        subheader={isEditMode ? 'Update contact information' : 'Create a new contact'}
      />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Contact Logo' />
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button
                component='label'
                size='small'
                variant='contained'
                htmlFor='contact-logo-upload'
                disabled={isSuccessfullySaved}
              >
                Upload Contact Logo
                <input
                  hidden
                  type='file'
                  value={fileInput}
                  accept='image/png, image/jpeg'
                  onChange={handleFileInputChange}
                  id='contact-logo-upload'
                  disabled={isSuccessfullySaved}
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='primary'
                onClick={handleOpenCropper}
                disabled={!imgSrc || imgSrc === '/images/avatars/default.png' || isSuccessfullySaved}
              >
                Crop Image
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='error'
                onClick={handleFileInputReset}
                disabled={isSuccessfullySaved}
              >
                Reset
              </Button>
            </div>
            <Typography>Allowed JPG, GIF or PNG. Max size of 800K</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Contact Name'
                {...register('name', { required: true })}
                error={Boolean(errors.name)}
                helperText={errors.name ? 'Contact name is required' : ''}
                value={formData.name || ''}
                onChange={e => handleFormChange('name', e.target.value)}
                margin='normal'
                disabled={isSuccessfullySaved}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal' error={Boolean(errors.type)} disabled={isSuccessfullySaved}>
                <InputLabel id='contact-type-label'>Contact Type</InputLabel>
                <Select
                  labelId='contact-type-label'
                  id='contact-type'
                  value={formData.type || ''}
                  label='Contact Type'
                  onChange={e => {
                    handleFormChange('type', e.target.value)
                  }}
                  inputProps={{
                    ...register('type', { required: true })
                  }}
                  disabled={isSuccessfullySaved}
                >
                  {contactTypes.map(type => (
                    <MenuItem key={type} value={type.toLowerCase()}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
                {errors.type && <FormHelperText>Contact type is required</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Link'
                {...register('link')}
                value={formData.link || ''}
                onChange={e => handleFormChange('link', e.target.value)}
                margin='normal'
                disabled={isSuccessfullySaved}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                {...register('description')}
                multiline
                rows={4}
                value={formData.description || ''}
                onChange={e => handleFormChange('description', e.target.value)}
                margin='normal'
                disabled={isSuccessfullySaved}
              />
            </Grid>
          </Grid>

          {!isSuccessfullySaved ? (
            <div className='flex gap-4 flex-wrap mt-6'>
              <Button variant='contained' type='submit' disabled={loading}>
                {loading ? 'Saving...' : isEditMode ? 'Update Contact' : 'Save Contact'}
              </Button>
              <Button
                variant='outlined'
                type='reset'
                color='secondary'
                onClick={() => {
                  reset(emptyContact)
                  setFormData(emptyContact)
                  handleFileInputReset()
                }}
                disabled={loading}
              >
                Reset
              </Button>
              <Button
                variant='outlined'
                color='primary'
                onClick={() => router.push('/contact?tab=all')}
                disabled={loading}
              >
                Cancel
              </Button>
            </div>
          ) : (
            <div className='flex gap-4 flex-wrap mt-6'>
              <div className='flex items-center gap-2'>
                <Typography variant='body1' color='success.main' sx={{ fontWeight: 'medium' }}>
                  ✓ Contact {isEditMode ? 'updated' : 'created'} successfully! Redirecting...
                </Typography>
              </div>
              <Button variant='outlined' color='primary' onClick={() => router.push('/contact?tab=all')}>
                Go to Contact List
              </Button>
            </div>
          )}
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default ContactForm
