'use client'

import React, { useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Switch from '@mui/material/Switch'
import Box from '@mui/material/Box'
import TextField from '@mui/material/TextField'
import MenuItem from '@mui/material/MenuItem'
import Pagination from '@mui/material/Pagination'

// Type Imports
import type { EducationEntity } from '@/types/entities'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

type EducationTableProps = {
  educations: EducationEntity[]
  loading: boolean
  error: string | null
  groupByUser?: boolean
  onDeleteSuccess: (deletedEducationId: string) => void
  onStatusChange?: (id: string, active: boolean) => Promise<void>
  selectedUserId?: string | null

  // Pagination props
  page?: number
  limit?: number
  total?: number
  totalPages?: number
  onPageChange?: (event: React.ChangeEvent<unknown>, newPage: number) => void
  onLimitChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const EducationTable = ({
  educations,
  loading,
  error,
  groupByUser = true,
  onDeleteSuccess,
  onStatusChange,
  selectedUserId,

  // Pagination props
  page = 1,
  limit = 10,
  total = 0,
  totalPages = 0,
  onPageChange,
  onLimitChange
}: EducationTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean
    educationId: string | null
    educationName: string
  }>({
    open: false,
    educationId: null,
    educationName: ''
  })

  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)

  // State for tracking educations being updated
  const [updatingEducations, setUpdatingEducations] = useState<Record<string, boolean>>({})

  // State for local toggle status (for visual feedback when API fails)
  const [localToggleStatus, setLocalToggleStatus] = useState<Record<string, boolean>>({})

  // Filter educations by selected user
  const filteredEducations = React.useMemo(() => {
    if (!educations || educations.length === 0) return []

    if (selectedUserId && selectedUserId !== 'all') {
      return educations.filter(education => education.user?.id === selectedUserId)
    }

    return educations
  }, [educations, selectedUserId])

  // Group educations by user
  const groupedEducations = React.useMemo(() => {
    if (!filteredEducations || filteredEducations.length === 0) return {}

    return filteredEducations.reduce(
      (groups, education) => {
        const userId = education.user?.id || 'unknown'
        const userName = education.user?.name || 'Unknown User'

        if (!groups[userId]) {
          groups[userId] = {
            user: education.user || { id: 'unknown', name: 'Unknown User', email: '', password: '' },
            educations: []
          }
        }

        groups[userId].educations.push(education)

        return groups
      },
      {} as Record<string, { user: any; educations: EducationEntity[] }>
    )
  }, [filteredEducations])

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (educationId: string, educationName: string) => {
    setDeleteDialog({
      open: true,
      educationId,
      educationName
    })
  }

  // Handle closing delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      educationId: null,
      educationName: ''
    })
    setDeleteError(null) // Reset error state when closing dialog
  }

  // Handle confirming deletion
  const handleConfirmDelete = async () => {
    if (!deleteDialog.educationId) return

    const educationIdToDelete = deleteDialog.educationId

    try {
      setDeleteLoading(true)
      setDeleteError(null)

      const response = await fetch(`/api/education/${educationIdToDelete}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to delete education record')
      }

      // Close dialog and notify parent of successful deletion with education ID
      handleCloseDeleteDialog()
      onDeleteSuccess(educationIdToDelete)
    } catch (err) {
      console.error('Error deleting education:', err)
      setDeleteError(err instanceof Error ? err.message : 'An error occurred while deleting the education record')
    } finally {
      setDeleteLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='flex justify-center p-6'>
        <CircularProgress />
      </div>
    )
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: boolean) => {
    if (!onStatusChange) return

    try {
      // Set the education as updating
      setUpdatingEducations(prev => ({ ...prev, [id]: true }))

      // Update local toggle state immediately for visual feedback
      setLocalToggleStatus(prev => ({
        ...prev,
        [id]: !currentStatus
      }))

      // Call the parent handler to update the status
      await onStatusChange(id, !currentStatus)
    } catch (error) {
      console.error('Error updating education status:', error)

      // Keep the local toggle state even if API fails
    } finally {
      // Remove the updating state
      setUpdatingEducations(prev => {
        const newState = { ...prev }

        delete newState[id]

        return newState
      })
    }
  }

  // Get the effective active status (use local state if available, otherwise use education.active)
  const getActiveStatus = (education: EducationEntity) => {
    return localToggleStatus[education.id as string] !== undefined
      ? localToggleStatus[education.id as string]
      : education.active
  }

  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Institution</th>
              <th>Degree</th>
              <th>Duration</th>
              <th>Location</th>
              <th>Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {groupByUser ? (
              // Grouped view
              Object.keys(groupedEducations).length > 0 ? (
                Object.entries(groupedEducations).map(([userId, { user, educations: userEducations }]) => (
                  <React.Fragment key={userId}>
                    {/* User Header Row */}
                    <tr className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-600 border-l-4 border-blue-500'>
                      <td colSpan={6} className='!plb-4 !pli-6'>
                        <div className='flex items-center gap-4'>
                          {user.profileImage ? (
                            <img
                              src={user.profileImage}
                              alt={user.name}
                              width={40}
                              height={40}
                              className='rounded-full object-cover ring-2 ring-blue-200 dark:ring-purple-600'
                            />
                          ) : (
                            <div
                              className='flex items-center justify-center bg-blue-100 text-blue-600 rounded-full ring-2 ring-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:ring-blue-600'
                              style={{ width: 40, height: 40, fontSize: '16px', fontWeight: 600 }}
                            >
                              {user.name?.charAt(0) || 'U'}
                            </div>
                          )}
                          <div className='flex-1'>
                            <Typography variant='h6' className='font-bold text-gray-800 dark:text-gray-100'>
                              {user.name || 'Unknown User'}
                            </Typography>
                            <Typography variant='body2' className='text-blue-600 dark:text-blue-400 font-medium'>
                              {userEducations.length} education record{userEducations.length !== 1 ? 's' : ''}
                            </Typography>
                          </div>
                          <div className='text-right'>
                            <Typography variant='caption' className='text-gray-500 dark:text-gray-400'>
                              {user.email}
                            </Typography>
                          </div>
                        </div>
                      </td>
                    </tr>
                    {/* Education Records for this User */}
                    {userEducations.map((education, index) => (
                      <tr
                        key={education.id}
                        className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      >
                        <td className='!plb-1'>
                          <div className='flex items-center gap-3 ml-8'>
                            {education.logo && education.logo.length > 0 ? (
                              <img
                                src={education.logo}
                                alt={education.name}
                                width={32}
                                height={32}
                                className='rounded-lg object-cover shadow-sm border border-gray-200'
                              />
                            ) : (
                              <div
                                className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                                style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                              >
                                {education.name.charAt(0)}
                              </div>
                            )}
                            <div>
                              <Typography className=' text-gray-900'>{education.name}</Typography>
                            </div>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <Typography className='font-medium text-gray-800'>{education.degree}</Typography>
                        </td>
                        <td className='!plb-1'>
                          <div className='bg-blue-100 px-2 py-1 rounded-md inline-block'>
                            <Typography variant='body2' className='font-medium text-blue-700'>
                              {new Date(education.startedAt).getFullYear()} -{' '}
                              {new Date(education.endedAt).getFullYear()}
                            </Typography>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <div className='flex items-center gap-2'>
                            <i className='ri-map-pin-line text-gray-500'></i>
                            <Typography className='text-gray-700'>
                              {education.location || 'No location specified'}
                            </Typography>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          {onStatusChange ? (
                            <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                              <Tooltip title={getActiveStatus(education) ? 'Deactivate' : 'Activate'}>
                                <span>
                                  <Switch
                                    checked={getActiveStatus(education)}
                                    onChange={() =>
                                      // @ts-ignore
                                      handleStatusToggle(education.id as string, getActiveStatus(education))
                                    }
                                    disabled={updatingEducations[education.id as string]}
                                    color='primary'
                                    size='medium'
                                  />
                                </span>
                              </Tooltip>
                              <div
                                style={{
                                  width: '24px',
                                  display: 'inline-flex',
                                  justifyContent: 'center',
                                  marginLeft: '8px'
                                }}
                              >
                                {updatingEducations[education.id as string] && <CircularProgress size={18} />}
                              </div>
                            </div>
                          ) : (
                            <div className='flex items-center justify-center'>
                              <div
                                className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(education) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                              >
                                {getActiveStatus(education) ? 'Active' : 'Inactive'}
                              </div>
                            </div>
                          )}
                        </td>
                        <td className='!plb-1'>
                          <div className='flex gap-1 justify-center'>
                            <Tooltip title='View'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/education?tab=education&id=${education.id}&mode=view`}
                                className='hover:bg-blue-50 dark:hover:bg-blue-900'
                              >
                                <i className='ri-eye-line text-blue-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Edit'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/education?tab=education&id=${education.id}&mode=edit`}
                                className='hover:bg-orange-50 dark:hover:bg-orange-900'
                              >
                                <i className='ri-pencil-line text-orange-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Delete'>
                              <IconButton
                                size='small'
                                onClick={() => handleDeleteClick(education.id as string, education.name)}
                                className='hover:bg-red-50 dark:hover:bg-red-900'
                              >
                                <i className='ri-delete-bin-line text-red-600'></i>
                              </IconButton>
                            </Tooltip>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className='text-center py-12'>
                    <div className='flex flex-col items-center gap-3'>
                      <div className='w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center'>
                        <i className='ri-graduation-cap-line text-2xl text-gray-400'></i>
                      </div>
                      <Typography variant='h6' className='text-gray-500 dark:text-gray-400'>
                        No education records found
                      </Typography>
                      <Typography variant='body2' className='text-gray-400 dark:text-gray-500'>
                        {selectedUserId && selectedUserId !== 'all'
                          ? 'No education records found for the selected user'
                          : 'Start by adding your first education record'}
                      </Typography>
                    </div>
                  </td>
                </tr>
              )
            ) : // List view (ungrouped)
            filteredEducations && filteredEducations.length > 0 ? (
              filteredEducations.map((education, index) => (
                <tr
                  key={education.id}
                  className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      {education.logo && education.logo.length > 0 ? (
                        <img
                          src={education.logo}
                          alt={education.name}
                          width={32}
                          height={32}
                          className='rounded-lg object-cover shadow-sm border border-gray-200'
                        />
                      ) : (
                        <div
                          className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                          style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                        >
                          {education.name.charAt(0)}
                        </div>
                      )}
                      <div>
                        <Typography className=' text-gray-900'>{education.name}</Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <Typography className=' text-gray-800'>{education.degree}</Typography>
                  </td>
                  <td className='!plb-1'>
                    <div className='bg-blue-100 px-2 py-1 rounded-md inline-block'>
                      <Typography variant='body2' className='font-medium text-blue-700'>
                        {new Date(education.startedAt).getFullYear()} - {new Date(education.endedAt).getFullYear()}
                      </Typography>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div className='flex items-center gap-2'>
                      <i className='ri-map-pin-line text-gray-500'></i>
                      <Typography className='text-gray-700'>{education.location || 'No location specified'}</Typography>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    {onStatusChange ? (
                      <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                        <Tooltip title={getActiveStatus(education) ? 'Deactivate' : 'Activate'}>
                          <span>
                            <Switch
                              checked={getActiveStatus(education)}
                              // @ts-ignore
                              onChange={() => handleStatusToggle(education.id as string, getActiveStatus(education))}
                              disabled={updatingEducations[education.id as string]}
                              color='primary'
                              size='medium'
                            />
                          </span>
                        </Tooltip>
                        <div
                          style={{ width: '24px', display: 'inline-flex', justifyContent: 'center', marginLeft: '8px' }}
                        >
                          {updatingEducations[education.id as string] && <CircularProgress size={18} />}
                        </div>
                      </div>
                    ) : (
                      <div className='flex items-center justify-center'>
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(education) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                        >
                          {getActiveStatus(education) ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                    )}
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-1 justify-center'>
                      <Tooltip title='View'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/education?tab=education&id=${education.id}&mode=view`}
                          className='hover:bg-blue-50 dark:hover:bg-blue-900'
                        >
                          <i className='ri-eye-line text-blue-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/education?tab=education&id=${education.id}&mode=edit`}
                          className='hover:bg-orange-50 dark:hover:bg-orange-900'
                        >
                          <i className='ri-pencil-line text-orange-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          onClick={() => handleDeleteClick(education.id as string, education.name)}
                          className='hover:bg-red-50 dark:hover:bg-red-900'
                        >
                          <i className='ri-delete-bin-line text-red-600'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className='text-center py-12'>
                  <div className='flex flex-col items-center gap-3'>
                    <div className='w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center'>
                      <i className='ri-graduation-cap-line text-2xl text-gray-400'></i>
                    </div>
                    <Typography variant='h6' className='text-gray-500 dark:text-gray-400'>
                      No education records found
                    </Typography>
                    <Typography variant='body2' className='text-gray-400 dark:text-gray-500'>
                      {selectedUserId && selectedUserId !== 'all'
                        ? 'No education records found for the selected user'
                        : 'Start by adding your first education record'}
                    </Typography>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {total > 0 && totalPages > 0 && onPageChange && onLimitChange && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3, px: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant='body2' color='text.secondary'>
              Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} education records
            </Typography>
            <TextField
              select
              size='small'
              label='Per page'
              value={limit}
              onChange={onLimitChange}
              disabled={loading}
              sx={{ minWidth: 100 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </TextField>
          </Box>
          {/*// @ts-ignore*/}
          <Pagination
            count={totalPages}
            page={page}
            onChange={onPageChange}
            disabled={loading}
            color='primary'
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
      >
        <DialogTitle id='delete-dialog-title'>Delete Education Record</DialogTitle>
        <DialogContent>
          <DialogContentText id='delete-dialog-description'>
            Are you sure you want to delete the education record for <strong>{deleteDialog.educationName}</strong>? This
            action cannot be undone.
          </DialogContentText>
          {deleteError && (
            <Typography color='error' className='mt-4'>
              {deleteError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color='error'
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : null}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default EducationTable
