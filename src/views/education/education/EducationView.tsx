'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'
import Chip from '@mui/material/Chip'

import { useEducationForm } from './hooks/useEducationForm'

const EducationView = () => {
  const { formData, imgSrc, fetchLoading, notification, educationId, setNotification, router } = useEducationForm()

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Education Details' />
        <CardContent>
          <Typography>Loading education data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Education Details' />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Institution Logo' />
          <div>
            <Typography variant='h6'>{formData.name}</Typography>
            <Typography variant='body1'>{formData.degree}</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Institution Name
            </Typography>
            <Typography variant='body1'>{formData.name}</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Degree
            </Typography>
            <Typography variant='body1'>{formData.degree}</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Start Date
            </Typography>
            <Typography variant='body1'>
              {formData.startedAt instanceof Date
                ? formData.startedAt.toLocaleDateString()
                : new Date(formData.startedAt).toLocaleDateString()}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              End Date
            </Typography>
            <Typography variant='body1'>
              {formData.endedAt instanceof Date
                ? formData.endedAt.toLocaleDateString()
                : new Date(formData.endedAt).toLocaleDateString()}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Website Link
            </Typography>
            {formData.link ? (
              <Typography variant='body1'>
                <a href={formData.link} target='_blank' rel='noopener noreferrer' className='text-primary'>
                  {formData.link}
                </a>
              </Typography>
            ) : (
              <Typography variant='body1'>No website link provided</Typography>
            )}
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Location
            </Typography>
            <div className='flex flex-wrap gap-2 mt-2'>
              {formData.location && formData.location.trim() !== '' ? (
                // @ts-ignore
                <Chip label={formData.location} color='primary' variant='outlined' />
              ) : (
                <Typography variant='body1'>No location specified</Typography>
              )}
            </div>
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/education?tab=education&id=${educationId}&mode=edit`)}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/education?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default EducationView
