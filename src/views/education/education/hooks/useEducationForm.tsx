'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { type SubmitHandler, useForm } from 'react-hook-form'

import type { EducationEntity } from '@/types/entities'

// Empty education template for new education records
export const emptyEducation: EducationEntity = {
  name: '',
  degree: '',
  logo: '',
  location: '',
  startedAt: new Date(),
  endedAt: new Date(),
  link: ''
}

export const useEducationForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const educationId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && educationId)

  // States
  const [formData, setFormData] = useState<EducationEntity>(emptyEducation)
  const [originalData, setOriginalData] = useState<EducationEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    setValue,
    formState: { errors }
  } = useForm<EducationEntity>({
    defaultValues: emptyEducation
  })

  // Fetch education data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && educationId) {
      const fetchEducation = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/education/${educationId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch education')
          }

          const data = await response.json()

          // // Format dates for form inputs
          // if (data.startedAt) {
          //   const startDate = new Date(data.startedAt)
          //   data.startedAt = startDate.toISOString().split('T')[0]
          // }
          //
          // if (data.endedAt) {
          //   const endDate = new Date(data.endedAt)
          //   data.endedAt = endDate.toISOString().split('T')[0]
          // }

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)

          // Reset form with the fetched data
          reset(data)

          // Also set individual form values to ensure they're properly registered
          if (data.name) setValue('name', data.name)
          if (data.degree) setValue('degree', data.degree)
          if (data.startedAt) setValue('startedAt', data.startedAt)
          if (data.endedAt) setValue('endedAt', data.endedAt)
          if (data.link) setValue('link', data.link)

          // Set image if available
          if (data.logo && data.logo.length > 0) {
            setImgSrc(data.logo[0])
          }
        } catch (err) {
          console.error('Error fetching education:', err)
          setNotification({
            show: true,
            message: 'Failed to load education data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchEducation()
    }
  }, [educationId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(file)
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof EducationEntity, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Handle form submission
  const onSubmit: SubmitHandler<EducationEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formDataObj = new FormData()

      // Merge the React Hook Form data with our local state
      // This ensures we have all the data from both sources
      const currentData = {
        ...formData,
        ...data
      }

      // Process form data
      // @ts-ignore
      currentData.startedAt = new Date(currentData.startedAt).toISOString()
      // @ts-ignore
      currentData.endedAt = new Date(currentData.endedAt).toISOString()

      // Add all form fields to FormData
      for (const key in currentData) {
        if (key === 'logo' && Array.isArray(currentData.logo)) {
          // Handle logo array
          const arr = currentData.logo as any[]

          arr.forEach((item, index) => {
            formDataObj.append(`logo[${index}]`, item)
          })
        } else if (key !== 'logo') {
          // Handle all other fields
          formDataObj.append(key, String(currentData[key as keyof EducationEntity]))
        }
      }

      // Add logo file if selected
      if (logoFile) {
        formDataObj.append('logo', logoFile)
      }

      let url = '/api/education'
      let method = 'POST'

      if (isEditMode && educationId) {
        url = `/api/education/${educationId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formDataObj
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save education')
      }

      const resp = await res.json()

      setNotification({
        show: true,
        message: isEditMode ? 'Education updated successfully' : 'Education created successfully',
        type: 'success'
      })

      // Redirect to education list after successful save
      setTimeout(() => {
        router.push('/education?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save education'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    educationId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setValue,
    setNotification,
    reset,
    router
  }
}
