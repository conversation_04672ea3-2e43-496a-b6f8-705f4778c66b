'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import SimpleCropper from '@/components/SimpleCropper'
import { useEducationForm } from './hooks/useEducationForm'

const EducationForm = () => {
  const {
    formData,
    fileInput,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useEducationForm()

  // No need for location input state as we're using a direct TextField

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Education' : 'Add New Education'} />
        <CardContent>
          <Typography>Loading education data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Education' : 'Add New Education'}
        subheader={isEditMode ? 'Update education information' : 'Create a new education record'}
      />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Institution Logo' />
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button component='label' size='small' variant='contained' htmlFor='education-logo-upload'>
                Upload Institution Logo
                <input
                  hidden
                  type='file'
                  value={fileInput}
                  accept='image/png, image/jpeg'
                  onChange={handleFileInputChange}
                  id='education-logo-upload'
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='primary'
                onClick={handleOpenCropper}
                disabled={!imgSrc || imgSrc === '/images/avatars/default.png'}
              >
                Crop Image
              </Button>
              <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                Reset
              </Button>
            </div>
            <Typography>Allowed JPG, GIF or PNG. Max size of 800K</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <form
          onSubmit={handleSubmit(data => {
            // Make sure form data is synchronized with the state
            const updatedData = {
              ...data,
              // @ts-ignore
              startedAt: data.startedAt ? new Date(data.startedAt).toISOString() : null,
              // @ts-ignore
              endedAt: data.endedAt ? new Date(data.endedAt).toISOString() : null,
              ...formData
            }

            onSubmit(updatedData)
          })}
        >
          <Grid container spacing={5}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Institution Name'
                {...register('name', { required: true })}
                error={Boolean(errors.name)}
                helperText={errors.name ? 'Institution name is required' : ''}
                defaultValue={formData.name || ''}
                onChange={e => {
                  handleFormChange('name', e.target.value)
                }}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Degree'
                {...register('degree', { required: true })}
                error={Boolean(errors.degree)}
                helperText={errors.degree ? 'Degree is required' : ''}
                defaultValue={formData.degree || ''}
                onChange={e => {
                  handleFormChange('degree', e.target.value)
                }}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Start Date'
                type='date'
                {...register('startedAt', { required: true })}
                error={Boolean(errors.startedAt)}
                helperText={errors.startedAt ? 'Start date is required' : ''}
                defaultValue={
                  formData.startedAt instanceof Date
                    ? formData.startedAt.toISOString().split('T')[0]
                    : formData.startedAt
                }
                onChange={e => {
                  handleFormChange('startedAt', e.target.value)
                }}
                margin='normal'
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='End Date'
                type='date'
                {...register('endedAt', { required: true })}
                error={Boolean(errors.endedAt)}
                helperText={errors.endedAt ? 'End date is required' : ''}
                defaultValue={
                  formData.endedAt instanceof Date ? formData.endedAt.toISOString().split('T')[0] : formData.endedAt
                }
                onChange={e => {
                  handleFormChange('endedAt', e.target.value)
                }}
                margin='normal'
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Website Link'
                {...register('link')}
                defaultValue={formData.link || ''}
                onChange={e => {
                  handleFormChange('link', e.target.value)
                }}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Location'
                {...register('location')}
                defaultValue={formData.location || ''}
                onChange={e => {
                  handleFormChange('location', e.target.value)
                }}
                margin='normal'
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update Education' : 'Save Education'}
            </Button>
            <Button
              variant='outlined'
              type='reset'
              color='secondary'
              onClick={() => reset(formData)}
              disabled={loading}
            >
              Reset
            </Button>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => router.push('/education?tab=all')}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default EducationForm
