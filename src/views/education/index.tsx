'use client'

import type { ReactElement, SyntheticEvent } from 'react'

// React Imports
import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Grid from '@mui/material/Grid'
import Tab from '@mui/material/Tab'
import TabContext from '@mui/lab/TabContext'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'

const Education = ({ tabContentList }: { tabContentList: { [key: string]: ReactElement } }) => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const tabParam = searchParams.get('tab')

  // States
  const [activeTab, setActiveTab] = useState(tabParam || 'all')

  // Update URL when tab changes
  const handleChange = (event: SyntheticEvent, value: string) => {
    setActiveTab(value)
    router.push(`/education?tab=${value}`)
  }

  // Update active tab if URL parameter changes
  useEffect(() => {
    if (tabParam && tabContentList[tabParam]) {
      setActiveTab(tabParam)
    }
  }, [tabParam, tabContentList])

  return (
    <TabContext value={activeTab}>
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <TabList onChange={handleChange} variant='scrollable'>
            <Tab
              label='Create Education'
              icon={<i className='ri-graduation-cap-line' />}
              iconPosition='start'
              value='education'
            />
            <Tab label='All' icon={<i className='ri-notification-3-line' />} iconPosition='start' value='all' />
          </TabList>
        </Grid>
        <Grid item xs={12}>
          <TabPanel value={activeTab} className='p-0'>
            {tabContentList[activeTab]}
          </TabPanel>
        </Grid>
      </Grid>
    </TabContext>
  )
}

export default Education
