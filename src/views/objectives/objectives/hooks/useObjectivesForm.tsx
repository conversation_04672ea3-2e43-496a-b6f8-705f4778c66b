'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { type SubmitHandler, useForm } from 'react-hook-form'

import type { ObjectiveEntity } from '@/types/entities'

// Empty objective template for new objectives
export const emptyObjective: ObjectiveEntity = {
  tag: '',
  description: '',
  logo: '',
  cover: [],
  active: false,
  user: undefined
}

export const useObjectivesForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const objectiveId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && objectiveId)

  // States
  const [formData, setFormData] = useState<ObjectiveEntity>(emptyObjective)
  const [originalData, setOriginalData] = useState<ObjectiveEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<ObjectiveEntity>()

  // Fetch objective data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && objectiveId) {
      const fetchObjective = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/objective/${objectiveId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch objective')
          }

          const data = await response.json()

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)
          reset(data)

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }
        } catch (err) {
          console.error('Error fetching objective:', err)
          setNotification({
            show: true,
            message: 'Failed to load objective data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchObjective()
    }
  }, [objectiveId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(file)
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof ObjectiveEntity, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Handle form submission
  const onSubmit: SubmitHandler<ObjectiveEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Add required backend fields to FormData
      // Only send fields that are in the backend schema
      formData.append('tag', data.tag || '')
      formData.append('description', data.description || '')

      // Store title in tag if tag is empty but title is provided
      if (!data.tag && data.title) {
        formData.append('tag', data.title)
      }

      // Handle cover array - ensure it's properly formatted as JSON string
      // Backend expects cover to be an array of strings
      // Create an empty array for cover
      const coverArray: string[] = []

      formData.append('cover', JSON.stringify(coverArray))

      // Add logo file if selected
      if (logoFile) {
        formData.append('logo', logoFile)
      }

      let url = '/api/objective'
      let method = 'POST'

      if (isEditMode && objectiveId) {
        url = `/api/objective/${objectiveId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save objective')
      }

      const resp = await res.json()

      setNotification({
        show: true,
        message: isEditMode ? 'Objective updated successfully' : 'Objective created successfully',
        type: 'success'
      })

      // Redirect to objective list after successful save
      setTimeout(() => {
        router.push('/objectives?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save objective'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    objectiveId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
