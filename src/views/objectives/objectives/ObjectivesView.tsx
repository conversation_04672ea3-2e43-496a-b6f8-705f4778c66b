'use client'


// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

import { useObjectivesForm } from './hooks/useObjectivesForm'

const ObjectivesView = () => {
  const { formData, fetchLoading, notification, objectiveId, setNotification, router } = useObjectivesForm()

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Objective Details' />
        <CardContent>
          <Typography>Loading objective data...</Typography>
        </CardContent>
      </Card>
    )
  }

  // Function to get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'error'
      case 'Medium':
        return 'warning'
      case 'Low':
        return 'success'
      default:
        return 'primary'
    }
  }

  return (
    <Card>
      <CardHeader title='Objective Details' />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Tag
            </Typography>
            <Typography variant='body1'>{formData.tag}</Typography>
          </Grid>

          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Description
            </Typography>
            <Typography variant='body1'>{formData.description || 'No description provided'}</Typography>
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/objectives?tab=objectives&id=${objectiveId}&mode=edit`)}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/objectives?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default ObjectivesView
