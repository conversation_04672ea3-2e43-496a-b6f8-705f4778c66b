'use client'

// React Imports
import { useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
// @ts-ignore
import Chip from '@mui/material/Chip'
import Switch from '@mui/material/Switch'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Button from '@mui/material/Button'
import DialogContentText from '@mui/material/DialogContentText'
import Avatar from '@mui/material/Avatar'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

// Type Imports
import type { UserRoleWithDetails } from '@/types/user-role'

interface UserRolesTableProps {
  userRoles: UserRoleWithDetails[]
  loading: boolean
  error: string | null
  onDeleteSuccess: (userRoleId: string) => void
  onStatusChange: (userRoleId: string, newStatus: boolean) => void
}

const UserRolesTable = ({ userRoles, loading, error, onDeleteSuccess, onStatusChange }: UserRolesTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean
    userRoleId: string | null
    userName: string
    roleName: string
  }>({
    open: false,
    userRoleId: null,
    userName: '',
    roleName: ''
  })

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (userRoleId: string | undefined, userName: string, roleName: string) => {
    setDeleteDialog({
      open: true,
      // @ts-ignore
      userRoleId,
      userName,
      roleName
    })
  }

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!deleteDialog.userRoleId) return

    try {
      const response = await fetch(`/api/user-roles/${deleteDialog.userRoleId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete user role assignment')
      }

      onDeleteSuccess(deleteDialog.userRoleId)
    } catch (error) {
      console.error('Error deleting user role assignment:', error)
    } finally {
      setDeleteDialog({ open: false, userRoleId: null, userName: '', roleName: '' })
    }
  }

  // Handle status toggle
  const handleStatusToggle = async (userRoleId: string | undefined, currentStatus: boolean) => {
    if (!userRoleId) return

    try {
      const response = await fetch(`/api/user-roles/${userRoleId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !currentStatus })
      })

      if (!response.ok) {
        throw new Error('Failed to update user role status')
      }

      onStatusChange(userRoleId, !currentStatus)
    } catch (error) {
      console.error('Error updating user role status:', error)
    }
  }

  // Get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'admin':
        return 'error'
      case 'moderator':
        return 'warning'
      case 'user':
        return 'primary'
      case 'viewer':
        return 'secondary'
      default:
        return 'default'
    }
  }

  // Format date
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'N/A'

    return new Date(date).toLocaleDateString()
  }

  if (loading) {
    return <Typography>Loading user role assignments...</Typography>
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  // @ts-ignore
  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>User</th>
              <th>Role</th>
              <th>Level</th>
              <th>Status</th>
              <th>Default</th>
              <th>Assigned Date</th>
              <th>Expires</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {userRoles && userRoles.length > 0 ? (
              userRoles.map(userRole => (
                <tr key={userRole.id}>
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      <Avatar
                        src={userRole.userDetails?.profileImage || '/images/avatars/1.png'}
                        alt={userRole.userDetails?.name || 'Unknown User'}
                        sx={{ width: 40, height: 40 }}
                      />
                      <div className='flex flex-col'>
                        <Typography className='font-medium' color='text.primary'>
                          {userRole.userDetails?.name || 'Unknown User'}
                        </Typography>
                        <Typography variant='body2' color='text.secondary'>
                          {userRole.userDetails?.email || userRole.user}
                        </Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <Typography className='font-medium'>{userRole.roleDetails?.name || 'Unknown Role'}</Typography>
                  </td>
                  <td className='!plb-1'>
                    {/*@ts-ignore*/}
                    <Chip
                      label={
                        userRole.roleDetails?.level
                          ? userRole.roleDetails.level.charAt(0).toUpperCase() + userRole.roleDetails.level.slice(1)
                          : 'Unknown'
                      }
                      color={getLevelColor(userRole.roleDetails?.level || '') as any}
                      variant='outlined'
                      size='small'
                    />
                  </td>
                  <td className='!plb-1'>
                    <Switch
                      checked={userRole.isActive}
                      onChange={() => handleStatusToggle(userRole.id, userRole.isActive)}
                      size='small'
                    />
                  </td>
                  <td className='!plb-1'>
                    {/*@ts-ignore*/}
                    {userRole.isDefault && <Chip label='Default' color='info' variant='outlined' size='small' />}
                  </td>
                  <td className='!plb-1'>
                    <Typography variant='body2'>{formatDate(userRole.assignedAt)}</Typography>
                  </td>
                  <td className='!plb-1'>
                    <Typography variant='body2'>
                      {userRole.expiresAt ? formatDate(userRole.expiresAt) : 'Never'}
                    </Typography>
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-2'>
                      <Tooltip title='View'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/user-roles?tab=user-role&id=${userRole.id}&mode=view`}
                        >
                          <i className='ri-eye-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/user-roles?tab=user-role&id=${userRole.id}&mode=edit`}
                        >
                          <i className='ri-pencil-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          color='error'
                          onClick={() =>
                            handleDeleteClick(
                              userRole.id,
                              userRole.userDetails?.name || 'Unknown User',
                              userRole.roleDetails?.name || 'Unknown Role'
                            )
                          }
                        >
                          <i className='ri-delete-bin-line'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className='text-center py-4'>
                  <Typography>No user role assignments found</Typography>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, userRoleId: null, userName: '', roleName: '' })}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to remove the role "{deleteDialog.roleName}" from user "{deleteDialog.userName}"? This
            action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, userRoleId: null, userName: '', roleName: '' })}>
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color='error' variant='contained'>
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default UserRolesTable
