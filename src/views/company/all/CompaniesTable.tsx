'use client'

import React, { useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import Button from '@mui/material/Button'
import Switch from '@mui/material/Switch'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'
import TextField from '@mui/material/TextField'
import MenuItem from '@mui/material/MenuItem'
import Pagination from '@mui/material/Pagination'

// Type Imports
import type { CompanyEntity } from '@/types/entities'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

type CompaniesTableProps = {
  companies: CompanyEntity[]
  loading: boolean
  error: string | null
  groupByUser?: boolean
  selectedUserId?: string | null
  onDeleteSuccess: (deletedCompanyId: string) => void
  onStatusChange?: (id: string, active: boolean) => Promise<void>

  // Pagination props
  page?: number
  limit?: number
  total?: number
  totalPages?: number
  onPageChange?: (event: React.ChangeEvent<unknown>, newPage: number) => void
  onLimitChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const CompaniesTable = ({
  companies,
  loading,
  error,
  groupByUser = true,
  selectedUserId = null,
  onDeleteSuccess,
  onStatusChange,

  // Pagination props
  page = 1,
  limit = 10,
  total = 0,
  totalPages = 0,
  onPageChange,
  onLimitChange
}: CompaniesTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; companyId: string | null; companyName: string }>({
    open: false,
    companyId: null,
    companyName: ''
  })

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (companyId: string, companyName: string) => {
    setDeleteDialog({
      open: true,
      companyId,
      companyName: companyName
    })
  }

  // Handle closing delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      companyId: null,
      companyName: ''
    })
    setDeleteError(null) // Reset error state when closing dialog
  }

  // State for error notification
  const [deleteError, setDeleteError] = useState<string | null>(null)

  // State for tracking companies being updated
  const [updatingCompanies, setUpdatingCompanies] = useState<Record<string, boolean>>({})

  // State for local toggle status (for visual feedback when API fails)
  const [localToggleStatus, setLocalToggleStatus] = useState<Record<string, boolean>>({})

  // Filter companies by selected user
  const filteredCompanies = React.useMemo(() => {
    if (!companies || companies.length === 0) return []

    if (selectedUserId && selectedUserId !== 'all') {
      return companies.filter(company => company.user?.id === selectedUserId)
    }

    return companies
  }, [companies, selectedUserId])

  // Group companies by user
  const groupedCompanies = React.useMemo(() => {
    if (!filteredCompanies || filteredCompanies.length === 0) return {}

    return filteredCompanies.reduce(
      (groups, company) => {
        const userId = company.user?.id || 'unknown'
        const userName = company.user?.name || 'Unknown User'

        if (!groups[userId]) {
          groups[userId] = {
            user: company.user || { id: 'unknown', name: 'Unknown User', email: '', password: '' },
            companies: []
          }
        }

        groups[userId].companies.push(company)

        return groups
      },
      {} as Record<string, { user: any; companies: CompanyEntity[] }>
    )
  }, [filteredCompanies])

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: boolean) => {
    if (!onStatusChange) return

    try {
      // Set the company as updating
      setUpdatingCompanies(prev => ({ ...prev, [id]: true }))

      // Update local toggle state immediately for visual feedback
      setLocalToggleStatus(prev => ({
        ...prev,
        [id]: !currentStatus
      }))

      // Call the parent handler to update the status
      await onStatusChange(id, currentStatus)
    } catch (error) {
      console.error('Error updating company status:', error)
    } finally {
      setUpdatingCompanies(prev => {
        const newState = { ...prev }

        delete newState[id]

        return newState
      })
    }
  }

  // Handle confirming delete action
  const handleConfirmDelete = async () => {
    if (!deleteDialog.companyId) return

    const companyIdToDelete = deleteDialog.companyId

    setDeleteError(null) // Reset error state

    try {
      const response = await fetch(`/api/company/${companyIdToDelete}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to delete company')
      }

      // Notify parent component of successful deletion with company ID
      onDeleteSuccess(companyIdToDelete)
      handleCloseDeleteDialog()
    } catch (err) {
      console.error('Error deleting company:', err)

      // Extract error message
      let errorMessage = 'Failed to delete company'

      if (err instanceof Error) {
        errorMessage = err.message
      }

      // Set error message to display in dialog
      setDeleteError(errorMessage)
    }
  }

  if (loading) {
    return <Typography>Loading companies...</Typography>
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  // Get the effective active status (use local state if available, otherwise use company.active)
  const getActiveStatus = (company: CompanyEntity) => {
    return localToggleStatus[company.id as string] !== undefined
      ? localToggleStatus[company.id as string]
      : company.active
  }

  // @ts-ignore
  // @ts-ignore
  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Company</th>
              <th>Designation</th>
              <th>Duration</th>
              <th>Status</th>
              <th>Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {groupByUser ? (
              // Grouped view
              Object.keys(groupedCompanies).length > 0 ? (
                Object.entries(groupedCompanies).map(([userId, { user, companies: userCompanies }]) => (
                  <React.Fragment key={userId}>
                    {/* User Header Row */}
                    <tr className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-l-4 border-blue-500'>
                      <td colSpan={6} className='!plb-4 !pli-6'>
                        <div className='flex items-center gap-4'>
                          {user.profileImage ? (
                            <img
                              src={user.profileImage}
                              alt={user.name}
                              width={40}
                              height={40}
                              className='rounded-full object-cover ring-2 ring-blue-200 dark:ring-blue-600'
                            />
                          ) : (
                            <div
                              className='flex items-center justify-center bg-blue-100 text-blue-600 rounded-full ring-2 ring-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:ring-blue-600'
                              style={{ width: 40, height: 40, fontSize: '16px', fontWeight: 600 }}
                            >
                              {user.name?.charAt(0) || 'U'}
                            </div>
                          )}
                          <div className='flex-1'>
                            <Typography variant='h6' className='font-bold text-gray-800 dark:text-gray-100'>
                              {user.name || 'Unknown User'}
                            </Typography>
                            <Typography variant='body2' className='text-blue-600 dark:text-blue-400 font-medium'>
                              {userCompanies.length} company record{userCompanies.length !== 1 ? 's' : ''}
                            </Typography>
                          </div>
                          <div className='text-right'>
                            <Typography variant='caption' className='text-gray-500 dark:text-gray-400'>
                              {user.email}
                            </Typography>
                          </div>
                        </div>
                      </td>
                    </tr>
                    {/* Company Records for this User */}
                    {userCompanies.map((company, index) => (
                      <tr
                        key={company.id}
                        className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      >
                        <td className='!plb-1'>
                          <div className='flex items-center gap-3 ml-8'>
                            {company.logo ? (
                              <img
                                src={company.logo}
                                alt={company.name}
                                width={32}
                                height={32}
                                className='rounded-lg object-cover shadow-sm border border-gray-200'
                              />
                            ) : (
                              <div
                                className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                                style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                              >
                                {company.name.charAt(0)}
                              </div>
                            )}
                            <div>
                              <Typography className='font-semibold text-gray-900'>{company.name}</Typography>
                              <Typography variant='caption' className='text-gray-600'>
                                {company.companyWebsite ? (
                                  <a
                                    href={company.companyWebsite}
                                    target='_blank'
                                    rel='noopener noreferrer'
                                    className='text-blue-600 hover:text-blue-800'
                                  >
                                    {company.companyWebsite}
                                  </a>
                                ) : (
                                  'No website'
                                )}
                              </Typography>
                            </div>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <Typography className='font-medium text-gray-800'>{company.designation}</Typography>
                        </td>
                        <td className='!plb-1'>
                          <div className='bg-blue-100 px-2 py-1 rounded-md inline-block'>
                            <Typography variant='body2' className='font-medium text-blue-700'>
                              {new Date(company.startedAt).toLocaleDateString()} -{' '}
                              {company.endedAt ? new Date(company.endedAt).toLocaleDateString() : 'Present'}
                            </Typography>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <div
                            className={`px-3 py-1 rounded-full text-xs font-medium inline-block ${company.isCurrent ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`}
                          >
                            {company.isCurrent ? 'Current' : 'Previous'}
                          </div>
                        </td>
                        <td className='!plb-1'>
                          {onStatusChange ? (
                            <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                              <Tooltip title={getActiveStatus(company) ? 'Deactivate' : 'Activate'}>
                                <span>
                                  <Switch
                                    checked={getActiveStatus(company)}
                                    onChange={() => handleStatusToggle(company.id as string, getActiveStatus(company))}
                                    disabled={updatingCompanies[company.id as string]}
                                    color='primary'
                                    size='medium'
                                  />
                                </span>
                              </Tooltip>
                              <div
                                style={{
                                  width: '24px',
                                  display: 'inline-flex',
                                  justifyContent: 'center',
                                  marginLeft: '8px'
                                }}
                              >
                                {updatingCompanies[company.id as string] && <CircularProgress size={18} />}
                              </div>
                            </div>
                          ) : (
                            <div className='flex items-center justify-center'>
                              <div
                                className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(company) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                              >
                                {getActiveStatus(company) ? 'Active' : 'Inactive'}
                              </div>
                            </div>
                          )}
                        </td>
                        <td className='!plb-1'>
                          <div className='flex gap-1 justify-center'>
                            <Tooltip title='View'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/company?tab=company&id=${company.id}&mode=view`}
                                className='hover:bg-blue-50'
                              >
                                <i className='ri-eye-line text-blue-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Edit'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/company?tab=company&id=${company.id}&mode=edit`}
                                className='hover:bg-orange-50'
                              >
                                <i className='ri-pencil-line text-orange-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Delete'>
                              {/*@ts-ignore*/}
                              <IconButton
                                size='small'
                                onClick={() => handleDeleteClick(company.id, company.name)}
                                className='hover:bg-red-50'
                              >
                                <i className='ri-delete-bin-line text-red-600'></i>
                              </IconButton>
                            </Tooltip>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className='text-center py-12'>
                    <div className='flex flex-col items-center gap-3'>
                      <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
                        <i className='ri-building-line text-2xl text-gray-400'></i>
                      </div>
                      <Typography variant='h6' className='text-gray-500'>
                        No company records found
                      </Typography>
                      <Typography variant='body2' className='text-gray-400'>
                        {selectedUserId && selectedUserId !== 'all'
                          ? 'No company records found for the selected user'
                          : 'Start by adding your first company record'}
                      </Typography>
                    </div>
                  </td>
                </tr>
              )
            ) : // List view (ungrouped)
            filteredCompanies && filteredCompanies.length > 0 ? (
              filteredCompanies.map((company, index) => (
                <tr
                  key={company.id}
                  className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      {company.logo ? (
                        <img
                          src={company.logo}
                          alt={company.name}
                          width={32}
                          height={32}
                          className='rounded-lg object-cover shadow-sm border border-gray-200'
                        />
                      ) : (
                        <div
                          className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                          style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                        >
                          {company.name.charAt(0)}
                        </div>
                      )}
                      <div>
                        <Typography className='font-semibold text-gray-900'>{company.name}</Typography>
                        <Typography variant='caption' className='text-gray-600'>
                          {company.companyWebsite ? (
                            <a
                              href={company.companyWebsite}
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-blue-600 hover:text-blue-800'
                            >
                              {company.companyWebsite}
                            </a>
                          ) : (
                            'No website'
                          )}
                        </Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <Typography className='font-medium text-gray-800'>{company.designation}</Typography>
                  </td>
                  <td className='!plb-1'>
                    <div className='bg-blue-100 px-2 py-1 rounded-md inline-block'>
                      <Typography variant='body2' className='font-medium text-blue-700'>
                        {new Date(company.startedAt).toLocaleDateString()} -{' '}
                        {company.endedAt ? new Date(company.endedAt).toLocaleDateString() : 'Present'}
                      </Typography>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div
                      className={`px-3 py-1 rounded-full text-xs font-medium inline-block ${company.isCurrent ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`}
                    >
                      {company.isCurrent ? 'Current' : 'Previous'}
                    </div>
                  </td>
                  <td className='!plb-1'>
                    {onStatusChange ? (
                      <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                        <Tooltip title={getActiveStatus(company) ? 'Deactivate' : 'Activate'}>
                          <span>
                            <Switch
                              checked={getActiveStatus(company)}
                              onChange={() => handleStatusToggle(company.id as string, getActiveStatus(company))}
                              disabled={updatingCompanies[company.id as string]}
                              color='primary'
                              size='medium'
                            />
                          </span>
                        </Tooltip>
                        <div
                          style={{ width: '24px', display: 'inline-flex', justifyContent: 'center', marginLeft: '8px' }}
                        >
                          {updatingCompanies[company.id as string] && <CircularProgress size={18} />}
                        </div>
                      </div>
                    ) : (
                      <div className='flex items-center justify-center'>
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(company) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                        >
                          {getActiveStatus(company) ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                    )}
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-1 justify-center'>
                      <Tooltip title='View'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/company?tab=company&id=${company.id}&mode=view`}
                          className='hover:bg-blue-50'
                        >
                          <i className='ri-eye-line text-blue-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/company?tab=company&id=${company.id}&mode=edit`}
                          className='hover:bg-orange-50'
                        >
                          <i className='ri-pencil-line text-orange-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          onClick={() => handleDeleteClick(company.id, company.name)}
                          className='hover:bg-red-50'
                        >
                          <i className='ri-delete-bin-line text-red-600'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className='text-center py-12'>
                  <div className='flex flex-col items-center gap-3'>
                    <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
                      <i className='ri-building-line text-2xl text-gray-400'></i>
                    </div>
                    <Typography variant='h6' className='text-gray-500'>
                      No company records found
                    </Typography>
                    <Typography variant='body2' className='text-gray-400'>
                      {selectedUserId && selectedUserId !== 'all'
                        ? 'No company records found for the selected user'
                        : 'Start by adding your first company record'}
                    </Typography>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {total > 0 && totalPages > 0 && onPageChange && onLimitChange && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3, px: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant='body2' color='text.secondary'>
              Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} companies
            </Typography>
            <TextField
              select
              size='small'
              label='Per page'
              value={limit}
              onChange={onLimitChange}
              disabled={loading}
              sx={{ minWidth: 100 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </TextField>
          </Box>
          {/*@ts-ignore*/}
          <Pagination
            count={totalPages}
            page={page}
            onChange={onPageChange}
            disabled={loading}
            color='primary'
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
      >
        <DialogTitle id='delete-dialog-title'>Delete Company</DialogTitle>
        <DialogContent>
          <DialogContentText id='delete-dialog-description'>
            Are you sure you want to delete <strong>{deleteDialog.companyName}</strong>? This action cannot be undone.
          </DialogContentText>
          {deleteError && (
            <Typography color='error' sx={{ mt: 2 }}>
              Error: {deleteError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color='primary'>
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default CompaniesTable
