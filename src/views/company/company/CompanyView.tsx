'use client'


// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Checkbox from '@mui/material/Checkbox'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import { useCompanyForm } from './hooks/useCompanyForm'

// Component Imports

const CompanyView = () => {
  const { formData, imgSrc, fetchLoading, notification, isViewMode, companyId, setNotification, router } =
    useCompanyForm()

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Company Details' />
        <CardContent>
          <Typography>Loading company data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Company Details' subheader='View company information' />
      <CardContent className='mbe-5'>
        <div className='flex justify-between items-start'>
          <div className='flex max-sm:flex-col items-center gap-4'>
            <img height={80} width={80} className='rounded' src={imgSrc} alt='Company Logo' />
            <div className='flex flex-grow flex-col gap-1'>
              <Typography variant='h5' className='font-semibold'>
                {formData.name}
              </Typography>
              <Typography variant='h6' color='text.secondary'>
                {formData.designation}
              </Typography>
              {formData.companyWebsite && (
                <Typography variant='body2'>
                  <a
                    href={formData.companyWebsite}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='text-primary flex items-center gap-1'
                  >
                    <i className='ri-global-line'></i>
                    {formData.companyWebsite}
                  </a>
                </Typography>
              )}
            </div>
          </div>
          <div className='flex gap-3'>
            <div className='flex items-center'>
              <Checkbox
                checked={formData.isCurrent}
                disabled
                size='small'
                sx={{ color: formData.isCurrent ? 'success.main' : 'inherit', padding: '2px' }}
              />
              <Typography
                variant='caption'
                color={formData.isCurrent ? 'success.main' : 'text.secondary'}
                fontWeight={formData.isCurrent ? 500 : 400}
              >
                Current
              </Typography>
            </div>
            <div className='flex items-center'>
              <Checkbox
                checked={formData.isPromoted}
                disabled
                size='small'
                sx={{ color: formData.isPromoted ? 'primary.main' : 'inherit', padding: '2px' }}
              />
              <Typography
                variant='caption'
                color={formData.isPromoted ? 'primary.main' : 'text.secondary'}
                fontWeight={formData.isPromoted ? 500 : 400}
              >
                Promoted
              </Typography>
            </div>
            <div className='flex items-center'>
              <Checkbox
                checked={formData.isActive}
                disabled
                size='small'
                sx={{ color: formData.isActive ? 'info.main' : 'inherit', padding: '2px' }}
              />
              <Typography
                variant='caption'
                color={formData.isActive ? 'info.main' : 'text.secondary'}
                fontWeight={formData.isActive ? 500 : 400}
              >
                Active
              </Typography>
            </div>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <Grid container spacing={6}>
          {/* First Row */}
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label='Company Name'
              value={formData.name}
              InputProps={{ readOnly: true }}
              size='small'
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label='Designation'
              value={formData.designation}
              InputProps={{ readOnly: true }}
              size='small'
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <div className='flex gap-2'>
              <TextField
                fullWidth
                label='Started At'
                value={formData.startedAt ? new Date(formData.startedAt).toLocaleDateString() : ''}
                InputProps={{ readOnly: true }}
                size='small'
              />
              <TextField
                fullWidth
                label='Ended At'
                value={formData.endedAt ? new Date(formData.endedAt).toLocaleDateString() : 'Present'}
                InputProps={{ readOnly: true }}
                size='small'
              />
            </div>
          </Grid>

          {/* Second Row */}
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label='Work Area'
              value={formData.workArea || ''}
              InputProps={{ readOnly: true }}
              size='small'
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <div className='flex gap-2'>
              <TextField
                fullWidth
                label='Phone Number'
                value={formData.phone || ''}
                InputProps={{ readOnly: true }}
                size='small'
              />
              <TextField
                fullWidth
                label='Salary'
                value={formData.salary ? `${formData.salary} ${formData.currency?.toUpperCase() || ''}` : ''}
                InputProps={{ readOnly: true }}
                size='small'
              />
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label='Country'
              value={formData.country || ''}
              InputProps={{ readOnly: true }}
              size='small'
            />
          </Grid>

          {/* Third Row */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label='Address'
              value={formData.address || ''}
              InputProps={{ readOnly: true }}
              size='small'
            />
          </Grid>

          {/* Fourth Row */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='Company Description'
              value={formData.companyDescription || ''}
              InputProps={{ readOnly: true }}
              multiline
              rows={2}
              size='small'
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='Work Description'
              value={formData.workDescription || ''}
              InputProps={{ readOnly: true }}
              multiline
              rows={2}
              size='small'
            />
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/company?tab=company&id=${companyId}&mode=edit`)}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/company?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default CompanyView
