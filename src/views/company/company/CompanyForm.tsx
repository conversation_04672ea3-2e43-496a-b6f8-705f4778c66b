'use client'

import { Controller } from 'react-hook-form'


// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Checkbox from '@mui/material/Checkbox'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import SimpleCropper from '@/components/SimpleCropper'
import { useCompanyForm } from './hooks/useCompanyForm'

// Component Imports

const CompanyForm = () => {
  const {
    formData,
    fileInput,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isEditMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  } = useCompanyForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Company' : 'Add New Company'} />
        <CardContent>
          <Typography>Loading company data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Company' : 'Add New Company'}
        subheader={isEditMode ? 'Update company information' : 'Create a new company profile'}
        action={
          <div className='flex flex-col sm:flex-row gap-2 sm:gap-4 mt-2'>
            <div className='flex items-center'>
              <Checkbox
                {...register('isCurrent')}
                checked={formData.isCurrent}
                onChange={e => handleFormChange('isCurrent', e.target.checked)}
                size='small'
                sx={{ color: formData.isCurrent ? 'success.main' : 'inherit' }}
              />
              <Typography
                variant='body2'
                color={formData.isCurrent ? 'success.main' : 'text.secondary'}
                fontWeight={formData.isCurrent ? 500 : 400}
              >
                Currently Working
              </Typography>
            </div>
            <div className='flex items-center'>
              <Checkbox
                {...register('isPromoted')}
                checked={formData.isPromoted}
                size='small'
                sx={{ color: formData.isPromoted ? 'primary.main' : 'inherit' }}
              />
              <Typography
                variant='body2'
                color={formData.isPromoted ? 'primary.main' : 'text.secondary'}
                fontWeight={formData.isPromoted ? 500 : 400}
              >
                Promoted
              </Typography>
            </div>
            <div className='flex items-center'>
              <Checkbox
                {...register('isActive')}
                checked={formData.isActive}
                size='small'
                sx={{ color: formData.isActive ? 'info.main' : 'inherit' }}
              />
              <Typography
                variant='body2'
                color={formData.isActive ? 'info.main' : 'text.secondary'}
                fontWeight={formData.isActive ? 500 : 400}
              >
                Active
              </Typography>
            </div>
          </div>
        }
      />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Company Logo' />
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button component='label' size='small' variant='contained' htmlFor='account-settings-upload-image'>
                Upload Company Logo
                <input
                  hidden
                  type='file'
                  value={fileInput}
                  accept='image/png, image/jpeg'
                  onChange={handleFileInputChange}
                  id='account-settings-upload-image'
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='primary'
                onClick={handleOpenCropper}
                disabled={!imgSrc || imgSrc === '/images/avatars/1.png'}
              >
                Crop Image
              </Button>
              <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                Reset
              </Button>
            </div>
            <Typography>Allowed JPG, GIF or PNG. Max size of 800K</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='flex flex-col md:flex-row gap-6'>
            {/* Left Column */}
            <div className='flex-1'>
              <div className='flex flex-col gap-5'>
                <TextField
                  fullWidth
                  label='Company Name'
                  defaultValue={formData.name}
                  placeholder='Company Name'
                  {...register('name', { required: true })}
                  error={Boolean(errors.name)}
                  helperText={errors.name ? 'Company name is required' : ''}
                />

                <TextField
                  fullWidth
                  label='Company Description'
                  defaultValue={formData.companyDescription}
                  placeholder='Brief description of the company'
                  {...register('companyDescription')}
                  multiline
                  rows={2}
                />

                <div className='flex gap-4'>
                  <TextField
                    fullWidth
                    type='date'
                    label='Started At'
                    defaultValue={formData.startedAt}
                    InputLabelProps={{ shrink: true }}
                    {...register('startedAt', { required: true })}
                    error={Boolean(errors.startedAt)}
                    helperText={errors.startedAt ? 'Start date is required' : ''}
                  />
                  <TextField
                    fullWidth
                    type='date'
                    label='Ended At'
                    defaultValue={formData.endedAt}
                    InputLabelProps={{ shrink: true }}
                    {...register('endedAt')}
                    disabled={formData.isCurrent}
                  />
                </div>

                <TextField
                  fullWidth
                  label='Work Description'
                  defaultValue={formData.workDescription}
                  placeholder='Description of your responsibilities'
                  multiline
                  rows={3}
                  {...register('workDescription')}
                />
              </div>
            </div>

            {/* Right Column */}
            <div className='flex-1'>
              <div className='flex flex-col gap-5'>
                <TextField
                  fullWidth
                  label='Designation'
                  defaultValue={formData.designation}
                  placeholder='Your Position'
                  {...register('designation', { required: true })}
                  error={Boolean(errors.designation)}
                  helperText={errors.designation ? 'Designation is required' : ''}
                />

                <div className='flex gap-4'>
                  <TextField
                    fullWidth
                    label='Work Area'
                    defaultValue={formData.workArea}
                    placeholder='Department or Area'
                    {...register('workArea')}
                  />
                  <TextField
                    fullWidth
                    type='url'
                    label='Company Website'
                    defaultValue={formData.companyWebsite}
                    placeholder='https://example.com'
                    {...register('companyWebsite')}
                  />
                </div>

                <div className='flex gap-4'>
                  <TextField
                    fullWidth
                    label='Phone Number'
                    defaultValue={formData.phone}
                    placeholder='+****************'
                    {...register('phone')}
                  />
                  <TextField
                    fullWidth
                    label='Address'
                    defaultValue={formData.address}
                    placeholder='Address'
                    {...register('address')}
                  />
                </div>

                <div className='flex gap-4'>
                  <TextField
                    fullWidth
                    label='Salary'
                    type='number'
                    defaultValue={formData.salary}
                    placeholder='Salary'
                    {...register('salary')}
                  />
                  <Controller
                    name='currency'
                    control={control}
                    defaultValue={formData.currency || 'usd'}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Currency</InputLabel>
                        <Select {...field} label='Currency'>
                          <MenuItem value='bdt'>BDT</MenuItem>
                          <MenuItem value='usd'>USD</MenuItem>
                          <MenuItem value='euro'>EUR</MenuItem>
                          <MenuItem value='pound'>Pound</MenuItem>
                          <MenuItem value='bitcoin'>Bitcoin</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </div>
                <div className='flex gap-4'>
                  <Controller
                    name='country'
                    control={control}
                    defaultValue={formData.country || ''}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>Country</InputLabel>
                        <Select {...field} label='Country'>
                          <MenuItem value='bangladesh'>Bangladesh</MenuItem>
                          <MenuItem value='usa'>USA</MenuItem>
                          <MenuItem value='uk'>UK</MenuItem>
                          <MenuItem value='australia'>Australia</MenuItem>
                          <MenuItem value='germany'>Germany</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update Company' : 'Save Company'}
            </Button>
            <Button
              variant='outlined'
              type='reset'
              color='secondary'
              onClick={() => reset(formData)}
              disabled={loading}
            >
              Reset
            </Button>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => router.push('/company?tab=all')}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default CompanyForm
