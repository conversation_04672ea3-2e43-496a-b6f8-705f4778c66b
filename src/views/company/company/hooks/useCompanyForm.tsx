'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { type SubmitHandler, useForm } from 'react-hook-form'

import type { CompanyEntity } from '@/types/entities'

// Empty company template for new companies
export const emptyCompany: CompanyEntity = {
  name: '',
  companyDescription: '',
  designation: '',
  startedAt: new Date(),
  endedAt: null,
  phone: '',
  address: '',
  workArea: '',
  workDescription: '',
  country: '',
  companyWebsite: '',
  logo: '',
  isCurrent: true,
  isActive: true,
  isPromoted: false,
  salary: 0,
  currency: 'usd',
  user: undefined,
  id: '',
  active: false
}

export const useCompanyForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const companyId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && companyId)

  // States
  const [formData, setFormData] = useState<CompanyEntity>(emptyCompany)
  const [originalData, setOriginalData] = useState<CompanyEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/1.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<CompanyEntity>()

  // Fetch company data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && companyId) {
      const fetchCompany = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/company/${companyId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch company')
          }

          const data = await response.json()

          // Format dates for form inputs
          if (data.startedAt) {
            const startDate = new Date(data.startedAt)

            data.startedAt = startDate.toISOString().split('T')[0]
          }

          if (data.endedAt) {
            const endDate = new Date(data.endedAt)

            data.endedAt = endDate.toISOString().split('T')[0]
          }

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)
          reset(data)

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }
        } catch (err) {
          console.error('Error fetching company:', err)
          setNotification({
            show: true,
            message: 'Failed to load company data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchCompany()
    }
  }, [companyId, isEditMode, isViewMode, reset])

  const onSubmit: SubmitHandler<CompanyEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Process form data
      // Don't set logo here, we'll add the file directly to FormData
      data.salary = parseInt(data.salary ? data.salary.toString() : '0')
      data.startedAt = new Date(data.startedAt)
      data.endedAt = data.endedAt ? new Date(data.endedAt) : null

      let url = '/api/company'
      let method = 'POST'

      // If creating new company
      if (!isEditMode) {
        // Add all form fields to FormData
        Object.keys(data).forEach(key => {
          // Skip logo as we'll handle it separately
          if (key === 'logo') return

          // Handle dates
          if (key === 'startedAt' || key === 'endedAt') {
            if (data[key]) {
              formData.append(key, data[key].toISOString())
            }

            return
          }

          // Add other fields
          // @ts-ignore
          if (data[key] !== undefined && data[key] !== null) {
            // @ts-ignore
            formData.append(key, data[key].toString())
          }
        })

        // Add logo file if available
        if (logoFile) {
          formData.append('logo', logoFile)
        }
      } else if (isEditMode && companyId && originalData) {
        // If editing, use PATCH method and include ID in URL
        url = `/api/company/${companyId}`
        method = 'PATCH'

        // Only include changed fields in the FormData for PATCH
        Object.keys(data).forEach(key => {
          // Skip id field
          if (key === 'id' || key === 'logo') return

          // Handle dates specially
          if (key === 'startedAt' || key === 'endedAt') {
            const originalDate = originalData[key] ? new Date(originalData[key]).getTime() : null
            const newDate = data[key] ? new Date(data[key]).getTime() : null

            if (originalDate !== newDate && data[key]) {
              formData.append(key, data[key].toISOString())
            }

            return
          }

          // For all other fields, do a simple comparison
          // @ts-ignore
          if (originalData[key] !== data[key] && data[key] !== undefined && data[key] !== null) {
            // @ts-ignore
            formData.append(key, data[key].toString())
          }
        })

        // Add logo file if available and changed
        if (logoFile) {
          formData.append('logo', logoFile)
        }
      }

      // Log FormData (for debugging)

      const res = await fetch(url, {
        method,

        // Don't set Content-Type header, the browser will set it with the boundary
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save company')
      }

      const resp = await res.json()

      setNotification({
        show: true,
        message: isEditMode ? 'Company updated successfully' : 'Company created successfully',
        type: 'success'
      })

      // Redirect to companies list after successful save
      setTimeout(() => {
        router.push('/company?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save company'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFormChange = (field: keyof CompanyEntity, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      setLogoFile(files[0])
      setFileInput(event.target.value)

      // Read and display the image preview
      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/1.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/1.png')
    setLogoFile(null)
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    companyId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
