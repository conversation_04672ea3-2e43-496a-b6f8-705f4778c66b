'use client'

// React Imports
import { useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Switch from '@mui/material/Switch'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import TextField from '@mui/material/TextField'
import FormControlLabel from '@mui/material/FormControlLabel'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Tabs from '@mui/material/Tabs'
import Tab from '@mui/material/Tab'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import CircularProgress from '@mui/material/CircularProgress'

// Component Imports
import CustomIconButton from '@core/components/mui/IconButton'
import ImageCropper from '@components/ImageCropper'

// Hook Imports
import { type SocialMediaItem, useSocialMedia } from '@/hooks/useSocialMedia'

type SocialAccountsType = {
  id?: string
  name: string
  logo?: string
  username?: string
  description?: string
  isConnected: boolean
  href?: string
  active?: boolean
  link?: string
}

const Connections = () => {
  // Use the social media hook
  const {
    socialMediaItems,
    loading,
    error,
    createSocialMedia,
    updateSocialMedia,
    deleteSocialMedia,
    toggleSocialMediaActive
  } = useSocialMedia()

  // State for modal
  const [modalOpen, setModalOpen] = useState<boolean>(false)

  // State for form data
  const [formData, setFormData] = useState<SocialAccountsType>({
    name: '',
    logo: '',
    description: '',
    isConnected: false,
    active: true,
    link: ''
  })

  // State for edit mode
  const [isEditMode, setIsEditMode] = useState<boolean>(false)

  // State for notification
  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // State for file upload
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('')
  const [logoFile, setLogoFile] = useState<File | null>(null)

  // State for image cropper
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // State for upload method tabs
  const [uploadTab, setUploadTab] = useState<number>(0)

  // Convert SocialMediaItem to SocialAccountsType for display
  const convertToSocialAccount = (item: SocialMediaItem): SocialAccountsType => ({
    id: item.id,
    name: item.name,
    logo: item.logo,
    description: item.description,
    isConnected: true, // All items from API are considered connected
    active: item.active,
    link: item.link,
    href: item.link
  })

  // Convert SocialAccountsType to API format
  const convertToApiFormat = (data: SocialAccountsType) => ({
    name: data.name,
    link: data.link || '',
    description: data.description || '',
    active: data.active ?? true,
    logo: data.logo
  })

  // Handle form change
  const handleFormChange = (field: keyof SocialAccountsType, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Handle modal open for adding new social media
  const handleAddSocialMedia = () => {
    setIsEditMode(false)
    setFormData({
      name: '',
      logo: '',
      description: '',
      isConnected: false,
      active: true,
      link: ''
    })
    setImgSrc('')
    setFileInput('')
    setLogoFile(null)
    setUploadTab(0)
    setModalOpen(true)
  }

  // Handle modal open for editing social media
  const handleEditSocialMedia = (item: SocialAccountsType) => {
    setIsEditMode(true)
    setFormData({
      ...item
    })
    setImgSrc(Array.isArray(item.logo) ? item.logo[0] || '' : item.logo || '')
    setFileInput('')
    setLogoFile(null)
    setUploadTab(0)
    setModalOpen(true)
  }

  // Handle modal close
  const handleModalClose = () => {
    setModalOpen(false)
    setCropperOpen(false)
  }

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      // Create preview URL
      const previewUrl = URL.createObjectURL(file)

      setImgSrc(previewUrl)
      setTempImgSrc(previewUrl) // Also store in tempImgSrc for cropper
      handleFormChange('logo', previewUrl)
    }
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setImgSrc('')
    setFileInput('')
    setLogoFile(null)
    handleFormChange('logo', '')
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc) {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Convert blob to file
    const file = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    setLogoFile(file)

    // Create preview URL
    const previewUrl = URL.createObjectURL(croppedImageBlob)

    setImgSrc(previewUrl)
    handleFormChange('logo', previewUrl)
    setCropperOpen(false)
  }

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setUploadTab(newValue)
  }

  // Handle form submit
  const handleSubmit = async () => {
    if (!formData.name || !formData.link) {
      setNotification({
        show: true,
        message: 'Please fill in all required fields',
        type: 'error'
      })

      return
    }

    const apiData = convertToApiFormat(formData)

    if (isEditMode && formData.id) {
      // Update existing social media
      const result = await updateSocialMedia(formData.id, apiData, logoFile || undefined)

      if (result) {
        setModalOpen(false)
      }
    } else {
      // Add new social media
      const result = await createSocialMedia(apiData, logoFile || undefined)

      if (result) {
        setModalOpen(false)
      }
    }
  }

  // Handle toggle active status
  const handleToggleActive = async (id: string | undefined) => {
    if (!id) return

    const items = Array.isArray(socialMediaItems) ? socialMediaItems : []
    const item = items.find(item => item.id === id)

    if (item) {
      await toggleSocialMediaActive(id, !item.active)
    }
  }

  // Handle delete social media
  const handleDeleteSocialMedia = async (id: string | undefined) => {
    if (!id) return
    await deleteSocialMedia(id)
  }

  // Convert API data to display format - ensure socialMediaItems is always an array
  const socialAccounts = (Array.isArray(socialMediaItems) ? socialMediaItems : []).map(convertToSocialAccount)

  return (
    <Card>
      <Grid container>
        <Grid item xs={12}>
          <CardHeader
            title='My Social Accounts'
            subheader='Manage your personal social media accounts'
            action={
              <Button
                variant='contained'
                startIcon={<i className='ri-add-line'></i>}
                onClick={handleAddSocialMedia}
                disabled={loading}
              >
                Add Social Media
              </Button>
            }
          />
          <CardContent className='flex flex-col gap-4'>
            {error ? (
              <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
                <Typography variant='body2' color='error'>
                  Error: {error}
                </Typography>
              </Box>
            ) : loading && socialAccounts.length === 0 ? (
              <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
                <CircularProgress />
              </Box>
            ) : socialAccounts.length === 0 ? (
              <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
                <Typography variant='body2' color='text.secondary'>
                  No social media accounts found. Add your first social media account!
                </Typography>
              </Box>
            ) : (
              socialAccounts.map((item, index) => (
                <div key={item.id || index} className='flex items-center justify-between gap-4'>
                  <div className='flex flex-grow items-center gap-4'>
                    <img
                      height={32}
                      width={32}
                      src={Array.isArray(item.logo) ? item.logo[0] : item.logo}
                      alt={item.name}
                      onError={e => {
                        ;(e.target as HTMLImageElement).src = '/images/logos/default.png'
                      }}
                    />
                    <div className='flex-grow'>
                      <Typography className='font-medium' color='text.primary'>
                        {item.name}
                      </Typography>
                      {item.isConnected ? (
                        <Typography color='primary' component={Link} href={item.link || '/'} target='_blank'>
                          {item.link}
                        </Typography>
                      ) : (
                        <Typography variant='body2'>Not Connected</Typography>
                      )}
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Switch checked={item.active} onChange={() => handleToggleActive(item.id)} disabled={loading} />
                    <CustomIconButton
                      variant='outlined'
                      color='primary'
                      onClick={() => handleEditSocialMedia(item)}
                      disabled={loading}
                    >
                      <i className='ri-edit-line' />
                    </CustomIconButton>
                    <CustomIconButton
                      variant='outlined'
                      color='error'
                      onClick={() => handleDeleteSocialMedia(item.id)}
                      disabled={loading}
                    >
                      <i className='ri-delete-bin-7-line' />
                    </CustomIconButton>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Grid>
      </Grid>

      {/* Social Media Modal */}
      <Dialog open={modalOpen} onClose={handleModalClose} maxWidth='sm' fullWidth>
        <DialogTitle>{isEditMode ? 'Edit Social Media' : 'Add Social Media'}</DialogTitle>
        <DialogContent>
          <div className='flex flex-col gap-5 mt-4'>
            <TextField
              fullWidth
              label='Name'
              value={formData.name}
              onChange={e => handleFormChange('name', e.target.value)}
              placeholder='Facebook'
              required
            />
            <TextField
              fullWidth
              label='Link'
              type='url'
              value={formData.link}
              onChange={e => handleFormChange('link', e.target.value)}
              placeholder='https://facebook.com/username'
              required
            />
            <TextField
              fullWidth
              label='Description'
              value={formData.description}
              onChange={e => handleFormChange('description', e.target.value)}
              placeholder='Description'
              required
            />

            {/* Logo Upload Section */}
            <Typography variant='subtitle1' className='font-medium mt-2'>
              Logo
            </Typography>

            <Tabs value={uploadTab} onChange={handleTabChange} aria-label='logo upload tabs'>
              <Tab label='Upload File' />
              <Tab label='URL' />
            </Tabs>

            <Box sx={{ mt: 2 }}>
              {uploadTab === 0 && (
                <div className='flex flex-col gap-4'>
                  <div className='flex items-center justify-center'>
                    {imgSrc ? (
                      <img
                        src={imgSrc}
                        alt='Logo Preview'
                        className='rounded'
                        style={{ width: '100px', height: '100px', objectFit: 'contain' }}
                      />
                    ) : (
                      <Box
                        sx={{
                          width: 100,
                          height: 100,
                          border: '1px dashed grey',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '4px'
                        }}
                      >
                        <Typography variant='caption' color='text.secondary'>
                          No image
                        </Typography>
                      </Box>
                    )}
                  </div>

                  <div className='flex flex-wrap gap-2 justify-center'>
                    <Button component='label' size='small' variant='contained'>
                      Upload Logo
                      <input
                        hidden
                        type='file'
                        value={fileInput}
                        accept='image/png, image/jpeg, image/svg+xml'
                        onChange={handleFileInputChange}
                      />
                    </Button>
                    {imgSrc && (
                      <>
                        <Button size='small' variant='outlined' color='primary' onClick={handleOpenCropper}>
                          Crop Image
                        </Button>
                        <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                          Reset
                        </Button>
                      </>
                    )}
                  </div>
                  <Typography variant='caption' color='text.secondary' align='center'>
                    Allowed JPG, PNG or SVG. Max size of 10MB.
                  </Typography>
                </div>
              )}

              {uploadTab === 1 && (
                <TextField
                  fullWidth
                  label='Logo URL'
                  value={Array.isArray(formData.logo) ? formData.logo[0] || '' : formData.logo || ''}
                  onChange={e => handleFormChange('logo', e.target.value)}
                  placeholder='/images/logos/facebook.png'
                />
              )}
            </Box>

            <Divider sx={{ my: 2 }} />

            <FormControlLabel
              control={
                <Switch checked={formData.active} onChange={e => handleFormChange('active', e.target.checked)} />
              }
              label='Active'
            />
          </div>
        </DialogContent>
        <DialogActions>
          <Button variant='outlined' color='secondary' onClick={handleModalClose}>
            Cancel
          </Button>
          <Button variant='contained' onClick={handleSubmit} disabled={loading}>
            {loading ? <CircularProgress size={20} /> : isEditMode ? 'Update' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Cropper */}
      <ImageCropper
        image={tempImgSrc}
        open={cropperOpen}
        onClose={() => setCropperOpen(false)}
        onCropComplete={handleCroppedImage}
      />

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default Connections
