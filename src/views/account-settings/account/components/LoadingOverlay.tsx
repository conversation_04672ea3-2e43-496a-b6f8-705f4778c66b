// MUI Imports
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import Typography from '@mui/material/Typography'

interface LoadingOverlayProps {
  show: boolean
}

const LoadingOverlay = ({ show }: LoadingOverlayProps) => {
  if (!show) return null

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        borderRadius: 1
      }}
    >
      <CircularProgress size={40} />
      <Typography variant='body2' sx={{ mt: 2, color: 'text.secondary' }}>
        Loading your profile...
      </Typography>
    </Box>
  )
}

export default LoadingOverlay
