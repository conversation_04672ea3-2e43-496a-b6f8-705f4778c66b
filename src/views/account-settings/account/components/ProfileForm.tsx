// MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'

// Types
import type { UserFormData } from '../types'

interface ProfileFormProps {
  formData: UserFormData
  fetchLoading: boolean
  onFormChange: (field: keyof UserFormData, value: UserFormData[keyof UserFormData]) => void
}

const ProfileForm = ({ formData, fetchLoading, onFormChange }: ProfileFormProps) => {
  return (
    <>
      {/* Profile Header */}
      <Grid item xs={12}>
        <Typography variant='h6' sx={{ mb: 2 }}>
          My Profile
        </Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label='Full Name'
          value={formData.name}
          placeholder='<PERSON>'
          onChange={e => onFormChange('name', e.target.value)}
          disabled={fetchLoading}
        />
      </Grid>

      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label='Email'
          type='email'
          value={formData.email}
          placeholder='<EMAIL>'
          onChange={e => onFormChange('email', e.target.value)}
          disabled={fetchLoading}
        />
      </Grid>

      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label='Phone Number'
          value={formData.phone || ''}
          placeholder='+****************'
          onChange={e => onFormChange('phone', e.target.value)}
          disabled={fetchLoading}
        />
      </Grid>

      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label='Date of Birth'
          type='date'
          value={formData.dob ? formData.dob.toISOString().split('T')[0] : ''}
          InputLabelProps={{ shrink: true }}
          onChange={e => onFormChange('dob', new Date(e.target.value))}
          disabled={fetchLoading}
        />
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControl fullWidth disabled={fetchLoading}>
          <InputLabel>Status</InputLabel>
          <Select
            label='Status'
            value={formData.status ? 'active' : 'inactive'}
            onChange={e => onFormChange('status', e.target.value === 'active')}
          >
            <MenuItem value='active'>Active</MenuItem>
            <MenuItem value='inactive'>Inactive</MenuItem>
          </Select>
        </FormControl>
      </Grid>
    </>
  )
}

export default ProfileForm
