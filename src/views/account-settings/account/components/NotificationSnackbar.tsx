// MUI Imports
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Types
import type { NotificationState } from '../types'

interface NotificationSnackbarProps {
  notification: NotificationState
  onClose: () => void
}

const NotificationSnackbar = ({ notification, onClose }: NotificationSnackbarProps) => {
  return (
    <Snackbar
      open={notification.show}
      autoHideDuration={6000}
      onClose={onClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
    >
      <Alert onClose={onClose} severity={notification.type} sx={{ width: '100%' }}>
        {notification.message}
      </Alert>
    </Snackbar>
  )
}

export default NotificationSnackbar
