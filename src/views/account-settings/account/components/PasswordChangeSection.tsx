// MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'

// Types
import type { PasswordData } from '../types'

interface PasswordChangeSectionProps {
  showPasswordChange: boolean
  passwordData: PasswordData
  loading: boolean
  fetchLoading: boolean
  onTogglePasswordChange: () => void
  onPasswordDataChange: (data: PasswordData) => void
  onPasswordChange: () => void
}

const PasswordChangeSection = ({
  showPasswordChange,
  passwordData,
  loading,
  fetchLoading,
  onTogglePasswordChange,
  onPasswordDataChange,
  onPasswordChange
}: PasswordChangeSectionProps) => {
  return (
    <>
      {/* Divider */}
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant='h6' sx={{ mb: 2 }}>
          Password Management
        </Typography>
      </Grid>

      {/* Password Change Section */}
      <Grid item xs={12}>
        <Button variant='outlined' color='secondary' onClick={onTogglePasswordChange} disabled={fetchLoading}>
          {showPasswordChange ? 'Cancel Password Change' : 'Change Password'}
        </Button>
      </Grid>

      {showPasswordChange && (
        <>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label='Current Password'
              type='password'
              value={passwordData.currentPassword}
              onChange={e => onPasswordDataChange({ ...passwordData, currentPassword: e.target.value })}
              disabled={loading || fetchLoading}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label='New Password'
              type='password'
              value={passwordData.newPassword}
              onChange={e => onPasswordDataChange({ ...passwordData, newPassword: e.target.value })}
              disabled={loading || fetchLoading}
              helperText='Minimum 6 characters'
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label='Confirm New Password'
              type='password'
              value={passwordData.confirmPassword}
              onChange={e => onPasswordDataChange({ ...passwordData, confirmPassword: e.target.value })}
              disabled={loading || fetchLoading}
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant='contained'
              color='warning'
              onClick={onPasswordChange}
              disabled={
                loading ||
                fetchLoading ||
                !passwordData.currentPassword ||
                !passwordData.newPassword ||
                !passwordData.confirmPassword
              }
            >
              {loading ? 'Changing Password...' : 'Change Password'}
            </Button>
          </Grid>
        </>
      )}
    </>
  )
}

export default PasswordChangeSection
