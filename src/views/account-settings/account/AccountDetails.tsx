'use client'

// MUI Imports
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'

// Local Imports
import { useAccountDetails } from './hooks/useAccountDetails'
import ProfileImageSection from './components/ProfileImageSection'
import ProfileForm from './components/ProfileForm'
import PasswordChangeSection from './components/PasswordChangeSection'
import LoadingOverlay from './components/LoadingOverlay'
import NotificationSnackbar from './components/NotificationSnackbar'
import ImageCropper from '@/components/ImageCropper'

const AccountDetails = () => {
  const {
    // Data
    formData,
    fetchLoading,
    error,
    loading,
    notification,
    imgSrc,

    // Password management
    showPasswordChange,
    setShowPasswordChange,
    passwordData,
    setPasswordData,

    // File upload
    fileInput,
    fileUploading,
    cropperOpen,
    tempImgSrc,

    // Handlers
    handleFormChange,
    handleUpdateUser,
    handlePasswordChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    handleNotificationClose
  } = useAccountDetails()

  return (
    <Card sx={{ position: 'relative' }}>
      <LoadingOverlay show={fetchLoading} />

      <ProfileImageSection
        imgSrc={imgSrc}
        fileInput={fileInput}
        fetchLoading={fetchLoading}
        fileUploading={fileUploading}
        // @ts-ignore
        onFileInputChange={handleFileInputChange}
        onFileInputReset={handleFileInputReset}
        onOpenCropper={handleOpenCropper}
      />

      <CardContent>
        <form onSubmit={e => e.preventDefault()}>
          <Grid container spacing={5}>
            <ProfileForm formData={formData} fetchLoading={fetchLoading} onFormChange={handleFormChange} />

            <PasswordChangeSection
              showPasswordChange={showPasswordChange}
              passwordData={passwordData}
              loading={loading}
              fetchLoading={fetchLoading}
              onTogglePasswordChange={() => setShowPasswordChange(!showPasswordChange)}
              onPasswordDataChange={setPasswordData}
              onPasswordChange={handlePasswordChange}
            />

            {error && (
              <Grid item xs={12}>
                <Typography color='error' variant='body2'>
                  {error}
                </Typography>
              </Grid>
            )}

            <Grid item xs={12} className='flex gap-4 flex-wrap'>
              <Button variant='contained' type='button' onClick={handleUpdateUser} disabled={loading || fetchLoading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
              <Button
                variant='outlined'
                type='reset'
                color='secondary'
                onClick={() => window.location.reload()}
                disabled={loading || fetchLoading}
              >
                Reset
              </Button>
            </Grid>
          </Grid>
        </form>
      </CardContent>

      <NotificationSnackbar notification={notification} onClose={handleNotificationClose} />

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <ImageCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default AccountDetails
