import { useState } from 'react'

import { useUserData } from './useUserData'
import { useUserUpdate } from './useUserUpdate'
import { usePasswordChange } from './usePasswordChange'
import { useFileUpload } from './useFileUpload'
import type { NotificationState } from '../types'
import { initialNotification } from '../types'

export const useAccountDetails = () => {
  const [notification, setNotification] = useState<NotificationState>(initialNotification)

  // User data management
  const {
    formData,
    setFormData,
    fetchLoading,
    error,
    setError,
    userId,
    imgSrc,
    setImgSrc,
    originalImageSrc,
    handleFormChange,
    updateUserData
  } = useUserData()

  // File upload operations
  const {
    fileInput,
    uploading: fileUploading,
    cropperOpen,
    tempImgSrc,
    profileImageFile,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen
  } = useFileUpload({
    formData,
    setFormData,
    imgSrc,
    setImgSrc,
    setNotification,
    originalImageSrc
  })

  // User update operations
  const { loading: updateLoading, handleUpdateUser } = useUserUpdate({
    userId,
    formData,
    profileImageFile,
    updateUserData,
    setNotification,
    setError
  })

  // Password change operations
  const {
    showPasswordChange,
    setShowPasswordChange,
    passwordData,
    setPasswordData,
    loading: passwordLoading,
    handlePasswordChange
  } = usePasswordChange({
    userId,
    setNotification,
    setError
  })

  // Combined loading state
  const loading = updateLoading || passwordLoading || fileUploading

  // Notification handlers
  const handleNotificationClose = () => {
    setNotification({ ...notification, show: false })
  }

  return {
    // Data
    formData,
    fetchLoading,
    error,
    userId,
    imgSrc,
    loading,
    notification,

    // Password management
    showPasswordChange,
    setShowPasswordChange,
    passwordData,
    setPasswordData,

    // File upload
    fileInput,
    fileUploading,
    cropperOpen,
    tempImgSrc,
    profileImageFile,

    // Handlers
    handleFormChange,
    handleUpdateUser,
    handlePasswordChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    handleNotificationClose
  }
}
