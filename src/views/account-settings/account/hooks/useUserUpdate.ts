import { useState } from 'react'

import type { NotificationState, UserFormData } from '../types'

interface UseUserUpdateProps {
  userId: string | null
  formData: UserFormData
  profileImageFile: File | null
  updateUserData: (updatedUser: any) => void
  setNotification: (notification: NotificationState) => void
  setError: (error: string | null) => void
}

export const useUserUpdate = ({
  userId,
  formData,
  profileImageFile,
  updateUserData,
  setNotification,
  setError
}: UseUserUpdateProps) => {
  const [loading, setLoading] = useState<boolean>(false)

  const handleUpdateUser = async () => {
    try {
      setLoading(true)
      setError(null)

      // Validate required fields
      if (!formData.name || !formData.email) {
        throw new Error('Name and email are required')
      }

      // Create FormData for file upload
      const formDataToSend = new FormData()

      // Add all form fields to FormData with proper validation
      formDataToSend.append('name', formData.name.trim())
      formDataToSend.append('email', formData.email.trim())

      // Add optional fields only if they have values
      if (formData.phone && formData.phone.trim()) {
        formDataToSend.append('phone', formData.phone.trim())
      }

      if (formData.dob) {
        formDataToSend.append('dob', formData.dob.toISOString())
      }

      // Convert boolean to string
      formDataToSend.append('status', formData.status ? 'true' : 'false')

      // Add profile image file if available
      if (profileImageFile) {
        formDataToSend.append('profileImage', profileImageFile)
      }

      // Make API call using the current userId
      const res = await fetch(`/api/user/${userId}`, {
        method: 'PATCH',
        body: formDataToSend
      })

      if (!res.ok) {
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to update user')
      }

      const updatedUser = await res.json()

      // Update form data with response
      updateUserData(updatedUser)

      // Show success notification
      setNotification({
        show: true,
        message: 'User updated successfully!',
        type: 'success'
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user'

      setError(errorMessage)

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    handleUpdateUser
  }
}
