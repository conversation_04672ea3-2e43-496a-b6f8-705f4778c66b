import { useState } from 'react'

import type { NotificationState, PasswordData } from '../types'
import { initialPasswordData } from '../types'

interface UsePasswordChangeProps {
  userId: string | null
  setNotification: (notification: NotificationState) => void
  setError: (error: string | null) => void
}

export const usePasswordChange = ({ userId, setNotification, setError }: UsePasswordChangeProps) => {
  const [showPasswordChange, setShowPasswordChange] = useState<boolean>(false)
  const [passwordData, setPasswordData] = useState<PasswordData>(initialPasswordData)
  const [loading, setLoading] = useState<boolean>(false)

  const handlePasswordChange = async () => {
    try {
      // Validate passwords
      if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
        throw new Error('All password fields are required')
      }

      if (passwordData.newPassword !== passwordData.confirmPassword) {
        throw new Error('New passwords do not match')
      }

      if (passwordData.newPassword.length < 6) {
        throw new Error('New password must be at least 6 characters long')
      }

      setLoading(true)
      setError(null)

      const response = await fetch(`/api/user/${userId}/change-password`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to change password')
      }

      // Reset password form
      setPasswordData(initialPasswordData)
      setShowPasswordChange(false)

      // Show success notification
      setNotification({
        show: true,
        message: 'Password changed successfully!',
        type: 'success'
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password'

      setError(errorMessage)

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    showPasswordChange,
    setShowPasswordChange,
    passwordData,
    setPasswordData,
    loading,
    handlePasswordChange
  }
}
