import { useEffect, useState } from 'react'

import type { UserFormData } from '../types'
import { initialData } from '../types'

export const useUserData = () => {
  const [formData, setFormData] = useState<UserFormData>(initialData)
  const [fetchLoading, setFetchLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [userId, setUserId] = useState<string | null>(null)
  const [imgSrc, setImgSrc] = useState<string>(initialData.profileImage || '/images/avatars/1.png')
  const [originalImageSrc, setOriginalImageSrc] = useState<string>(initialData.profileImage || '/images/avatars/1.png')

  // Fetch user data by ID
  const fetchUserData = async (id: string) => {
    try {
      setFetchLoading(true)
      setError(null)

      const response = await fetch(`/api/user/${id}`)

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to fetch user')
      }

      const userData = await response.json()

      // Update form data with fetched user data - ensure all required fields are present
      const updatedFormData = {
        id: userData.id || '',
        name: userData.name || '',
        email: userData.email || '',
        password: '', // Don't populate password field
        phone: userData.phone || '',
        dob: userData.dob ? new Date(userData.dob) : undefined,
        profileImage: userData.profileImage || '/images/avatars/1.png',
        status: userData.status !== undefined ? userData.status : true
      }

      setFormData(updatedFormData)

      // Update image source and store original
      // Only use default avatar if profileImage is truly empty/null/undefined
      const hasValidProfileImage =
        userData.profileImage &&
        typeof userData.profileImage === 'string' &&
        userData.profileImage.trim() !== '' &&
        userData.profileImage !== '/images/avatars/1.png'

      const profileImageUrl = hasValidProfileImage ? userData.profileImage : '/images/avatars/1.png'

      console.log('User data from API:', userData)
      console.log('Has valid profile image:', hasValidProfileImage)
      console.log('Profile image URL:', profileImageUrl)

      setImgSrc(profileImageUrl)
      setOriginalImageSrc(profileImageUrl) // Store the original image from database
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch user')
    } finally {
      setFetchLoading(false)
    }
  }

  // Fetch current user info from server
  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/auth/me')

      if (response.ok) {
        const userData = await response.json()

        return userData.id
      } else {
        return null
      }
    } catch (error) {
      return null
    }
  }

  // Load current user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      // Try to get current user ID from server
      const currentUserId = await fetchCurrentUser()

      if (currentUserId) {
        setUserId(currentUserId)
        fetchUserData(currentUserId)
      } else {
        // Fallback to hardcoded ID for testing
        const fallbackUserId = '1'

        setUserId(fallbackUserId)
        await fetchUserData(fallbackUserId)
      }
    }

    loadUserData()
  }, [])

  const handleFormChange = (field: keyof UserFormData, value: UserFormData[keyof UserFormData]) => {
    setFormData({ ...formData, [field]: value })
  }

  const updateUserData = (updatedUser: any) => {
    const profileImageUrl = updatedUser.profileImage || '/images/avatars/1.png'

    setFormData({
      ...updatedUser,
      dob: updatedUser.dob ? new Date(updatedUser.dob) : undefined,
      phone: updatedUser.phone || '',
      profileImage: profileImageUrl
    })
    setImgSrc(profileImageUrl)

    // Update original image after successful save
    setOriginalImageSrc(profileImageUrl)
  }

  return {
    formData,
    setFormData,
    fetchLoading,
    error,
    setError,
    userId,
    imgSrc,
    setImgSrc,
    originalImageSrc,
    handleFormChange,
    updateUserData,
    fetchUserData
  }
}
