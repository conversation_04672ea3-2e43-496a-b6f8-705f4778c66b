/**
 * Image utility functions for profile image handling
 */

export interface ImageValidationResult {
  isValid: boolean
  error?: string
}

export interface ImageCompressionOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
}

// File validation constants
export const IMAGE_VALIDATION = {
  MAX_FILE_SIZE: 800 * 1024, // 800KB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif']
} as const

/**
 * Validate image file
 */
export const validateImageFile = (file: File): ImageValidationResult => {
  // Check file type
  // @ts-ignore
  if (!IMAGE_VALIDATION.ALLOWED_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: 'Please select a valid image file (JPG, PNG, or GIF)'
    }
  }

  // Check file size
  if (file.size > IMAGE_VALIDATION.MAX_FILE_SIZE) {
    const maxSizeMB = IMAGE_VALIDATION.MAX_FILE_SIZE / (1024 * 1024)

    return {
      isValid: false,
      error: `File size must be less than ${maxSizeMB}MB`
    }
  }

  return { isValid: true }
}

/**
 * Compress and resize image
 */
export const compressImage = (file: File, options: ImageCompressionOptions = {}): Promise<string> => {
  const { maxWidth = 400, maxHeight = 400, quality = 0.8, format = 'jpeg' } = options

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    if (!ctx) {
      reject(new Error('Canvas context not available'))

      return
    }

    img.onload = () => {
      try {
        // Calculate new dimensions maintaining aspect ratio
        let { width, height } = img

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height
            height = maxHeight
          }
        }

        // Set canvas dimensions
        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height)

        const mimeType = format === 'png' ? 'image/png' : 'image/jpeg'
        const compressedDataUrl = canvas.toDataURL(mimeType, quality)

        resolve(compressedDataUrl)
      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * Convert data URL to blob
 */
export const dataURLToBlob = (dataURL: string): Blob => {
  const arr = dataURL.split(',')
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg'
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }

  return new Blob([u8arr], { type: mime })
}

/**
 * Get file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if image is default avatar
 */
export const isDefaultAvatar = (imageSrc: string): boolean => {
  return imageSrc === '/images/avatars/1.png' || imageSrc.includes('avatars/1.png')
}
