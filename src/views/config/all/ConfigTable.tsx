'use client'

import { useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Switch from '@mui/material/Switch'

// Type Imports
import type { ConfigEntity } from '@/types/entities'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

type ConfigTableProps = {
  configs: ConfigEntity[]
  loading: boolean
  error: string | null
  onDeleteSuccess: (deletedConfigId: string) => void
  onStatusChange?: (id: string, active: boolean) => Promise<void>
}

const ConfigTable = ({ configs, loading, error, onDeleteSuccess, onStatusChange }: ConfigTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; configId: string | null; configName: string }>({
    open: false,
    configId: null,
    configName: ''
  })

  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)

  // State for tracking configs being updated
  const [updatingConfigs, setUpdatingConfigs] = useState<Record<string, boolean>>({})

  // State for local toggle status (for visual feedback when API fails)
  const [localToggleStatus, setLocalToggleStatus] = useState<Record<string, boolean>>({})

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (configId: string, configName: string) => {
    setDeleteDialog({
      open: true,
      configId,
      configName
    })
  }

  // Handle closing delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      configId: null,
      configName: ''
    })
    setDeleteError(null) // Reset error state when closing dialog
  }

  // Handle confirming deletion
  const handleConfirmDelete = async () => {
    if (!deleteDialog.configId) return

    const configIdToDelete = deleteDialog.configId

    try {
      setDeleteLoading(true)
      setDeleteError(null)

      const response = await fetch(`/api/config/${configIdToDelete}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to delete configuration')
      }

      // Close dialog and notify parent of successful deletion with config ID
      handleCloseDeleteDialog()
      onDeleteSuccess(configIdToDelete)
    } catch (err) {
      console.error('Error deleting configuration:', err)
      setDeleteError(err instanceof Error ? err.message : 'An error occurred while deleting the configuration')
    } finally {
      setDeleteLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='flex justify-center p-6'>
        <CircularProgress />
      </div>
    )
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: boolean) => {
    if (!onStatusChange) return

    try {
      // Set the config as updating
      setUpdatingConfigs(prev => ({ ...prev, [id]: true }))

      // Update local toggle state immediately for visual feedback
      setLocalToggleStatus(prev => ({
        ...prev,
        [id]: !currentStatus
      }))

      // Call the parent handler to update the status
      await onStatusChange(id, !currentStatus)
    } catch (error) {
      console.error('Error updating config status:', error)

      // Keep the local toggle state even if API fails
    } finally {
      // Remove the updating state
      setUpdatingConfigs(prev => {
        const newState = { ...prev }

        delete newState[id]

        return newState
      })
    }
  }

  // Get the effective active status (use local state if available, otherwise use config.active)
  const getActiveStatus = (config: ConfigEntity) => {
    return localToggleStatus[config.id as string] !== undefined ? localToggleStatus[config.id as string] : config.active
  }

  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Configuration</th>
              <th>User</th>
              <th>Theme</th>
              <th>Language</th>
              <th>Default</th>
              <th>Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {configs && configs.length > 0 ? (
              configs.map(config => (
                <tr key={config.id}>
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      <div
                        className='flex items-center justify-center bg-primary-lightOpacity text-primary rounded-full'
                        style={{ width: 34, height: 34 }}
                      >
                        <i className='ri-settings-3-line'></i>
                      </div>
                      <div>
                        <Typography className='font-medium'>Configuration #{config.id?.slice(-6)}</Typography>
                        <Typography variant='body2' color='text.secondary'>
                          {config.isDefault ? 'Default Config' : 'Custom Config'}
                        </Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div className='flex items-center gap-2'>
                      {typeof config.user === 'object' && config.user?.profileImage ? (
                        <img
                          src={config.user.profileImage}
                          alt={config.user.name}
                          width={24}
                          height={24}
                          className='rounded-full object-cover'
                        />
                      ) : (
                        <div
                          className='flex items-center justify-center bg-primary-lightOpacity text-primary rounded-full'
                          style={{ width: 24, height: 24, fontSize: '12px' }}
                        >
                          {typeof config.user === 'object' ? config.user?.name?.charAt(0) || 'U' : 'U'}
                        </div>
                      )}
                      <div>
                        <Typography className='font-medium text-sm'>
                          {typeof config.user === 'object'
                            ? config.user?.name || 'Unknown User'
                            : `User ID: ${config.user}`}
                        </Typography>
                        <Typography variant='body2' color='text.secondary' className='text-xs'>
                          {typeof config.user === 'object' ? config.user?.email || 'No email' : 'User ID only'}
                        </Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    {/*// @ts-ignore*/}
                    <Chip
                      label={config.theme.charAt(0).toUpperCase() + config.theme.slice(1)}
                      color={config.theme === 'dark' ? 'default' : 'primary'}
                      size='small'
                    />
                  </td>
                  <td className='!plb-1'>
                    <Typography className='font-medium'>{config.language.toUpperCase()}</Typography>
                  </td>
                  <td className='!plb-1'>
                    {config.isDefault ? (
                      // @ts-ignore
                      <Chip label='Default' color='success' size='small' />
                    ) : (
                      <Typography variant='body2' color='text.secondary'>
                        Custom
                      </Typography>
                    )}
                  </td>
                  <td className='!plb-1'>
                    {onStatusChange ? (
                      <div className='flex items-center' style={{ minWidth: '80px' }}>
                        <Tooltip title={getActiveStatus(config) ? 'Deactivate' : 'Activate'}>
                          <span>
                            <Switch
                              checked={getActiveStatus(config)}
                              onChange={() => handleStatusToggle(config.id as string, getActiveStatus(config))}
                              disabled={updatingConfigs[config.id as string]}
                              color='primary'
                              size='small'
                            />
                          </span>
                        </Tooltip>
                        <div style={{ width: '20px', display: 'inline-flex', justifyContent: 'center' }}>
                          {updatingConfigs[config.id as string] && <CircularProgress size={16} />}
                        </div>
                      </div>
                    ) : (
                      <Typography color={getActiveStatus(config) ? 'success.main' : 'text.secondary'}>
                        {getActiveStatus(config) ? 'Active' : 'Inactive'}
                      </Typography>
                    )}
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-2'>
                      <Tooltip title='View'>
                        <IconButton size='small' component={Link} href={`/config?tab=config&id=${config.id}&mode=view`}>
                          <i className='ri-eye-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton size='small' component={Link} href={`/config?tab=config&id=${config.id}&mode=edit`}>
                          <i className='ri-pencil-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          color='error'
                          onClick={() =>
                            handleDeleteClick(config.id as string, `Configuration #${config.id?.slice(-6)}`)
                          }
                        >
                          <i className='ri-delete-bin-line'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className='text-center'>
                  <Typography>No configurations found</Typography>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
      >
        <DialogTitle id='delete-dialog-title'>Delete Configuration</DialogTitle>
        <DialogContent>
          <DialogContentText id='delete-dialog-description'>
            Are you sure you want to delete the configuration <strong>{deleteDialog.configName}</strong>? This action
            cannot be undone.
          </DialogContentText>
          {deleteError && (
            <Typography color='error' className='mt-4'>
              {deleteError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color='error'
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : null}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default ConfigTable
