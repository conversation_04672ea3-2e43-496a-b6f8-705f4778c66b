'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Type Imports
import type { ConfigEntity } from '@/types/entities'

// Component Imports
import ConfigTable from './ConfigTable'

const AllConfigs = () => {
  // States
  const [configs, setConfigs] = useState<ConfigEntity[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Fetch configs function
  const fetchConfigs = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/config')

      if (!response.ok) {
        throw new Error('Failed to fetch configurations')
      }

      const data = await response.json()

      // Handle paginated response structure
      if (data.data && Array.isArray(data.data)) {
        setConfigs(data.data)
      } else if (Array.isArray(data)) {
        setConfigs(data)
      } else {
        setConfigs([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching configurations')
    } finally {
      setLoading(false)
    }
  }

  // Fetch configs on component mount
  useEffect(() => {
    fetchConfigs()
  }, [])

  // Handle successful deletion
  const handleDeleteSuccess = (deletedConfigId: string) => {
    setNotification({
      show: true,
      message: 'Configuration deleted successfully',
      type: 'success'
    })

    // Update local state by removing the deleted config
    setConfigs(prevConfigs => prevConfigs.filter(config => config.id !== deletedConfigId))
  }

  // Handle config status change
  const handleStatusChange = async (id: string, active: boolean) => {
    try {
      // Create JSON payload for the API call
      const updateData = {
        active: active
      }

      // Make API call to update the config
      const response = await fetch(`/api/config/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to update configuration status')
      }

      // Show success notification
      setNotification({
        show: true,
        message: `Configuration ${active ? 'activated' : 'deactivated'} successfully`,
        type: 'success'
      })

      // Update the local state with the new status
      setConfigs(prevConfigs => prevConfigs.map(config => (config.id === id ? { ...config, active } : config)))
    } catch (error) {
      console.error('Error updating configuration status:', error)

      // Show error notification
      setNotification({
        show: true,
        message: error instanceof Error ? error.message : 'Failed to update configuration status',
        type: 'error'
      })

      // Don't rethrow the error - let the UI handle it gracefully
      // The ConfigTable component will keep the toggle in the new state
    }
  }

  return (
    <Card>
      <CardHeader
        title='All Configurations'
        action={
          <Button
            variant='contained'
            startIcon={<i className='ri-add-line'></i>}
            component={Link}
            href='/config?tab=config'
          >
            Add Configuration
          </Button>
        }
      />
      <CardContent>
        <ConfigTable
          configs={configs}
          loading={loading}
          error={error}
          onDeleteSuccess={handleDeleteSuccess}
          onStatusChange={handleStatusChange}
        />
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default AllConfigs
