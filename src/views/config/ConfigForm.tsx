'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Snackbar,
  Switch,
  TextField,
  Typography
} from '@mui/material'

import type { ConfigEntity, UserEntity } from '@/types/entities'
import ImageCropper from '@/components/ImageCropper'

// Empty config template for new configs
export const emptyConfig: ConfigEntity = {
  theme: 'light',
  language: 'en',
  websiteUrl: '',
  active: true,
  isDefault: false,
  isEmailReceivedOn: true,
  isSmsReceivedOn: false,
  isPushNotificationReceivedOn: false,
  isVisitorLogOn: false,
  isResumeVisibilityOn: true,
  githubURL: '',
  leetcodeURL: '',
  linkedInURL: '',
  image: '',

  // EmailJS Configuration
  emailjsServiceId: '',
  emailjsTemplateId: '',
  emailjsPublicKey: '',
  emailjsPrivateKey: '',

  // Cloudinary Configuration
  cloudinaryCloudName: '',
  cloudinaryApiKey: '',
  cloudinaryApiSecret: '',

  user: undefined // Will be set when user is selected
}

const ConfigForm = () => {
  const [formData, setFormData] = useState<ConfigEntity>(emptyConfig)
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(false)
  const [users, setUsers] = useState<UserEntity[]>([])
  const [usersLoading, setUsersLoading] = useState(false)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }>({ show: false, message: '', type: 'info' })

  // Image upload states
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  const router = useRouter()
  const searchParams = useSearchParams()

  // Get URL parameters
  const configId = searchParams.get('id')
  const mode = searchParams.get('mode') // 'edit', 'view', or null (create)

  // Determine the current mode
  const isEditMode = mode === 'edit' && !!configId
  const isViewMode = mode === 'view' && !!configId
  const isCreateMode = !mode && !configId

  // Theme options
  const themeOptions = ['light', 'dark', 'auto']

  // Language options
  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'bn', label: 'Bengali' }
  ]

  // Fetch users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setUsersLoading(true)
        const response = await fetch('/api/user')

        if (!response.ok) {
          throw new Error('Failed to fetch users')
        }

        const data = await response.json()

        // Handle both single user and array of users
        setUsers(Array.isArray(data) ? data : [data])
      } catch (error) {
        console.error('Error fetching users:', error)
        setNotification({
          show: true,
          message: 'Failed to fetch users',
          type: 'error'
        })
      } finally {
        setUsersLoading(false)
      }
    }

    fetchUsers()
  }, [])

  // Fetch config data when in edit or view mode
  useEffect(() => {
    const fetchConfig = async () => {
      if (!configId || isCreateMode) return

      try {
        setFetchLoading(true)
        const response = await fetch(`/api/config/${configId}`)

        if (!response.ok) {
          throw new Error('Failed to fetch configuration')
        }

        const data = await response.json()

        console.log('Fetched config data:', data)
        console.log('User field type:', typeof data.user)
        console.log('User field value:', data.user)
        setFormData(data)

        // Set image if available
        if (data.image) {
          setImgSrc(data.image)
        }
      } catch (error) {
        console.error('Error fetching config:', error)
        setNotification({
          show: true,
          message: 'Failed to fetch configuration',
          type: 'error'
        })
      } finally {
        setFetchLoading(false)
      }
    }

    fetchConfig()
  }, [configId, isCreateMode])

  // Resolve user object when both users and formData are loaded
  useEffect(() => {
    console.log('User resolution effect triggered')
    console.log('Users loaded:', users.length)
    console.log('FormData user:', formData.user)
    console.log('FormData user type:', typeof formData.user)

    if (users.length > 0 && formData.user && typeof formData.user === 'string') {
      console.log('Looking for user with ID:', formData.user)
      const userObject = users.find(user => user.id === formData.user)

      console.log('Found user object:', userObject)

      if (userObject) {
        console.log('Setting user object in form data')
        setFormData(prev => ({ ...prev, user: userObject }))
      }
    }
  }, [users, formData.user])

  // Validate website URL format
  const validateWebsiteUrl = (url: string): boolean => {
    if (!url) return true // Optional field
    const urlPattern = /^https?:\/\/(?:[-\w.])+(?::[0-9]+)?(?:\/.*)?$/

    return urlPattern.test(url)
  }

  // Image upload handlers
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      setImageFile(files[0])

      // Update file input value
      setFileInput(event.target.value)

      // Create preview
      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setImageFile(null)
  }

  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setImageFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof ConfigEntity, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate that a user is selected
    if (!formData.user || (typeof formData.user === 'object' && !formData.user.id)) {
      setNotification({
        show: true,
        message: 'Please select a user for this configuration',
        type: 'error'
      })

      return
    }

    // Validate website URL format
    if (formData.websiteUrl && !validateWebsiteUrl(formData.websiteUrl)) {
      setNotification({
        show: true,
        message: 'Please enter a valid website URL (e.g., https://example.com or https://localhost:4000)',
        type: 'error'
      })

      return
    }

    setLoading(true)

    try {
      const url = isEditMode ? `/api/config/${configId}` : '/api/config'
      const method = isEditMode ? 'PATCH' : 'POST'

      // Create FormData for file upload
      const formDataToSend = new FormData()

      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'user') {
          // Only send user ID, not full user object
          // @ts-ignore
          const userId = typeof value === 'object' ? value?.id : value

          if (userId) {
            formDataToSend.append(key, userId)
          }
        } else if (typeof value === 'boolean') {
          // Explicitly handle boolean values
          formDataToSend.append(key, String(value))
        } else if (value !== null && value !== undefined && value !== '') {
          formDataToSend.append(key, String(value))
        }
      })

      // Add image file if present
      if (imageFile) {
        formDataToSend.append('image', imageFile)
      }

      const response = await fetch(url, {
        method,
        body: formDataToSend // Don't set Content-Type header, let browser set it for FormData
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || `Failed to ${isEditMode ? 'update' : 'create'} configuration`)
      }

      setNotification({
        show: true,
        message: `Configuration ${isEditMode ? 'updated' : 'created'} successfully!`,
        type: 'success'
      })

      // Reset form only if creating
      if (isCreateMode) {
        setFormData(emptyConfig)
      }

      // Redirect to config list after a short delay
      setTimeout(() => {
        router.push('/config?tab=all')
      }, 1500)
    } catch (error) {
      setNotification({
        show: true,
        message: error instanceof Error ? error.message : `Failed to ${isEditMode ? 'update' : 'create'} configuration`,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // Get dynamic title and subheader based on mode
  const getTitle = () => {
    if (isViewMode) return 'View Configuration'
    if (isEditMode) return 'Edit Configuration'

    return 'Add New Configuration'
  }

  const getSubheader = () => {
    if (isViewMode) return 'View configuration details'
    if (isEditMode) return 'Update configuration settings'

    return 'Create a new configuration with your preferred settings'
  }

  // Show loading state while fetching config data
  if (fetchLoading) {
    return (
      <Card>
        <CardContent className='flex justify-center items-center py-12'>
          <div className='text-center'>
            <div className='mb-4'>Loading configuration...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title={getTitle()} subheader={getSubheader()} />
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={5}>
            {/* User Selection */}
            <Grid item xs={12}>
              <FormControl fullWidth margin='normal' required>
                <InputLabel id='user-label'>Select User</InputLabel>
                <Select
                  labelId='user-label'
                  id='user'
                  value={typeof formData.user === 'object' ? formData.user?.id || '' : formData.user || ''}
                  label='Select User'
                  onChange={e => {
                    const selectedUser = users.find(user => user.id === e.target.value)

                    handleFormChange('user', selectedUser)
                  }}
                  disabled={usersLoading || isViewMode}
                >
                  {usersLoading ? (
                    <MenuItem disabled>Loading users...</MenuItem>
                  ) : users.length === 0 ? (
                    <MenuItem disabled>No users available</MenuItem>
                  ) : (
                    users.map(user => (
                      <MenuItem key={user.id} value={user.id}>
                        <div className='flex items-center gap-3'>
                          {user.profileImage ? (
                            <img
                              src={user.profileImage}
                              alt={user.name}
                              width={24}
                              height={24}
                              className='rounded-full object-cover'
                            />
                          ) : (
                            <div
                              className='flex items-center justify-center bg-primary-lightOpacity text-primary rounded-full'
                              style={{ width: 24, height: 24, fontSize: '12px' }}
                            >
                              {user.name?.charAt(0) || 'U'}
                            </div>
                          )}
                          <div>
                            <div className='font-medium'>{user.name}</div>
                            <div className='text-sm text-gray-500'>{user.email}</div>
                          </div>
                        </div>
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            </Grid>

            {/* Theme Selection */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal'>
                <InputLabel id='theme-label'>Theme</InputLabel>
                <Select
                  labelId='theme-label'
                  id='theme'
                  value={formData.theme}
                  label='Theme'
                  onChange={e => handleFormChange('theme', e.target.value)}
                  disabled={isViewMode}
                >
                  {themeOptions.map(theme => (
                    <MenuItem key={theme} value={theme}>
                      {theme.charAt(0).toUpperCase() + theme.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Language Selection */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal'>
                <InputLabel id='language-label'>Language</InputLabel>
                <Select
                  labelId='language-label'
                  id='language'
                  value={formData.language}
                  label='Language'
                  onChange={e => handleFormChange('language', e.target.value)}
                  disabled={isViewMode}
                >
                  {languageOptions.map(lang => (
                    <MenuItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Website URL */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Website URL'
                placeholder='https://localhost:4000 or https://example.com'
                value={formData.websiteUrl || ''}
                onChange={e => handleFormChange('websiteUrl', e.target.value)}
                disabled={isViewMode}
                helperText={
                  formData.websiteUrl && !validateWebsiteUrl(formData.websiteUrl)
                    ? 'Please enter a valid URL starting with http:// or https://'
                    : 'Unique website URL for displaying user data (supports localhost for development)'
                }
                error={formData.websiteUrl ? !validateWebsiteUrl(formData.websiteUrl) : false}
                margin='normal'
                type='url'
              />
            </Grid>

            {/* Social Media URLs */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                Social Media URLs
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='GitHub URL'
                placeholder='https://github.com/username'
                value={formData.githubURL || ''}
                onChange={e => handleFormChange('githubURL', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                type='url'
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='LeetCode URL'
                placeholder='https://leetcode.com/username'
                value={formData.leetcodeURL || ''}
                onChange={e => handleFormChange('leetcodeURL', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                type='url'
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='LinkedIn URL'
                placeholder='https://linkedin.com/in/username'
                value={formData.linkedInURL || ''}
                onChange={e => handleFormChange('linkedInURL', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                type='url'
              />
            </Grid>

            {/* Image Upload Section */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                Profile Image
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <div className='flex max-sm:flex-col items-center gap-6'>
                <img height={100} width={100} className='rounded' src={imgSrc} alt='Config Image' />
                <div className='flex flex-grow flex-col gap-4'>
                  <div className='flex flex-col sm:flex-row gap-4'>
                    <Button
                      component='label'
                      size='small'
                      variant='contained'
                      htmlFor='config-image-upload'
                      disabled={isViewMode}
                    >
                      Upload Image
                      <input
                        hidden
                        type='file'
                        value={fileInput}
                        accept='image/png, image/jpeg, image/svg+xml'
                        onChange={handleFileInputChange}
                        id='config-image-upload'
                      />
                    </Button>
                    <Button
                      size='small'
                      variant='outlined'
                      color='primary'
                      onClick={handleOpenCropper}
                      disabled={!imgSrc || imgSrc === '/images/avatars/default.png' || isViewMode}
                    >
                      Crop Image
                    </Button>
                    <Button
                      size='small'
                      variant='outlined'
                      color='error'
                      onClick={handleFileInputReset}
                      disabled={isViewMode}
                    >
                      Reset
                    </Button>
                  </div>
                  <Typography>Allowed JPG, GIF, PNG or SVG. Max size of 800K</Typography>
                </div>
              </div>
            </Grid>
            {/* Configuration Switches */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                General Settings
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.active}
                    onChange={e => handleFormChange('active', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='Active Configuration'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isDefault}
                    onChange={e => handleFormChange('isDefault', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='Default Configuration'
              />
            </Grid>

            {/* Notification Settings */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                Notification Settings
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isEmailReceivedOn}
                    onChange={e => handleFormChange('isEmailReceivedOn', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='Email Notifications'
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isSmsReceivedOn}
                    onChange={e => handleFormChange('isSmsReceivedOn', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='SMS Notifications'
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isPushNotificationReceivedOn}
                    onChange={e => handleFormChange('isPushNotificationReceivedOn', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='Push Notifications'
              />
            </Grid>

            {/* Privacy Settings */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                Privacy Settings
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isVisitorLogOn}
                    onChange={e => handleFormChange('isVisitorLogOn', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='Visitor Logging'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isResumeVisibilityOn}
                    onChange={e => handleFormChange('isResumeVisibilityOn', e.target.checked)}
                    disabled={isViewMode}
                  />
                }
                label='Resume Visibility'
              />
            </Grid>

            {/* EmailJS Configuration */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                EmailJS Configuration
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='EmailJS Service ID'
                placeholder='service_xxxxxxx'
                value={formData.emailjsServiceId || ''}
                onChange={e => handleFormChange('emailjsServiceId', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                helperText='Your EmailJS service ID for sending emails'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='EmailJS Template ID'
                placeholder='template_xxxxxxx'
                value={formData.emailjsTemplateId || ''}
                onChange={e => handleFormChange('emailjsTemplateId', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                helperText='Your EmailJS template ID for email formatting'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='EmailJS Public Key'
                placeholder='Your EmailJS public key'
                value={formData.emailjsPublicKey || ''}
                onChange={e => handleFormChange('emailjsPublicKey', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                helperText='Your EmailJS public key (safe to expose in frontend)'
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='EmailJS Private Key'
                placeholder='Your EmailJS private key'
                value={formData.emailjsPrivateKey || ''}
                onChange={e => handleFormChange('emailjsPrivateKey', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                type='password'
                helperText='Your EmailJS private key (keep secure)'
              />
            </Grid>

            {/* Cloudinary Configuration */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom sx={{ mt: 2 }}>
                Cloudinary Configuration
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='Cloudinary Cloud Name'
                placeholder='your-cloud-name'
                value={formData.cloudinaryCloudName || ''}
                onChange={e => handleFormChange('cloudinaryCloudName', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                helperText='Your Cloudinary cloud name'
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='Cloudinary API Key'
                placeholder='123456789012345'
                value={formData.cloudinaryApiKey || ''}
                onChange={e => handleFormChange('cloudinaryApiKey', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                helperText='Your Cloudinary API key'
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='Cloudinary API Secret'
                placeholder='Your Cloudinary API secret'
                value={formData.cloudinaryApiSecret || ''}
                onChange={e => handleFormChange('cloudinaryApiSecret', e.target.value)}
                disabled={isViewMode}
                margin='normal'
                type='password'
                helperText='Your Cloudinary API secret (keep secure)'
              />
            </Grid>
          </Grid>

          {/* Form Buttons */}
          <div className='flex gap-4 flex-wrap mt-6'>
            {!isViewMode && (
              <Button variant='contained' type='submit' disabled={loading}>
                {loading
                  ? isEditMode
                    ? 'Updating...'
                    : 'Saving...'
                  : isEditMode
                    ? 'Update Configuration'
                    : 'Save Configuration'}
              </Button>
            )}

            {isCreateMode && (
              <Button
                variant='outlined'
                type='button'
                color='secondary'
                onClick={() => setFormData(emptyConfig)}
                disabled={loading}
              >
                Reset
              </Button>
            )}

            {isEditMode && (
              <Button
                variant='outlined'
                type='button'
                color='info'
                onClick={() => router.push(`/config?tab=config&id=${configId}&mode=view`)}
                disabled={loading}
              >
                View Only
              </Button>
            )}

            {isViewMode && (
              <Button
                variant='outlined'
                type='button'
                color='primary'
                onClick={() => router.push(`/config?tab=config&id=${configId}&mode=edit`)}
                disabled={loading}
              >
                Edit Configuration
              </Button>
            )}

            <Button
              variant='outlined'
              color={isViewMode ? 'primary' : 'secondary'}
              onClick={() => router.push('/config?tab=all')}
              disabled={loading}
            >
              {isViewMode ? 'Back to List' : 'Cancel'}
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      <ImageCropper
        image={tempImgSrc}
        open={cropperOpen}
        onClose={() => setCropperOpen(false)}
        onCropComplete={handleCroppedImage}
      />
    </Card>
  )
}

export default ConfigForm
