'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { type SubmitHandler, useForm } from 'react-hook-form'

import type { SkillEntity } from '@/types/entities'

// Empty skill template for new skills
export const emptySkill: SkillEntity = {
  name: '',
  logo: '',
  description: '',
  achievedPercentage: 0,
  tags: [],
  level: 'Beginner',
  active: false
}

export const useSkillsForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const skillId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && skillId)

  // States
  const [formData, setFormData] = useState<SkillEntity>(emptySkill)
  const [originalData, setOriginalData] = useState<SkillEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    setValue,
    formState: { errors }
  } = useForm<SkillEntity>({
    defaultValues: emptySkill
  })

  // Fetch skill data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && skillId) {
      const fetchSkill = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/skill/${skillId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch skill')
          }

          const data = await response.json()

          // Store original data for comparison when updating
          setOriginalData(data)

          // Ensure achievedPercentage is a number
          if (data.achievedPercentage) {
            data.achievedPercentage = Number(data.achievedPercentage)
          }

          setFormData(data)

          // Reset form with fetched data
          reset(data, {
            keepDirty: false,
            keepErrors: false,
            keepIsSubmitted: false,
            keepTouched: false,
            keepIsValid: false,
            keepSubmitCount: false
          })

          // Explicitly set form values
          setValue('achievedPercentage', Number(data.achievedPercentage), {
            shouldValidate: true,
            shouldDirty: true
          })

          // Explicitly set tags value
          if (data.tags) {
            setValue('tags', data.tags, {
              shouldValidate: true,
              shouldDirty: true
            })
          }

          // Explicitly set description value
          if (data.description) {
            setValue('description', data.description, {
              shouldValidate: true,
              shouldDirty: true
            })
          }

          // Explicitly set level value
          if (data.level) {
            setValue('level', data.level, {
              shouldValidate: true,
              shouldDirty: true
            })
          }

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }
        } catch (err) {
          console.error('Error fetching skill:', err)
          setNotification({
            show: true,
            message: 'Failed to load skill data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchSkill()
    }
  }, [skillId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Effect to sync form state with form library
  useEffect(() => {
    // Ensure achievedPercentage is properly set in the form
    if (typeof formData.achievedPercentage === 'number' && formData.achievedPercentage > 0) {
      setValue('achievedPercentage', formData.achievedPercentage, {
        shouldValidate: true,
        shouldDirty: true
      })
    }

    // Ensure tags are properly set in the form
    if (formData.tags && formData.tags.length > 0) {
      setValue('tags', formData.tags, {
        shouldValidate: true,
        shouldDirty: true
      })
    }

    // Ensure description is properly set in the form
    if (formData.description) {
      setValue('description', formData.description, {
        shouldValidate: true,
        shouldDirty: true
      })
    }

    // Ensure level is properly set in the form
    if (formData.level) {
      setValue('level', formData.level, {
        shouldValidate: true,
        shouldDirty: true
      })
    }
  }, [formData, setValue])

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(file)
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof SkillEntity, value: any) => {
    // Process value based on field type
    let processedValue = value

    // Ensure achievedPercentage is a number
    if (field === 'achievedPercentage') {
      processedValue = Number(value)
    }

    // Update the form state
    const updatedData = { ...formData, [field]: processedValue }

    setFormData(updatedData)

    // Directly set the value in the form for all fields
    setValue(field, processedValue, {
      shouldValidate: true,
      shouldDirty: true
    })

    // Force update for level field
    if (field === 'level') {
      // Force a re-render to ensure the UI updates
      setTimeout(() => {
        setValue('level', processedValue, {
          shouldValidate: true,
          shouldDirty: true
        })
      }, 0)
    }
  }

  // Handle form submission
  const onSubmit: SubmitHandler<SkillEntity> = async data => {
    try {
      setLoading(true)

      // Ensure achievedPercentage is a number
      let achievedPercentage = Number(data.achievedPercentage)

      // If the value is NaN or 0, use the value from the form state
      if (isNaN(achievedPercentage) || achievedPercentage === 0) {
        achievedPercentage = Number(formData.achievedPercentage)
      }

      if (isNaN(achievedPercentage)) {
        throw new Error('Proficiency must be a valid number')
      }

      // Ensure tags are included
      let tagsValue = data.tags || []

      if ((!tagsValue || tagsValue.length === 0) && formData.tags && formData.tags.length > 0) {
        tagsValue = formData.tags
      }

      // Ensure description is included
      let descriptionValue = data.description

      if (!descriptionValue && formData.description) {
        descriptionValue = formData.description
      }

      // Ensure level is included
      let levelValue = data.level

      if (!levelValue && formData.level) {
        levelValue = formData.level
      }

      // Create FormData object
      const formDataObj = new FormData()

      // Add all form fields to FormData
      for (const key in data) {
        if (key !== 'logo') {
          // Handle special fields
          if (key === 'achievedPercentage') {
            formDataObj.append(key, achievedPercentage.toString())
          } else if (key === 'tags') {
            // Convert tags array to JSON string
            formDataObj.append(key, JSON.stringify(tagsValue || []))
          } else if (key === 'description') {
            formDataObj.append(key, descriptionValue || '')
          } else if (key === 'level') {
            // Always use the value from formData for level
            const levelToUse = formData.level || levelValue || 'Beginner'

            formDataObj.append(key, levelToUse)
          } else {
            formDataObj.append(key, String(data[key as keyof SkillEntity]))
          }
        }
      }

      // Add logo file if selected
      if (logoFile) {
        formDataObj.append('logo', logoFile)
      }

      let url = '/api/skill'
      let method = 'POST'

      if (isEditMode && skillId) {
        url = `/api/skill/${skillId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formDataObj
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save skill')
      }

      const resp = await res.json()

      setNotification({
        show: true,
        message: isEditMode ? 'Skill updated successfully' : 'Skill created successfully',
        type: 'success'
      })

      // Redirect to skill list after successful save
      setTimeout(() => {
        router.push('/skills?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save skill'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    setFormData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    skillId,
    register,
    control,
    setValue,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
