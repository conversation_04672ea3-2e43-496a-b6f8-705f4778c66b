'use client'

import { useEffect, useState } from 'react'

import { Box } from '@mui/material'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'
import Chip from '@mui/material/Chip'
import Autocomplete from '@mui/material/Autocomplete'
import IconButton from '@mui/material/IconButton'
import Checkbox from '@mui/material/Checkbox'
import FormControlLabel from '@mui/material/FormControlLabel'

import SimpleCropper from '@/components/SimpleCropper'
import { useProjectsForm } from './hooks/useProjectsForm'

// Type Imports
import type { SkillEntity } from '@/types/entities'

const ProjectsForm = () => {
  const {
    formData,
    fileInput,
    imgSrc,
    coverImageFiles,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    handleRemoveImage,
    handleDeleteExistingImage,
    setCropperOpen,
    setNotification,
    reset,
    setValue, // Add setValue to the destructured props
    router
  } = useProjectsForm()

  console.log({ formData })

  // State for skills
  const [availableSkills, setAvailableSkills] = useState<SkillEntity[]>([])
  const [selectedSkills, setSelectedSkills] = useState<SkillEntity[]>([])

  // Register skill field with validation
  useEffect(() => {
    register('skill', {
      required: true,
      validate: value => {
        // Check if value is an array with at least one item
        if (Array.isArray(value) && value.length > 0) {
          return true
        }

        // If no value but we have selected skills, use those instead
        if (selectedSkills.length > 0) {
          const skillIds = selectedSkills.map(skill => skill.id)

          // Update the form value
          // @ts-ignore
          setValue('skill', skillIds, { shouldValidate: false })

          return true
        }

        return false
      }
    })
  }, [register, selectedSkills, setValue])

  // Fetch available skills
  useEffect(() => {
    const fetchSkills = async () => {
      try {
        const response = await fetch('/api/skill')

        if (response.ok) {
          const data = await response.json()

          // Ensure data is an array and has the expected structure
          const skillsArray = Array.isArray(data) ? data : data?.data || []

          setAvailableSkills(skillsArray)

          // If we already have form data with skills and they weren't processed yet,
          // process them now that we have the available skills
          if (formData.skill && selectedSkills.length === 0) {
            processSkillData(formData.skill, skillsArray)
          }
        }
      } catch (error) {
        console.error('Error fetching skills:', error)
        setAvailableSkills([]) // Set empty array on error
      }
    }

    fetchSkills()
  }, [formData.skill, selectedSkills.length])

  // Helper function to process skill data in different formats
  const processSkillData = (skillData: any, skills: SkillEntity[]) => {
    console.log('Processing skill data:', skillData)
    if (!skillData) return

    try {
      // Handle array of skill objects or IDs
      if (Array.isArray(skillData) && skillData.length > 0) {
        let skillObjects: SkillEntity[] = []

        // Case 1: Array of skill objects with id or _id property
        if (typeof skillData[0] === 'object') {
          // Check if these are MongoDB objects with _id
          if (skillData[0]?._id) {
            console.log('Found MongoDB skill objects with _id')

            // Convert _id to id for compatibility
            skillObjects = skillData.map(skill => ({
              ...skill,
              id: skill._id // Use _id as id
            })) as SkillEntity[]
            console.log('Converted MongoDB skill objects:', skillObjects)
          }

          // Check if these objects have id property
          else if (skillData[0]?.id) {
            // Check if these objects have name property (full skill objects)
            if (skillData[0]?.name) {
              skillObjects = skillData as SkillEntity[]
              console.log('Found complete skill objects with id:', skillObjects)
            } else {
              // Objects with just IDs, need to find the full objects
              skillObjects = skillData
                .map(skillObj => skills.find(skill => skill.id === skillObj.id))
                .filter(Boolean) as SkillEntity[]
              console.log('Mapped skill ID objects to full objects:', skillObjects)
            }
          }
        }

        // Case 2: Array of skill IDs as strings
        else if (typeof skillData[0] === 'string') {
          skillObjects = skillData
            .map(skillId => skills.find(skill => skill.id === skillId))
            .filter(Boolean) as SkillEntity[]
          console.log('Mapped skill IDs to objects:', skillObjects)
        }

        if (skillObjects.length > 0) {
          console.log('Setting selected skills to:', skillObjects)

          // Update the UI with full skill objects
          setSelectedSkills(skillObjects)

          // Update the form value with skill IDs
          const skillIds = skillObjects.map(skill => skill.id)

          // @ts-ignore
          setValue('skill', skillIds, { shouldValidate: true })
        } else {
          console.warn('No matching skill objects found!')
        }
      }

      // Handle single skill object
      else if (skillData && typeof skillData === 'object' && !Array.isArray(skillData)) {
        // Check if it's a MongoDB object with _id
        if (skillData._id) {
          const skillWithId = {
            ...skillData,
            id: skillData._id // Use _id as id
          } as SkillEntity

          setSelectedSkills([skillWithId])
          // @ts-ignore
          setValue('skill', [skillWithId.id], { shouldValidate: true })
          console.log('Set single MongoDB skill object:', skillWithId)
        }

        // Check if it has an id property
        else if (skillData.id) {
          // If it's a full skill object with name
          if (skillData.name) {
            setSelectedSkills([skillData as SkillEntity])
            setValue('skill', [skillData.id], { shouldValidate: true })
            console.log('Set single complete skill object:', skillData)
          } else {
            // If it's just an ID object, find the full object
            const matchedSkill = skills.find(s => s.id === skillData.id)

            if (matchedSkill) {
              setSelectedSkills([matchedSkill])
              // @ts-ignore
              setValue('skill', [matchedSkill.id], { shouldValidate: true })
              console.log('Matched single skill ID object to full object:', matchedSkill)
            }
          }
        }
      }

      // Handle single skill ID as string
      else if (typeof skillData === 'string') {
        const matchedSkill = skills.find(s => s.id === skillData)

        if (matchedSkill) {
          setSelectedSkills([matchedSkill])
          // @ts-ignore
          setValue('skill', [matchedSkill.id], { shouldValidate: true })
          console.log('Matched single skill ID string to full object:', matchedSkill)
        }
      }
    } catch (error) {
      console.error('Error processing skill data:', error)
    }
  }

  // Update selected skills when form data changes or available skills load
  useEffect(() => {
    if (formData.skill && availableSkills.length > 0) {
      processSkillData(formData.skill, availableSkills)
    }
  }, [formData.skill, availableSkills])

  // Cleanup image preview URLs when component unmounts or when files change
  useEffect(() => {
    // Create an array to store all the preview URLs
    const previewUrls: string[] = []

    // Return a cleanup function
    return () => {
      // Revoke all object URLs to avoid memory leaks
      previewUrls.forEach(url => URL.revokeObjectURL(url))
    }
  }, [coverImageFiles])

  // Check for skills on component mount
  useEffect(() => {
    // If we have both form data with skills and available skills, process them
    if (formData.skill && availableSkills.length > 0 && selectedSkills.length === 0) {
      processSkillData(formData.skill, availableSkills)
    }
  }, [])

  // Handle skill selection change
  const handleSkillChange = (event: React.SyntheticEvent, newValue: SkillEntity[]) => {
    console.log('handleSkillChange called with:', newValue)

    // Check if skills were cleared (empty array)
    if (!newValue || newValue.length === 0) {
      console.log('All skills cleared')

      // Clear selected skills in UI
      setSelectedSkills([])

      // Clear skills in form data
      handleFormChange('skill', [])

      // Clear skills in form validation
      setValue('skill', [], { shouldValidate: true })

      return
    }

    // Make sure all skills have an ID (either id or _id)
    const validSkills = newValue.filter(skill => skill && skill.id)

    // Convert any skills with _id to have id property
    const normalizedSkills = validSkills.map(skill => {
      if (skill.id && !skill.id) {
        return { ...skill, id: skill.id }
      }

      return skill
    })

    console.log('Normalized skills:', normalizedSkills)

    // Update the UI state with full skill objects
    setSelectedSkills(normalizedSkills)

    // Only pass the skill IDs to the form data
    const skillIds = normalizedSkills.map(skill => skill.id)

    console.log('Selected skill IDs:', skillIds)

    // Update the form data with just the IDs
    handleFormChange('skill', skillIds)

    // IMPORTANT: This is the key fix - manually set the value in react-hook-form
    // This ensures the validation recognizes the selected skills
    if (skillIds.length > 0) {
      // Use the setValue function from useProjectsForm
      // This will properly register the value with react-hook-form
      // @ts-ignore
      setValue('skill', skillIds, { shouldValidate: true })
    } else {
      // If we somehow end up with no valid skills, clear the form value
      setValue('skill', [], { shouldValidate: true })
    }
  }

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Project' : 'Add New Project'} />
        <CardContent>
          <Typography>Loading project data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Project' : 'Add New Project'}
        subheader={isEditMode ? 'Update project information' : 'Create a new project'}
      />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-start gap-6'>
          <div className='flex flex-col gap-4'>
            <Typography variant='subtitle1'>Project Cover Images</Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              {/* Display existing images from the database */}
              {formData.coverImages &&
                formData.coverImages.length > 0 &&
                formData.coverImages.map((image, index) => (
                  <Box key={`existing-${index}`} sx={{ position: 'relative' }}>
                    <img height={100} width={100} className='rounded' src={image} alt={`Project Cover ${index + 1}`} />
                    {isEditMode && (
                      <IconButton
                        size='small'
                        color='error'
                        onClick={() => handleDeleteExistingImage(image)}
                        sx={{
                          position: 'absolute',
                          top: -10,
                          right: -10,
                          backgroundColor: 'white',
                          '&:hover': { backgroundColor: '#f5f5f5' }
                        }}
                      >
                        <i className='ri-close-circle-fill'></i>
                      </IconButton>
                    )}
                  </Box>
                ))}

              {/* Display newly uploaded images */}
              {coverImageFiles.length > 0 &&
                coverImageFiles.map((file, index) => {
                  // Create a preview URL for the file
                  const previewUrl = URL.createObjectURL(file)

                  return (
                    <Box key={`new-${index}`} sx={{ position: 'relative' }}>
                      <img
                        height={100}
                        width={100}
                        className='rounded'
                        src={previewUrl}
                        alt={`New Upload ${index + 1}`}
                      />
                      <IconButton
                        size='small'
                        color='error'
                        onClick={() => handleRemoveImage(index)}
                        sx={{
                          position: 'absolute',
                          top: -10,
                          right: -10,
                          backgroundColor: 'white',
                          '&:hover': { backgroundColor: '#f5f5f5' }
                        }}
                      >
                        <i className='ri-close-circle-fill'></i>
                      </IconButton>
                    </Box>
                  )
                })}

              {/* Show default image if no images */}
              {formData.coverImages?.length === 0 && coverImageFiles.length === 0 && (
                <img height={100} width={100} className='rounded' src={imgSrc} alt='Project Cover' />
              )}
            </Box>
          </div>
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button component='label' size='small' variant='contained' htmlFor='project-cover-upload'>
                Upload Project Cover
                <input
                  hidden
                  type='file'
                  multiple
                  value={fileInput}
                  accept='image/png, image/jpeg'
                  onChange={handleFileInputChange}
                  id='project-cover-upload'
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='primary'
                onClick={handleOpenCropper}
                disabled={!imgSrc || imgSrc === '/images/avatars/default.png'}
              >
                Crop Image
              </Button>
              <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                Reset
              </Button>
            </div>
            <Typography variant='caption'>
              Allowed JPG, GIF or PNG. Max size of 800K. You can upload multiple images.
            </Typography>
            {coverImageFiles.length > 0 && (
              <Typography variant='caption' color='primary'>
                {coverImageFiles.length} new image{coverImageFiles.length > 1 ? 's' : ''} selected for upload
              </Typography>
            )}
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <form
          onSubmit={handleSubmit(data => {
            // Make sure form data is synchronized with the state

            // Ensure skill data is properly formatted
            if (selectedSkills.length > 0) {
              // Always use the selectedSkills from the UI state
              const skillIds = selectedSkills.map(skill => skill.id)

              // Update both the form data and the submission data
              // @ts-ignore
              data.skill = skillIds
              handleFormChange('skill', skillIds)
            } else if (!data.skill || !Array.isArray(data.skill) || data.skill.length === 0) {
              // If no skills are selected, set an empty array
              data.skill = []
              handleFormChange('skill', [])
            } else if (Array.isArray(data.skill) && data.skill.length > 0) {
              // If skills are already in the data but not in selectedSkills, process them
              // Extract IDs if they're objects
              const skillIds = data.skill
                .map((skill: any) => {
                  return typeof skill === 'string' ? skill : skill && skill.id ? skill.id : null
                })
                .filter(Boolean)

              data.skill = skillIds
              handleFormChange('skill', skillIds)
            }

            onSubmit(data)
          })}
        >
          <Grid container spacing={5}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Project Name'
                {...register('name', { required: true })}
                error={Boolean(errors.name)}
                helperText={errors.name ? 'Project name is required' : ''}
                value={formData.name || ''}
                onChange={e => handleFormChange('name', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type='url'
                label='Company Website'
                {...register('companyWebsite')}
                value={formData.companyWebsite || ''}
                onChange={e => handleFormChange('companyWebsite', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='Start Date'
                type='date'
                {...register('startedAt', { required: true })}
                error={Boolean(errors.startedAt)}
                helperText={errors.startedAt ? 'Start date is required' : ''}
                value={
                  formData.startedAt instanceof Date
                    ? formData.startedAt.toISOString().split('T')[0]
                    : formData.startedAt
                }
                onChange={e => handleFormChange('startedAt', e.target.value)}
                margin='normal'
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='End Date'
                type='date'
                {...register('endedAt', { required: true })}
                error={Boolean(errors.endedAt)}
                helperText={errors.endedAt ? 'End date is required' : ''}
                value={
                  formData.endedAt instanceof Date ? formData.endedAt.toISOString().split('T')[0] : formData.endedAt
                }
                onChange={e => handleFormChange('endedAt', e.target.value)}
                margin='normal'
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type='url'
                label='GitHub Link'
                {...register('githubLink')}
                value={formData.githubLink || ''}
                onChange={e => handleFormChange('githubLink', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label='Project Live Link'
                type='url'
                {...register('projectLiveLink')}
                value={formData.projectLiveLink || ''}
                onChange={e => handleFormChange('projectLiveLink', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                id='skills-select'
                options={Array.isArray(availableSkills) ? availableSkills : []}
                value={Array.isArray(selectedSkills) ? selectedSkills : []}
                onChange={handleSkillChange}
                getOptionLabel={option => {
                  console.log('getOptionLabel called with:', option)

                  return option?.name || ''
                }}
                isOptionEqualToValue={(option, value) => {
                  console.log('isOptionEqualToValue comparing:', option, value)

                  // Handle both id and _id for comparison
                  const optionId = option?.id
                  const valueId = value?.id

                  return optionId === valueId
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    label='Skills Used'
                    margin='normal'
                    error={Boolean(errors.skill)}
                    helperText={errors.skill ? 'At least one skill is required' : ''}
                  />
                )}
                renderTags={(value, getTagProps) => {
                  console.log('renderTags called with:', value)

                  return value.map((option, index) => (
                    // @ts-ignore
                    <Chip
                      label={option?.name || `Skill ${index + 1}`}
                      {...getTagProps({ index })}
                      key={option?.id || index}
                    />
                  ))
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Project Description'
                {...register('projectDescription', { required: true })}
                error={Boolean(errors.projectDescription)}
                helperText={errors.projectDescription ? 'Project description is required' : ''}
                multiline
                rows={3}
                value={formData.projectDescription || ''}
                onChange={e => handleFormChange('projectDescription', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Project Details'
                {...register('details')}
                multiline
                rows={5}
                value={formData.details || ''}
                onChange={e => handleFormChange('details', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    {...register('active')}
                    checked={formData.active || false}
                    onChange={e => handleFormChange('active', e.target.checked)}
                  />
                }
                label='Active Project'
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update Project' : 'Save Project'}
            </Button>
            <Button
              variant='outlined'
              type='reset'
              color='secondary'
              onClick={() => reset(formData)}
              disabled={loading}
            >
              Reset
            </Button>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => router.push('/projects?tab=all')}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default ProjectsForm
