'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { type SubmitHandler, useForm } from 'react-hook-form'

import type { ProjectEntity } from '@/types/entities'

// Empty projects template for new projects
export const emptyProject: ProjectEntity = {
  name: '',
  coverImages: [],
  projectDescription: '',
  startedAt: new Date(),
  endedAt: new Date(),
  githubLink: '',
  projectLiveLink: '',
  skill: [],
  companyWebsite: '',
  details: '',
  active: true,
  user: undefined
}

export const useProjectsForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const projectId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && projectId)

  // States
  const [formData, setFormData] = useState<ProjectEntity>(emptyProject)
  const [originalData, setOriginalData] = useState<ProjectEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [coverImageFiles, setCoverImageFiles] = useState<File[]>([])
  const [deletedImageUrls, setDeletedImageUrls] = useState<string[]>([])
  const [loading, setLoading] = useState<boolean>(false)

  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    setValue, // Add setValue to the destructured props
    formState: { errors }
  } = useForm<ProjectEntity>({
    defaultValues: emptyProject,
    mode: 'onBlur' // Change validation mode to prevent continuous validation
  })

  // Fetch projects data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && projectId) {
      const fetchProject = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/projects/${projectId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch projects')
          }

          const data = await response.json()

          // Log the raw data from API for debugging
          console.log('Raw project data from API:', JSON.stringify(data, null, 2))
          console.log('Skill data type:', typeof data.skill)
          console.log('Is skill an array?', Array.isArray(data.skill))

          // Format dates for form inputs
          if (data.startedAt) {
            const startDate = new Date(data.startedAt)

            data.startedAt = startDate.toISOString().split('T')[0]
          }

          if (data.endedAt) {
            const endDate = new Date(data.endedAt)

            data.endedAt = endDate.toISOString().split('T')[0]
          }

          // Process skill data if it exists
          if (data.skill) {
            console.log('Received skill data from API:', data.skill)

            // Ensure skill is always an array
            if (!Array.isArray(data.skill)) {
              if (data.skill && typeof data.skill === 'object') {
                // If it's a single object, convert to array
                data.skill = [data.skill]
              } else if (typeof data.skill === 'string') {
                // If it's a string (single ID), convert to array
                data.skill = [data.skill]
              } else {
                // Default to empty array if we can't determine the format
                data.skill = []
              }
            }

            console.log('Processed skill data:', data.skill)

            // Check if we have skill IDs but not full skill objects
            const hasOnlyIds =
              Array.isArray(data.skill) &&
              data.skill.length > 0 &&
              (typeof data.skill[0] === 'string' || (typeof data.skill[0] === 'object' && !data.skill[0].name))

            if (hasOnlyIds) {
              console.log('We have only skill IDs, fetching full skill objects...')

              // We'll fetch the skill details and update the form data
              fetchSkillDetails(data.skill)
            }
          }

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)
          reset(data)

          // Set image preview if available
          if (data.coverImages && data.coverImages.length > 0) {
            setImgSrc(data.coverImages[0])
          }
        } catch (err) {
          console.error('Error fetching projects:', err)
          setNotification({
            show: true,
            message: 'Failed to load projects data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchProject()
    }
  }, [projectId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Handle file input change for multiple images
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files

    if (files && files.length > 0) {
      // Add all selected files to the coverImageFiles array
      const newFiles = Array.from(files)

      setCoverImageFiles([...coverImageFiles, ...newFiles])
      setFileInput('') // Reset the input value to allow selecting the same file again

      // Use the first file for preview and cropping
      const reader = new FileReader()

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], `cropped-image-${Date.now()}.jpg`, { type: 'image/jpeg' })

    // Store the cropped file for FormData - add to existing files
    setCoverImageFiles([...coverImageFiles, croppedFile])

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setCoverImageFiles([])
  }

  // Remove a specific image from the coverImageFiles array (new uploads)
  const handleRemoveImage = (index: number) => {
    const newFiles = [...coverImageFiles]

    newFiles.splice(index, 1)
    setCoverImageFiles(newFiles)
  }

  // Mark an existing image for deletion
  const handleDeleteExistingImage = (imageUrl: string) => {
    // Add the URL to the list of deleted images
    setDeletedImageUrls([...deletedImageUrls, imageUrl])

    // Update the formData to remove this image
    if (formData.coverImages) {
      const updatedImages = formData.coverImages.filter(url => url !== imageUrl)

      handleFormChange('coverImages', updatedImages)
    }
  }

  // Fetch skill details when we only have IDs
  const fetchSkillDetails = async (skillData: any[]) => {
    try {
      // Extract IDs from the skill data
      const skillIds = skillData
        .map(skill => {
          if (typeof skill === 'string') return skill
          if (typeof skill === 'object' && skill.id) return skill.id

          return null
        })
        .filter(Boolean)

      console.log('Fetching details for skill IDs:', skillIds)

      if (skillIds.length === 0) return

      // Fetch all skills first
      const response = await fetch('/api/skill')

      if (!response.ok) {
        throw new Error('Failed to fetch skills')
      }

      const allSkills = await response.json()

      console.log('Fetched all skills:', allSkills)

      // Find the matching skills
      const matchedSkills = allSkills.filter((skill: any) => skillIds.includes(skill.id))

      console.log('Matched skills:', matchedSkills)

      if (matchedSkills.length > 0) {
        // Update the form data with the full skill objects
        setFormData(prev => ({
          ...prev,
          skill: matchedSkills
        }))

        // Also update the form values
        reset({
          ...formData,
          skill: matchedSkills
        })

        // Manually trigger a form change event to ensure the UI updates
        handleFormChange('skill', matchedSkills)
      }
    } catch (error) {
      console.error('Error fetching skill details:', error)
    }
  }

  // Handle form field changes
  const handleFormChange = (field: keyof ProjectEntity, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Handle form submission
  const onSubmit: SubmitHandler<ProjectEntity> = async data => {
    try {
      setLoading(true)

      // Log the form data for debugging
      console.log('Form data submitted:', data)

      // Create FormData object
      const formData = new FormData()

      // Process form data
      // @ts-ignore
      data.startedAt = data.startedAt ? new Date(data.startedAt).toISOString() : null
      // @ts-ignore
      data.endedAt = data.endedAt ? new Date(data.endedAt).toISOString() : null

      // Add all form fields to FormData
      for (const key in data) {
        if (key !== 'coverImages' && key !== 'skill') {
          formData.append(key, String(data[key as keyof ProjectEntity]))
        }
      }

      // First try to get skills from the form data
      let skillIds: string[] = []

      if (data.skill && Array.isArray(data.skill) && data.skill.length > 0) {
        // Extract skill IDs regardless of whether they're strings or objects
        skillIds = data.skill
          .map((skill: any) => {
            // If skill is already a string (ID), use it directly
            if (typeof skill === 'string') return skill

            // If skill is an object with an id property, use that
            if (skill && skill.id) return skill.id

            // Otherwise return null and filter it out
            return null
          })
          .filter(Boolean) as string[]

        console.log('Extracted skill IDs from form data:', skillIds)
      }

      // If no skills in form data, check the form state
      // @ts-ignore
      else if (formData.skill) {
        // @ts-ignore
        if (Array.isArray(formData.skill) && formData.skill.length > 0) {
          // @ts-ignore
          skillIds = formData.skill
            .map((skill: any) => {
              return typeof skill === 'string' ? skill : skill && skill.id ? skill.id : null
            })
            .filter(Boolean) as string[]
          console.log('Extracted skill IDs from form state:', skillIds)
        }

        // Handle case where skill might be a single object
        // @ts-ignore
        else if (typeof formData.skill === 'object' && !Array.isArray(formData.skill) && formData.skill.id) {
          // @ts-ignore
          skillIds = [formData.skill.id]
          console.log('Extracted skill ID from single object:', skillIds)
        }
      }

      // Send skills as a JSON string (even if empty)
      console.log('Final skill IDs being sent:', skillIds)
      formData.append('skill', JSON.stringify(skillIds))

      // Add cover image files if selected
      if (coverImageFiles.length > 0) {
        coverImageFiles.forEach((file, index) => {
          formData.append(`coverImages`, file)
        })
      }

      // If we're in edit mode and have deleted images, send them to the API
      if (isEditMode && deletedImageUrls.length > 0) {
        formData.append('deletedImageUrls', JSON.stringify(deletedImageUrls))
      }

      let url = '/api/projects'
      let method = 'POST'

      if (isEditMode && projectId) {
        url = `/api/projects/${projectId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formData
      })

      const responseText = await res.text()

      if (!res.ok) {
        // Parse the error response if it's JSON
        let errorMessage = 'Failed to save projects'

        try {
          const errorData = JSON.parse(responseText)

          errorMessage = errorData.message || errorMessage
        } catch (e) {
          console.error('Error parsing error response:', e)
        }

        throw new Error(errorMessage)
      }

      // Parse the response if it's not empty
      const resp = responseText ? JSON.parse(responseText) : {}

      setNotification({
        show: true,
        message: isEditMode ? 'Project updated successfully' : 'Project created successfully',
        type: 'success'
      })

      // Redirect to projects list after successful save
      setTimeout(() => {
        router.push('/projects?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save projects'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    coverImageFiles,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    projectId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    handleRemoveImage,
    handleDeleteExistingImage,
    setCropperOpen,

    setNotification,
    reset,
    setValue, // Add setValue to the returned object
    router
  }
}
