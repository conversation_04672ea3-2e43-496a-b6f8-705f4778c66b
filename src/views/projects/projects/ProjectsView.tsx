'use client'

import { useState } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'
import Chip from '@mui/material/Chip'
import Link from '@mui/material/Link'
import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import IconButton from '@mui/material/IconButton'

import { useProjectsForm } from './hooks/useProjectsForm'

const ProjectsView = () => {
  const { formData, imgSrc, fetchLoading, notification, projectId, setNotification, router } = useProjectsForm()

  // State for image preview modal
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Project Details' />
        <CardContent>
          <Typography>Loading project data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Project Details' />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-start gap-6'>
          <div>
            <Typography variant='subtitle1' gutterBottom>
              Project Images
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              {formData.coverImages && formData.coverImages.length > 0 ? (
                formData.coverImages.map((image, index) => (
                  <Box
                    key={index}
                    sx={{
                      cursor: 'pointer',
                      '&:hover': { opacity: 0.8 },
                      transition: 'opacity 0.2s'
                    }}
                    onClick={() => setPreviewImage(image)}
                  >
                    <img
                      height={100}
                      width={100}
                      className='rounded'
                      src={image}
                      alt={`Project Cover ${index + 1}`}
                      style={{ objectFit: 'cover' }}
                    />
                  </Box>
                ))
              ) : (
                <img
                  height={100}
                  width={100}
                  className='rounded'
                  src={imgSrc}
                  alt='Project Cover'
                  style={{ objectFit: 'cover' }}
                />
              )}
            </Box>
          </div>
          <div>
            <Typography variant='h6'>{formData.name}</Typography>
            <Typography variant='body1'>
              {/*@ts-ignore*/}
              {new Date(formData.startedAt).toLocaleDateString()} - {new Date(formData.endedAt).toLocaleDateString()}
            </Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Project Name
            </Typography>
            <Typography variant='body1'>{formData.name}</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Company Website
            </Typography>
            {formData.companyWebsite ? (
              <Link href={formData.companyWebsite} target='_blank' rel='noopener noreferrer'>
                {formData.companyWebsite}
              </Link>
            ) : (
              <Typography variant='body1'>No company website provided</Typography>
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Start Date
            </Typography>
            <Typography variant='body1'>
              {formData.startedAt instanceof Date
                ? formData.startedAt.toLocaleDateString()
                : // @ts-ignore
                  new Date(formData.startedAt).toLocaleDateString()}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              End Date
            </Typography>
            <Typography variant='body1'>
              {formData.endedAt instanceof Date
                ? formData.endedAt.toLocaleDateString()
                : // @ts-ignore
                  new Date(formData.endedAt).toLocaleDateString()}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              GitHub Link
            </Typography>
            {formData.githubLink ? (
              <Link href={formData.githubLink} target='_blank' rel='noopener noreferrer'>
                {formData.githubLink}
              </Link>
            ) : (
              <Typography variant='body1'>No GitHub link provided</Typography>
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Project Live Link
            </Typography>
            {formData.projectLiveLink ? (
              <Link href={formData.projectLiveLink} target='_blank' rel='noopener noreferrer'>
                {formData.projectLiveLink}
              </Link>
            ) : (
              <Typography variant='body1'>No live link provided</Typography>
            )}
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Skills Used
            </Typography>
            <div className='flex flex-wrap gap-2 mt-2'>
              {formData.skill && formData.skill.length > 0 ? (
                formData.skill.map(skill => (
                  // @ts-ignore
                  <Chip key={skill.id} label={skill.name} color='primary' variant='outlined' />
                ))
              ) : (
                <Typography variant='body1'>No skills specified</Typography>
              )}
            </div>
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Project Description
            </Typography>
            <Typography variant='body1'>{formData.projectDescription || 'No description provided'}</Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Project Details
            </Typography>
            <Typography variant='body1'>{formData.details || 'No details provided'}</Typography>
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/projects?tab=projects&id=${projectId}&mode=edit`)}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/projects?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Preview Modal */}
      <Dialog
        open={Boolean(previewImage)}
        onClose={() => setPreviewImage(null)}
        maxWidth='lg'
        PaperProps={{
          sx: { borderRadius: 1, overflow: 'hidden' }
        }}
      >
        <DialogContent sx={{ p: 0, position: 'relative' }}>
          {previewImage && (
            <>
              <IconButton
                onClick={() => setPreviewImage(null)}
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  bgcolor: 'rgba(0,0,0,0.5)',
                  color: 'white',
                  '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
                }}
              >
                <i className='ri-close-line'></i>
              </IconButton>
              <img
                src={previewImage}
                alt='Project Image'
                style={{ display: 'block', maxWidth: '100%', maxHeight: '80vh' }}
              />
            </>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}

export default ProjectsView
