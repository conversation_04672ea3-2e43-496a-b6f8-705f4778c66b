'use client'

import React, { useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Switch from '@mui/material/Switch'
import Box from '@mui/material/Box'
import TextField from '@mui/material/TextField'
import MenuItem from '@mui/material/MenuItem'
import Pagination from '@mui/material/Pagination'

// Type Imports
import type { ProjectEntity } from '@/types/entities'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

type ProjectsTableProps = {
  projects: ProjectEntity[]
  loading: boolean
  error: string | null
  groupByUser?: boolean
  selectedUserId?: string | null
  onDeleteSuccess: (deletedProjectId: string) => void
  onStatusChange?: (id: string, active: boolean) => Promise<void>

  // Pagination props
  page?: number
  limit?: number
  total?: number
  totalPages?: number
  onPageChange?: (event: React.ChangeEvent<unknown>, newPage: number) => void
  onLimitChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const ProjectsTable = ({
  projects,
  loading,
  error,
  groupByUser = true,
  selectedUserId = null,
  onDeleteSuccess,
  onStatusChange,

  // Pagination props
  page = 1,
  limit = 10,
  total = 0,
  totalPages = 0,
  onPageChange,
  onLimitChange
}: ProjectsTableProps) => {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; projectId: string | null; projectName: string }>({
    open: false,
    projectId: null,
    projectName: ''
  })

  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)

  // State for tracking projects being updated
  const [updatingProjects, setUpdatingProjects] = useState<Record<string, boolean>>({})

  // State for local toggle status (for visual feedback when API fails)
  const [localToggleStatus, setLocalToggleStatus] = useState<Record<string, boolean>>({})

  // Image gallery modal state
  const [galleryModal, setGalleryModal] = useState<{
    open: boolean
    projectName: string
    images: string[]
    currentIndex: number
  }>({
    open: false,
    projectName: '',
    images: [],
    currentIndex: 0
  })

  // Filter projects by selected user
  const filteredProjects = React.useMemo(() => {
    if (!projects || projects.length === 0) return []

    if (selectedUserId && selectedUserId !== 'all') {
      return projects.filter(project => project.user?.id === selectedUserId)
    }

    return projects
  }, [projects, selectedUserId])

  // Group projects by user
  const groupedProjects = React.useMemo(() => {
    if (!filteredProjects || filteredProjects.length === 0) return {}

    return filteredProjects.reduce(
      (groups, project) => {
        const userId = project.user?.id || 'unknown'

        if (!groups[userId]) {
          groups[userId] = {
            user: project.user || { id: 'unknown', name: 'Unknown User', email: '', password: '' },
            projects: []
          }
        }

        groups[userId].projects.push(project)

        return groups
      },
      {} as Record<string, { user: any; projects: ProjectEntity[] }>
    )
  }, [filteredProjects])

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (projectId: string, projectName: string) => {
    setDeleteDialog({
      open: true,
      projectId,
      projectName
    })
  }

  // Handle closing delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      projectId: null,
      projectName: ''
    })
    setDeleteError(null) // Reset error state when closing dialog
  }

  // Handle opening image gallery modal
  const handleOpenGallery = (project: ProjectEntity) => {
    if (project.coverImages && project.coverImages.length > 0) {
      setGalleryModal({
        open: true,
        projectName: project.name,
        images: project.coverImages,
        currentIndex: 0
      })
    }
  }

  // Handle closing image gallery modal
  const handleCloseGallery = () => {
    setGalleryModal({
      ...galleryModal,
      open: false
    })
  }

  // Navigate to next image in gallery
  const handleNextImage = () => {
    setGalleryModal({
      ...galleryModal,
      currentIndex: (galleryModal.currentIndex + 1) % galleryModal.images.length
    })
  }

  // Navigate to previous image in gallery
  const handlePrevImage = () => {
    setGalleryModal({
      ...galleryModal,
      currentIndex: (galleryModal.currentIndex - 1 + galleryModal.images.length) % galleryModal.images.length
    })
  }

  // Handle confirming deletion
  const handleConfirmDelete = async () => {
    if (!deleteDialog.projectId) return

    const projectIdToDelete = deleteDialog.projectId

    try {
      setDeleteLoading(true)
      setDeleteError(null)

      const response = await fetch(`/api/projects/${projectIdToDelete}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to delete project')
      }

      // Close dialog and notify parent of successful deletion with project ID
      handleCloseDeleteDialog()
      onDeleteSuccess(projectIdToDelete)
    } catch (err) {
      console.error('Error deleting project:', err)
      setDeleteError(err instanceof Error ? err.message : 'An error occurred while deleting the project')
    } finally {
      setDeleteLoading(false)
    }
  }

  if (loading) {
    return (
      <div className='flex justify-center p-6'>
        <CircularProgress />
      </div>
    )
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: boolean) => {
    if (!onStatusChange) return

    try {
      // Set the project as updating
      setUpdatingProjects(prev => ({ ...prev, [id]: true }))

      // Update local toggle state immediately for visual feedback
      setLocalToggleStatus(prev => ({
        ...prev,
        [id]: !currentStatus
      }))

      // Call the parent handler to update the status
      await onStatusChange(id, !currentStatus)
    } catch (error) {
      console.error('Error updating project status:', error)

      // Keep the local toggle state even if API fails
    } finally {
      // Remove the updating state
      setUpdatingProjects(prev => {
        const newState = { ...prev }

        delete newState[id]

        return newState
      })
    }
  }

  // Get the effective active status (use local state if available, otherwise use project.active)
  const getActiveStatus = (project: ProjectEntity) => {
    return localToggleStatus[project.id as string] !== undefined
      ? localToggleStatus[project.id as string]
      : project.active
  }

  // @ts-ignore
  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Project</th>
              <th>Duration</th>
              <th>Skills</th>
              <th>Links</th>
              <th>Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {groupByUser ? (
              // Grouped view
              Object.keys(groupedProjects).length > 0 ? (
                Object.entries(groupedProjects).map(([userId, { user, projects: userProjects }]) => (
                  <React.Fragment key={userId}>
                    {/* User Header Row */}
                    <tr className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-l-4 border-blue-500'>
                      <td colSpan={6} className='!plb-4 !pli-6'>
                        <div className='flex items-center gap-4'>
                          {user.profileImage ? (
                            <img
                              src={user.profileImage}
                              alt={user.name}
                              width={40}
                              height={40}
                              className='rounded-full object-cover ring-2 ring-blue-200 dark:ring-blue-600'
                            />
                          ) : (
                            <div
                              className='flex items-center justify-center bg-blue-100 text-blue-600 rounded-full ring-2 ring-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:ring-blue-600'
                              style={{ width: 40, height: 40, fontSize: '16px', fontWeight: 600 }}
                            >
                              {user.name?.charAt(0) || 'U'}
                            </div>
                          )}
                          <div className='flex-1'>
                            <Typography variant='h6' className='font-bold text-gray-800 dark:text-gray-100'>
                              {user.name || 'Unknown User'}
                            </Typography>
                            <Typography variant='body2' className='text-blue-600 dark:text-blue-400 font-medium'>
                              {userProjects.length} project record{userProjects.length !== 1 ? 's' : ''}
                            </Typography>
                          </div>
                          <div className='text-right'>
                            <Typography variant='caption' className='text-gray-500 dark:text-gray-400'>
                              {user.email}
                            </Typography>
                          </div>
                        </div>
                      </td>
                    </tr>
                    {/* Project Records for this User */}
                    {userProjects.map((project, index) => (
                      <tr
                        key={project.id}
                        className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      >
                        <td className='!plb-1'>
                          <div className='flex items-center gap-3 ml-8'>
                            {project.coverImages && project.coverImages.length > 0 ? (
                              <div
                                className='relative group cursor-pointer'
                                onClick={() => handleOpenGallery(project)}
                                title='Click to view all images'
                              >
                                <img
                                  src={project.coverImages[0]}
                                  alt={project.name}
                                  width={32}
                                  height={32}
                                  className='rounded-lg object-cover shadow-sm border border-gray-200 hover:opacity-80 transition-opacity'
                                />
                                {project.coverImages.length > 1 && (
                                  <div className='absolute -top-1 -right-1 bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs'>
                                    {project.coverImages.length}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div
                                className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                                style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                              >
                                {project.name.charAt(0)}
                              </div>
                            )}
                            <div>
                              <Typography className='font-semibold text-gray-900'>{project.name}</Typography>
                              <Typography variant='caption' className='text-gray-600'>
                                Project
                              </Typography>
                            </div>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <div className='bg-blue-100 px-2 py-1 rounded-md inline-block'>
                            <Typography variant='body2' className='font-medium text-blue-700'>
                              {/*@ts-ignore*/}
                              {new Date(project.startedAt).toLocaleDateString()} - {/*@ts-ignore*/}
                              {new Date(project.endedAt).toLocaleDateString()}
                            </Typography>
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <div className='flex flex-wrap gap-1'>
                            {project.skill && project.skill.length > 0 ? (
                              project.skill.slice(0, 3).map(skill => (
                                // @ts-ignore
                                <Chip key={skill.id} label={skill.name} size='small' variant='outlined' />
                              ))
                            ) : (
                              <Typography variant='body2' className='text-gray-500'>
                                No skills
                              </Typography>
                            )}
                            {project.skill && project.skill.length > 3 && (
                              // @ts-ignore
                              <Chip label={`+${project.skill.length - 3}`} size='small' color='primary' />
                            )}
                          </div>
                        </td>
                        <td className='!plb-1'>
                          <div className='flex gap-1 justify-center'>
                            {project.githubLink && (
                              <Tooltip title='GitHub'>
                                <IconButton
                                  size='small'
                                  component='a'
                                  href={project.githubLink}
                                  target='_blank'
                                  rel='noopener noreferrer'
                                  className='hover:bg-gray-50'
                                >
                                  <i className='ri-github-fill text-gray-700'></i>
                                </IconButton>
                              </Tooltip>
                            )}
                            {project.projectLiveLink && (
                              <Tooltip title='Live Demo'>
                                <IconButton
                                  size='small'
                                  component='a'
                                  href={project.projectLiveLink}
                                  target='_blank'
                                  rel='noopener noreferrer'
                                  className='hover:bg-green-50'
                                >
                                  <i className='ri-external-link-line text-green-600'></i>
                                </IconButton>
                              </Tooltip>
                            )}
                            {!project.githubLink && !project.projectLiveLink && (
                              <Typography variant='body2' className='text-gray-500'>
                                No links
                              </Typography>
                            )}
                          </div>
                        </td>
                        <td className='!plb-1'>
                          {onStatusChange ? (
                            <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                              <Tooltip title={getActiveStatus(project) ? 'Deactivate' : 'Activate'}>
                                <span>
                                  <Switch
                                    checked={getActiveStatus(project)}
                                    onChange={() => handleStatusToggle(project.id as string, getActiveStatus(project))}
                                    disabled={updatingProjects[project.id as string]}
                                    color='primary'
                                    size='medium'
                                  />
                                </span>
                              </Tooltip>
                              <div
                                style={{
                                  width: '24px',
                                  display: 'inline-flex',
                                  justifyContent: 'center',
                                  marginLeft: '8px'
                                }}
                              >
                                {updatingProjects[project.id as string] && <CircularProgress size={18} />}
                              </div>
                            </div>
                          ) : (
                            <div className='flex items-center justify-center'>
                              <div
                                className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(project) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                              >
                                {getActiveStatus(project) ? 'Active' : 'Inactive'}
                              </div>
                            </div>
                          )}
                        </td>
                        <td className='!plb-1'>
                          <div className='flex gap-1 justify-center'>
                            <Tooltip title='View'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/projects?tab=projects&id=${project.id}&mode=view`}
                                className='hover:bg-blue-50'
                              >
                                <i className='ri-eye-line text-blue-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Edit'>
                              <IconButton
                                size='small'
                                component={Link}
                                href={`/projects?tab=projects&id=${project.id}&mode=edit`}
                                className='hover:bg-orange-50'
                              >
                                <i className='ri-pencil-line text-orange-600'></i>
                              </IconButton>
                            </Tooltip>
                            <Tooltip title='Delete'>
                              <IconButton
                                size='small'
                                onClick={() => handleDeleteClick(project.id as string, project.name)}
                                className='hover:bg-red-50'
                              >
                                <i className='ri-delete-bin-line text-red-600'></i>
                              </IconButton>
                            </Tooltip>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className='text-center py-12'>
                    <div className='flex flex-col items-center gap-3'>
                      <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
                        <i className='ri-folder-line text-2xl text-gray-400'></i>
                      </div>
                      <Typography variant='h6' className='text-gray-500'>
                        No project records found
                      </Typography>
                      <Typography variant='body2' className='text-gray-400'>
                        {selectedUserId && selectedUserId !== 'all'
                          ? 'No project records found for the selected user'
                          : 'Start by adding your first project record'}
                      </Typography>
                    </div>
                  </td>
                </tr>
              )
            ) : // List view (ungrouped)
            filteredProjects && filteredProjects.length > 0 ? (
              filteredProjects.map((project, index) => (
                <tr
                  key={project.id}
                  className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      {project.coverImages && project.coverImages.length > 0 ? (
                        <div
                          className='relative group cursor-pointer'
                          onClick={() => handleOpenGallery(project)}
                          title='Click to view all images'
                        >
                          <img
                            src={project.coverImages[0]}
                            alt={project.name}
                            width={32}
                            height={32}
                            className='rounded-lg object-cover shadow-sm border border-gray-200 hover:opacity-80 transition-opacity'
                          />
                          {project.coverImages.length > 1 && (
                            <div className='absolute -top-1 -right-1 bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs'>
                              {project.coverImages.length}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div
                          className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                          style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                        >
                          {project.name.charAt(0)}
                        </div>
                      )}
                      <div>
                        <Typography className='font-semibold text-gray-900'>{project.name}</Typography>
                        <Typography variant='caption' className='text-gray-600'>
                          Project
                        </Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div className='bg-blue-100 px-2 py-1 rounded-md inline-block'>
                      <Typography variant='body2' className='font-medium text-blue-700'>
                        {/*// @ts-ignore*/}
                        {new Date(project.startedAt).toLocaleDateString()} - {/*// @ts-ignore*/}
                        {new Date(project.endedAt).toLocaleDateString()}
                      </Typography>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div className='flex flex-wrap gap-1'>
                      {project.skill && project.skill.length > 0 ? (
                        project.skill
                          .slice(0, 3)
                          // @ts-ignore
                          .map(skill => <Chip key={skill.id} label={skill.name} size='small' variant='outlined' />)
                      ) : (
                        <Typography variant='body2' className='text-gray-500'>
                          No skills
                        </Typography>
                      )}
                      {project.skill && project.skill.length > 3 && (
                        // @ts-ignore
                        <Chip label={`+${project.skill.length - 3}`} size='small' color='primary' />
                      )}
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-1 justify-center'>
                      {project.githubLink && (
                        <Tooltip title='GitHub'>
                          <IconButton
                            size='small'
                            component='a'
                            href={project.githubLink}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='hover:bg-gray-50'
                          >
                            <i className='ri-github-fill text-gray-700'></i>
                          </IconButton>
                        </Tooltip>
                      )}
                      {project.projectLiveLink && (
                        <Tooltip title='Live Demo'>
                          <IconButton
                            size='small'
                            component='a'
                            href={project.projectLiveLink}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='hover:bg-green-50'
                          >
                            <i className='ri-external-link-line text-green-600'></i>
                          </IconButton>
                        </Tooltip>
                      )}
                      {!project.githubLink && !project.projectLiveLink && (
                        <Typography variant='body2' className='text-gray-500'>
                          No links
                        </Typography>
                      )}
                    </div>
                  </td>
                  <td className='!plb-1'>
                    {onStatusChange ? (
                      <div className='flex items-center justify-center' style={{ minWidth: '100px' }}>
                        <Tooltip title={getActiveStatus(project) ? 'Deactivate' : 'Activate'}>
                          <span>
                            <Switch
                              checked={getActiveStatus(project)}
                              onChange={() => handleStatusToggle(project.id as string, getActiveStatus(project))}
                              disabled={updatingProjects[project.id as string]}
                              color='primary'
                              size='medium'
                            />
                          </span>
                        </Tooltip>
                        <div
                          style={{ width: '24px', display: 'inline-flex', justifyContent: 'center', marginLeft: '8px' }}
                        >
                          {updatingProjects[project.id as string] && <CircularProgress size={18} />}
                        </div>
                      </div>
                    ) : (
                      <div className='flex items-center justify-center'>
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium ${getActiveStatus(project) ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                        >
                          {getActiveStatus(project) ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                    )}
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-1 justify-center'>
                      <Tooltip title='View'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/projects?tab=projects&id=${project.id}&mode=view`}
                          className='hover:bg-blue-50'
                        >
                          <i className='ri-eye-line text-blue-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton
                          size='small'
                          component={Link}
                          href={`/projects?tab=projects&id=${project.id}&mode=edit`}
                          className='hover:bg-orange-50'
                        >
                          <i className='ri-pencil-line text-orange-600'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          onClick={() => handleDeleteClick(project.id as string, project.name)}
                          className='hover:bg-red-50'
                        >
                          <i className='ri-delete-bin-line text-red-600'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className='text-center py-12'>
                  <div className='flex flex-col items-center gap-3'>
                    <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
                      <i className='ri-folder-line text-2xl text-gray-400'></i>
                    </div>
                    <Typography variant='h6' className='text-gray-500'>
                      No project records found
                    </Typography>
                    <Typography variant='body2' className='text-gray-400'>
                      {selectedUserId && selectedUserId !== 'all'
                        ? 'No project records found for the selected user'
                        : 'Start by adding your first project record'}
                    </Typography>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {total > 0 && totalPages > 0 && onPageChange && onLimitChange && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3, px: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant='body2' color='text.secondary'>
              Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} projects
            </Typography>
            <TextField
              select
              size='small'
              label='Per page'
              value={limit}
              onChange={onLimitChange}
              disabled={loading}
              sx={{ minWidth: 100 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </TextField>
          </Box>
          {/*// @ts-ignore*/}
          <Pagination
            count={totalPages}
            page={page}
            onChange={onPageChange}
            disabled={loading}
            color='primary'
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
      >
        <DialogTitle id='delete-dialog-title'>Delete Project</DialogTitle>
        <DialogContent>
          <DialogContentText id='delete-dialog-description'>
            Are you sure you want to delete the project <strong>{deleteDialog.projectName}</strong>? This action cannot
            be undone.
          </DialogContentText>
          {deleteError && (
            <Typography color='error' className='mt-4'>
              {deleteError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color='error'
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : null}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Gallery Modal */}
      <Dialog
        open={galleryModal.open}
        onClose={handleCloseGallery}
        maxWidth='md'
        fullWidth
        PaperProps={{
          sx: { borderRadius: 1, overflow: 'hidden' }
        }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant='h6'>{galleryModal.projectName} - Images</Typography>
          <IconButton onClick={handleCloseGallery} size='small'>
            <i className='ri-close-line'></i>
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, position: 'relative', height: '60vh', display: 'flex', flexDirection: 'column' }}>
          {galleryModal.images.length > 0 && (
            <>
              <div className='flex-1 flex items-center justify-center bg-black/5 relative'>
                <img
                  src={galleryModal.images[galleryModal.currentIndex]}
                  alt={`${galleryModal.projectName} image ${galleryModal.currentIndex + 1}`}
                  style={{ maxHeight: 'calc(60vh - 120px)', maxWidth: '100%', objectFit: 'contain' }}
                />

                {/* Navigation buttons */}
                {galleryModal.images.length > 1 && (
                  <>
                    <IconButton
                      onClick={handlePrevImage}
                      sx={{
                        position: 'absolute',
                        left: 8,
                        bgcolor: 'rgba(255,255,255,0.7)',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    >
                      <i className='ri-arrow-left-s-line'></i>
                    </IconButton>
                    <IconButton
                      onClick={handleNextImage}
                      sx={{
                        position: 'absolute',
                        right: 8,
                        bgcolor: 'rgba(255,255,255,0.7)',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    >
                      <i className='ri-arrow-right-s-line'></i>
                    </IconButton>
                  </>
                )}
              </div>

              {/* Thumbnails */}
              <div className='p-4 overflow-x-auto'>
                <div className='flex gap-2'>
                  {galleryModal.images.map((image, index) => (
                    <div
                      key={index}
                      className={`cursor-pointer border-2 ${index === galleryModal.currentIndex ? 'border-primary' : 'border-transparent'}`}
                      onClick={() => setGalleryModal({ ...galleryModal, currentIndex: index })}
                    >
                      <img
                        src={image}
                        alt={`Thumbnail ${index + 1}`}
                        style={{ width: 60, height: 60, objectFit: 'cover' }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ProjectsTable
