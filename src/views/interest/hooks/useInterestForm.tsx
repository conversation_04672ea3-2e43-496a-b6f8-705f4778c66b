'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { useForm } from 'react-hook-form'
import type { SubmitHandler } from 'react-hook-form'

import type { InterestEntity } from '@/types/entities'
import { extractErrorDetails } from '@/utils/errorHandling'

// Empty interest template for new interests
export const emptyInterest: InterestEntity = {
  // @ts-ignore
  cover: [],
  description: '',
  logo: ''
}

export const useInterestForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const interestId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && interestId)

  // States
  const [formData, setFormData] = useState<InterestEntity>(emptyInterest)
  const [logoFileInput, setLogoFileInput] = useState<string>('')
  const [coverFileInput, setCoverFileInput] = useState<string>('')
  const [logoImgSrc, setLogoImgSrc] = useState<string>('/images/avatars/default.png')
  const [coverImgSrc, setCoverImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [coverFile, setCoverFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Image cropper states for logo and cover
  const [logoCropperOpen, setLogoCropperOpen] = useState<boolean>(false)
  const [coverCropperOpen, setCoverCropperOpen] = useState<boolean>(false)
  const [logoTempImgSrc, setLogoTempImgSrc] = useState<string>('')
  const [coverTempImgSrc, setCoverTempImgSrc] = useState<string>('')

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<InterestEntity>({
    defaultValues: emptyInterest,
    mode: 'onBlur' // Change validation mode to prevent continuous validation
  })

  // Fetch interest data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && interestId) {
      const fetchInterest = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/interest/${interestId}`)

          if (!response.ok) {
            const errorData = await response.json()

            throw new Error(errorData.message || 'Failed to fetch interest')
          }

          const data = await response.json()

          // Update all state in a single batch to prevent multiple re-renders
          setFormData(data)

          // Set images if available
          if (data.logo) {
            setLogoImgSrc(data.logo)
          }

          if (data.cover && data.cover.length > 0) {
            setCoverImgSrc(data.cover[0])
          }

          // Reset form with fetched data - use setTimeout to ensure it happens after render
          setTimeout(() => {
            reset(data, {
              keepDirty: false,
              keepErrors: false,
              keepIsSubmitted: false,
              keepTouched: false,
              keepIsValid: false,
              keepSubmitCount: false
            })
          }, 0)
        } catch (err) {
          console.error('Error fetching interest:', err)
          setNotification({
            show: true,
            message: err instanceof Error ? err.message : 'Failed to load interest data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchInterest()
    }
  }, [interestId, isEditMode, isViewMode, reset])

  // Open the logo cropper dialog
  const handleOpenLogoCropper = () => {
    if (logoImgSrc && logoImgSrc !== '/images/avatars/default.png') {
      setLogoTempImgSrc(logoImgSrc)
      setLogoCropperOpen(true)
    }
  }

  // Handle cropped logo image
  const handleCroppedLogoImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-logo.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setLogoImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setLogoCropperOpen(false)
  }

  // Open the cover cropper dialog
  const handleOpenCoverCropper = () => {
    if (coverImgSrc && coverImgSrc !== '/images/avatars/default.png') {
      setCoverTempImgSrc(coverImgSrc)
      setCoverCropperOpen(true)
    }
  }

  // Handle cropped cover image
  const handleCroppedCoverImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-cover.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setCoverFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setCoverImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCoverCropperOpen(false)
  }

  const onSubmit: SubmitHandler<InterestEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Process form data
      Object.keys(data).forEach(key => {
        // Skip logo and cover fields as we'll handle them separately
        if (key === 'logo' || key === 'cover') return

        // Add other fields to FormData
        // @ts-ignore
        formData.append(key, data[key] as string)
      })

      // Add logo file if available
      if (logoFile) {
        formData.append('logo', logoFile)
      } else if (logoImgSrc && logoImgSrc !== '/images/avatars/default.png') {
        // If we have an existing logo URL and no new file, pass the URL
        formData.append('logo', logoImgSrc)
      }

      // Add cover file if available
      if (coverFile) {
        formData.append('cover', coverFile)
      } else if (coverImgSrc && coverImgSrc !== '/images/avatars/default.png') {
        // If we have an existing cover URL and no new file, pass the URL
        formData.append('cover', coverImgSrc)
      }

      // Set up request URL and method
      let url = '/api/interest'
      let method = 'POST'

      // If editing, use PATCH method and include ID in URL
      if (isEditMode && interestId) {
        url = `/api/interest/${interestId}`
        method = 'PATCH'
      }

      // Send the request
      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save interest')
      }

      // Handle successful response
      await res.json()

      // Show success notification
      setNotification({
        show: true,
        message: isEditMode ? 'Interest updated successfully' : 'Interest created successfully',
        type: 'success'
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/interest')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract error message using utility function
      const { errorMessage } = extractErrorDetails(error, 'Failed to save interest')

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFormChange = (field: keyof InterestEntity, value: any) => {
    setFormData(prevData => ({ ...prevData, [field]: value }))
  }

  const handleLogoFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      setLogoFile(files[0])

      // Update file input value
      setLogoFileInput(event.target.value)

      // Create preview
      reader.onload = () => {
        setLogoImgSrc(reader.result as string)
        setLogoTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  const handleCoverFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      setCoverFile(files[0])

      // Update file input value
      setCoverFileInput(event.target.value)

      // Create preview
      reader.onload = () => {
        setCoverImgSrc(reader.result as string)
        setCoverTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  const handleLogoFileInputReset = () => {
    setLogoFileInput('')
    setLogoImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  const handleCoverFileInputReset = () => {
    setCoverFileInput('')
    setCoverImgSrc('/images/avatars/default.png')
    setCoverFile(null)
  }

  return {
    formData,
    logoFileInput,
    coverFileInput,
    logoImgSrc,
    coverImgSrc,
    loading,
    fetchLoading,
    notification,
    logoCropperOpen,
    coverCropperOpen,
    logoTempImgSrc,
    coverTempImgSrc,

    isViewMode,
    isEditMode,
    interestId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleLogoFileInputChange,
    handleCoverFileInputChange,
    handleLogoFileInputReset,
    handleCoverFileInputReset,
    handleOpenLogoCropper,
    handleOpenCoverCropper,
    handleCroppedLogoImage,
    handleCroppedCoverImage,
    setLogoCropperOpen,
    setCoverCropperOpen,

    setNotification,
    reset,
    router
  }
}
