'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { type SubmitHandler, useForm } from 'react-hook-form'

import type { InterestEntity } from '@/types/entities'

// Empty interest template for new interests
export const emptyInterest: InterestEntity = {
  title: '',
  description: '',
  logo: '',

  // Frontend-only fields for enhanced UI
  category: '',
  active: false,
  user: undefined
}

export const useInterestForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const interestId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && interestId)

  // States
  const [formData, setFormData] = useState<InterestEntity>(emptyInterest)
  const [originalData, setOriginalData] = useState<InterestEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<InterestEntity>()

  // Fetch interest data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && interestId) {
      const fetchInterest = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/interest/${interestId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch interest')
          }

          const data = await response.json()

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)
          reset(data)

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }
        } catch (err) {
          console.error('Error fetching interest:', err)
          setNotification({
            show: true,
            message: 'Failed to load interest data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchInterest()
    }
  }, [interestId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(file)
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof InterestEntity, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Handle form submission
  const onSubmit: SubmitHandler<InterestEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Add required backend fields to FormData
      // Only send fields that are in the backend schema
      formData.append('title', data.title || '')
      formData.append('description', data.description || '')

      // Add logo file if selected
      if (logoFile) {
        formData.append('logo', logoFile)
      }

      let url = '/api/interest'
      let method = 'POST'

      if (isEditMode && interestId) {
        url = `/api/interest/${interestId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save interest')
      }

      const resp = await res.json()

      setNotification({
        show: true,
        message: isEditMode ? 'Interest updated successfully' : 'Interest created successfully',
        type: 'success'
      })

      // Redirect to interest list after successful save
      setTimeout(() => {
        router.push('/interest?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save interest'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    interestId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
