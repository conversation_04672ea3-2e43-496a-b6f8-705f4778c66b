'use client'

// React Imports
import React, { useEffect, useState } from 'react'

// Next Imports
import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Chip from '@mui/material/Chip'

// Type Imports
import type { InterestEntity, UserEntity } from '@/types/entities'

// Component Imports
import InterestTable from './InterestTable'

const AllInterest = () => {
  // States
  const [interests, setInterests] = useState<InterestEntity[]>([])
  const [users, setUsers] = useState<UserEntity[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [usersLoading, setUsersLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [groupByUser, setGroupByUser] = useState<boolean>(true)
  const [selectedUserId, setSelectedUserId] = useState<string>('all')

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Pagination states
  const [page, setPage] = useState<number>(1)
  const [limit, setLimit] = useState<number>(10)
  const [total, setTotal] = useState<number>(0)
  const [totalPages, setTotalPages] = useState<number>(0)

  // Fetch interests function
  const fetchInterests = async (currentPage = page, currentLimit = limit) => {
    try {
      setLoading(true)

      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: currentLimit.toString()
      })

      const response = await fetch(`/api/interest?${queryParams.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch interests')
      }

      const data = await response.json()

      // Handle new paginated response structure
      if (data.data && Array.isArray(data.data)) {
        setInterests(data.data)
        setTotal(data.total || 0)
        setTotalPages(data.totalPages || 0)
        setPage(data.page || currentPage)
      } else {
        // Fallback for non-paginated response (backward compatibility)
        setInterests(Array.isArray(data) ? data : [])
        setTotal(Array.isArray(data) ? data.length : 0)
        setTotalPages(1)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching interests')
      console.error('Error fetching interests:', err)
    } finally {
      setLoading(false)
    }
  }

  // Fetch users function
  const fetchUsers = async () => {
    try {
      setUsersLoading(true)
      const response = await fetch('/api/v1/user')

      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const data = await response.json()

      setUsers(data)
    } catch (err) {
      console.error('Error fetching users:', err)
    } finally {
      setUsersLoading(false)
    }
  }

  // Fetch interests and users on component mount
  useEffect(() => {
    fetchInterests()
    fetchUsers()
  }, [])

  // Handle pagination changes
  const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage)
    fetchInterests(newPage, limit)
  }

  const handleLimitChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10)

    setLimit(newLimit)
    setPage(1) // Reset to first page when changing limit
    fetchInterests(1, newLimit)
  }

  // Handle successful deletion
  const handleDeleteSuccess = (deletedInterestId: string) => {
    setNotification({
      show: true,
      message: 'Interest deleted successfully',
      type: 'success'
    })

    // Update local state by removing the deleted interest
    setInterests(prevInterests => prevInterests.filter(interest => interest.id !== deletedInterestId))

    // Update total count
    setTotal(prevTotal => Math.max(0, prevTotal - 1))
  }

  // Handle interest status change
  const handleStatusChange = async (id: string, active: boolean) => {
    try {
      // Create FormData for the API call
      const formData = new FormData()

      formData.append('active', active.toString())

      // Make API call to update the interest
      const response = await fetch(`/api/interest/${id}`, {
        method: 'PATCH',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.message || 'Failed to update interest status')
      }

      // Show success notification
      setNotification({
        show: true,
        message: `Interest ${active ? 'activated' : 'deactivated'} successfully`,
        type: 'success'
      })

      // Update the local state with the new status
      setInterests(prevInterests =>
        prevInterests.map(interest => (interest.id === id ? { ...interest, active } : interest))
      )
    } catch (error) {
      console.error('Error updating interest status:', error)

      // Show error notification
      setNotification({
        show: true,
        message: error instanceof Error ? error.message : 'Failed to update interest status',
        type: 'error'
      })

      // Don't rethrow the error - let the UI handle it gracefully
      // The InterestTable component will keep the toggle in the new state
    }
  }

  // Get unique users from interests for filtering
  const availableUsers = React.useMemo(() => {
    const uniqueUsers = new Map<string, UserEntity>()

    interests.forEach(interest => {
      if (interest.user && interest.user.id) {
        uniqueUsers.set(interest.user.id, interest.user)
      }
    })

    return Array.from(uniqueUsers.values())
  }, [interests])

  return (
    <Card>
      <CardHeader
        title={
          <div className='flex items-center gap-3'>
            <div className='flex items-center gap-2'>
              <i className='ri-heart-line text-2xl text-blue-600'></i>
              <span>All Interest Records</span>
            </div>
            {selectedUserId !== 'all' && (
              // @ts-ignore
              <Chip
                label={`Filtered by: ${availableUsers.find(u => u.id === selectedUserId)?.name || 'Unknown User'}`}
                onDelete={() => setSelectedUserId('all')}
                color='primary'
                variant='outlined'
                size='small'
              />
            )}
          </div>
        }
        action={
          <div className='flex items-center gap-3'>
            {/* User Filter */}
            <FormControl size='small' style={{ minWidth: 200 }}>
              <InputLabel>Filter by User</InputLabel>
              <Select
                value={selectedUserId}
                label='Filter by User'
                onChange={e => setSelectedUserId(e.target.value)}
                disabled={usersLoading}
              >
                <MenuItem value='all'>
                  <div className='flex items-center gap-2'>
                    <i className='ri-group-line text-gray-500'></i>
                    <span>All Users</span>
                  </div>
                </MenuItem>
                {availableUsers.map(user => (
                  <MenuItem key={user.id} value={user.id}>
                    <div className='flex items-center gap-3'>
                      {user.profileImage ? (
                        <img
                          src={user.profileImage}
                          alt={user.name}
                          width={24}
                          height={24}
                          className='rounded-full object-cover'
                        />
                      ) : (
                        <div
                          className='flex items-center justify-center bg-blue-100 text-blue-600 rounded-full'
                          style={{ width: 24, height: 24, fontSize: '12px', fontWeight: 600 }}
                        >
                          {user.name?.charAt(0) || 'U'}
                        </div>
                      )}
                      <div>
                        <div className='font-medium'>{user.name}</div>
                        <div className='text-xs text-gray-500'>{user.email}</div>
                      </div>
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Group Toggle */}
            <Tooltip title={groupByUser ? 'Show as list' : 'Group by user'}>
              <IconButton onClick={() => setGroupByUser(!groupByUser)} color={groupByUser ? 'primary' : 'default'}>
                <i className={groupByUser ? 'ri-group-line' : 'ri-list-unordered'}></i>
              </IconButton>
            </Tooltip>

            {/* Add Button */}
            <Button
              variant='contained'
              startIcon={<i className='ri-add-line'></i>}
              component={Link}
              href='/interest?tab=interest'
            >
              Add Interest
            </Button>
          </div>
        }
      />
      <CardContent>
        <InterestTable
          interests={interests}
          loading={loading}
          error={error}
          groupByUser={groupByUser}
          selectedUserId={selectedUserId}
          onDeleteSuccess={handleDeleteSuccess}
          onStatusChange={handleStatusChange}
          page={page}
          limit={limit}
          total={total}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
        />
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default AllInterest
