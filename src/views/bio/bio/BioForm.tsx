'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import SimpleCropper from '@/components/SimpleCropper'
import CoverImageUpload from '@/components/CoverImageUpload'
import RichTextEditor from '@/components/RichTextEditor'
import { useBioForm } from './hooks/useBioForm'

const BioForm = () => {
  const {
    formData,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    isViewMode,
    register,
    setValue,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useBioForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Bio' : 'Add New Bio'} />
        <CardContent>
          <Typography>Loading bio data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isViewMode ? 'View Bio' : isEditMode ? 'Edit Bio' : 'Add New Bio'}
        subheader={
          isViewMode
            ? 'Bio details and information'
            : isEditMode
              ? 'Update bio information'
              : 'Create a new bio'
        }
      />
      <CardContent className='mbe-5'>
        <CoverImageUpload
          value={imgSrc !== '/images/avatars/default.png' ? imgSrc : undefined}
          onChange={(file, preview) => {
            if (file && preview) {
              handleFileInputChange({ target: { files: [file] } } as any)
            } else {
              handleFileInputReset()
            }
          }}
          onRemove={handleFileInputReset}
          onCrop={() => {
            if (imgSrc && imgSrc !== '/images/avatars/default.png') {
              handleOpenCropper()
            }
          }}
          label='Bio Cover Image'
          helperText={
            isViewMode
              ? 'Cover image for this bio'
              : 'Upload a cover image for your bio. Recommended size: 1200x400px. Use the crop tool to adjust the image.'
          }
          acceptedFormats={['image/jpeg', 'image/png', 'image/webp']}
          maxSize={2}
          loading={loading}
          disabled={loading || isViewMode}
        />
      </CardContent>
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Bio Title (Required)'
                {...register('title', { required: true })}
                error={Boolean(errors.title)}
                helperText={
                  isViewMode
                    ? 'Bio title'
                    : errors.title
                      ? 'Title is required by the backend'
                      : 'Required field in the backend schema'
                }
                value={formData.title || ''}
                onChange={e => handleFormChange('title', e.target.value)}
                margin='normal'
                disabled={isViewMode}
                InputProps={{
                  readOnly: isViewMode
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <RichTextEditor
                value={formData.description || ''}
                onChange={(value) => {
                  handleFormChange('description', value)
                  // Manually trigger validation for the hidden field
                  setValue('description', value, { shouldValidate: true })
                }}
                label='Description (Required)'
                placeholder='Enter your bio description...'
                error={Boolean(errors.description)}
                helperText={
                  isViewMode
                    ? 'Bio description content'
                    : errors.description
                      ? errors.description.message || 'Description is required'
                      : 'Rich text content will be saved as HTML'
                }
                required
                disabled={loading || isViewMode}
                height={300}
              />
              {/* Hidden input for form validation */}
              <input
                type='hidden'
                {...register('description', {
                  required: 'Description is required',
                  validate: value => {
                    // Remove HTML tags and check if there's actual content
                    const textContent = value?.replace(/<[^>]*>/g, '').trim()

                    if (!textContent || textContent.length === 0) {
                      return 'Please enter a description'
                    }

                    if (textContent.length < 10) {
                      return 'Description must be at least 10 characters'
                    }

                    return true
                  }
                })}
                value={formData.description || ''}
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            {!isViewMode && (
              <>
                <Button variant='contained' type='submit' disabled={loading}>
                  {loading ? 'Saving...' : isEditMode ? 'Update Bio' : 'Save Bio'}
                </Button>
                <Button
                  variant='outlined'
                  type='reset'
                  color='secondary'
                  onClick={() => reset(formData)}
                  disabled={loading}
                >
                  Reset
                </Button>
              </>
            )}
            {isViewMode && (
              <Button
                variant='contained'
                color='primary'
                onClick={() => router.push(`/bio?tab=bio&id=${formData.id}&mode=edit`)}
              >
                Edit Bio
              </Button>
            )}
            <Button variant='outlined' color='primary' onClick={() => router.push('/bio?tab=all')} disabled={loading}>
              {isViewMode ? 'Back to List' : 'Cancel'}
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default BioForm
