'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import SimpleCropper from '@/components/SimpleCropper'
import CoverImageUpload from '@/components/CoverImageUpload'
import RichTextEditor from '@/components/RichTextEditor'
import { useBioForm } from './hooks/useBioForm'

const BioForm = () => {
  const {
    formData,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    register,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useBioForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Bio' : 'Add New Bio'} />
        <CardContent>
          <Typography>Loading bio data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Bio' : 'Add New Bio'}
        subheader={isEditMode ? 'Update bio information' : 'Create a new bio'}
      />
      <CardContent className='mbe-5'>
        <CoverImageUpload
          value={imgSrc !== '/images/avatars/default.png' ? imgSrc : undefined}
          onChange={(file, preview) => {
            if (file && preview) {
              handleFileInputChange({ target: { files: [file] } } as any)
            } else {
              handleFileInputReset()
            }
          }}
          onRemove={handleFileInputReset}
          label="Bio Cover Image"
          helperText="Upload a cover image for your bio. Recommended size: 1200x400px"
          acceptedFormats={['image/jpeg', 'image/png', 'image/webp']}
          maxSize={2}
          loading={loading}
          disabled={loading}
        />
      </CardContent>
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Bio Title (Required)'
                {...register('title', { required: true })}
                error={Boolean(errors.title)}
                helperText={errors.title ? 'Title is required by the backend' : 'Required field in the backend schema'}
                value={formData.title || ''}
                onChange={e => handleFormChange('title', e.target.value)}
                margin='normal'
              />
            </Grid>

            <Grid item xs={12}>
              <RichTextEditor
                value={formData.description || ''}
                onChange={(value) => handleFormChange('description', value)}
                label="Description (Required)"
                placeholder="Enter your bio description..."
                error={Boolean(errors.description)}
                helperText={
                  errors.description ? 'Description is required by the backend' : 'Rich text content will be saved as HTML'
                }
                required
                disabled={loading}
                height={300}
              />
              {/* Hidden input for form validation */}
              <input
                type="hidden"
                {...register('description', {
                  required: 'Description is required',
                  validate: (value) => {
                    // Remove HTML tags and check if there's actual content
                    const textContent = value?.replace(/<[^>]*>/g, '').trim()

                    return textContent && textContent.length > 0 ? true : 'Description cannot be empty'
                  }
                })}
                value={formData.description || ''}
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update Bio' : 'Save Bio'}
            </Button>
            <Button
              variant='outlined'
              type='reset'
              color='secondary'
              onClick={() => reset(formData)}
              disabled={loading}
            >
              Reset
            </Button>
            <Button variant='outlined' color='primary' onClick={() => router.push('/bio?tab=all')} disabled={loading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default BioForm
