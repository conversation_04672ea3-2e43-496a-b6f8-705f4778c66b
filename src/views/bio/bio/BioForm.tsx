'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import SimpleCropper from '@/components/SimpleCropper'
import CoverImageUpload from '@/components/CoverImageUpload'
import RichTextEditor from '@/components/RichTextEditor'
import { useBioForm } from './hooks/useBioForm'

const BioForm = () => {
  const {
    formData,
    fileInput,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useBioForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Bio' : 'Add New Bio'} />
        <CardContent>
          <Typography>Loading bio data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Bio' : 'Add New Bio'}
        subheader={isEditMode ? 'Update bio information' : 'Create a new bio'}
      />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Bio Logo' />
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button component='label' size='small' variant='contained' htmlFor='bio-logo-upload'>
                Upload Bio Logo
                <input
                  hidden
                  type='file'
                  value={fileInput}
                  accept='image/png, image/jpeg'
                  onChange={handleFileInputChange}
                  id='bio-logo-upload'
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='primary'
                onClick={handleOpenCropper}
                disabled={!imgSrc || imgSrc === '/images/avatars/default.png'}
              >
                Crop Image
              </Button>
              <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                Reset
              </Button>
            </div>
            <Typography>Allowed JPG, GIF or PNG. Max size of 800K</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Bio Title (Required)'
                {...register('title', { required: true })}
                error={Boolean(errors.title)}
                helperText={errors.title ? 'Title is required by the backend' : 'Required field in the backend schema'}
                value={formData.title || ''}
                onChange={e => handleFormChange('title', e.target.value)}
                margin='normal'
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description (Required)'
                {...register('description', { required: true })}
                error={Boolean(errors.description)}
                helperText={
                  errors.description ? 'Description is required by the backend' : 'Required field in the backend schema'
                }
                multiline
                rows={4}
                value={formData.description || ''}
                onChange={e => handleFormChange('description', e.target.value)}
                margin='normal'
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update Bio' : 'Save Bio'}
            </Button>
            <Button
              variant='outlined'
              type='reset'
              color='secondary'
              onClick={() => reset(formData)}
              disabled={loading}
            >
              Reset
            </Button>
            <Button variant='outlined' color='primary' onClick={() => router.push('/bio?tab=all')} disabled={loading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default BioForm
