'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { useForm } from 'react-hook-form'
import type { SubmitHandler } from 'react-hook-form'

import type { BioEntity } from '@/types/entities'

// Empty bio template for new bios
export const emptyBio: BioEntity = {
  title: '',
  description: '',
  logo: '',
  active: false,
  user: undefined,
  id: ''
}

export const useBioForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const bioId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && bioId)

  // States
  const [formData, setFormData] = useState<BioEntity>(emptyBio)
  const [originalData, setOriginalData] = useState<BioEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  })

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<BioEntity>()

  // Fetch bio data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && bioId) {
      const fetchBio = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/bio/${bioId}`)

          if (!response.ok) {
            throw new Error('Failed to fetch bio')
          }

          const data = await response.json()

          // Store original data for comparison when updating
          setOriginalData(data)

          setFormData(data)
          reset(data)

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }
        } catch (err) {
          console.error('Error fetching bio:', err)
          setNotification({
            show: true,
            message: 'Failed to load bio data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchBio()
    }
  }, [bioId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const files = event.target.files

    if (files && files.length > 0) {
      const file = files[0]

      setLogoFile(file)
      setFileInput(event.target.value)

      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(file)
    }
  }

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  // Handle file input reset
  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  // Handle form field changes
  const handleFormChange = (field: keyof BioEntity, value: any) => {
    setFormData({ ...formData, [field]: value })
  }

  // Handle form submission
  const onSubmit: SubmitHandler<BioEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Add required backend fields to FormData
      // Only send fields that are in the backend schema
      formData.append('title', data.title || '')
      formData.append('description', data.description || '')

      // Add logo file if selected
      if (logoFile) {
        formData.append('logo', logoFile)
      }

      let url = '/api/bio'
      let method = 'POST'

      if (isEditMode && bioId) {
        url = `/api/bio/${bioId}`
        method = 'PATCH'
      }

      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save bio')
      }

      const resp = await res.json()

      setNotification({
        show: true,
        message: isEditMode ? 'Bio updated successfully' : 'Bio created successfully',
        type: 'success'
      })

      // Redirect to bio list after successful save
      setTimeout(() => {
        router.push('/bio?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract the error message
      let errorMessage = 'Failed to save bio'

      if (error instanceof Error) {
        errorMessage = error.message
      }

      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,

    isViewMode,
    isEditMode,
    bioId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
