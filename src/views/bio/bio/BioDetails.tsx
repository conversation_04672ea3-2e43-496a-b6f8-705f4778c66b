'use client'

// React Imports
import { useSearchParams } from 'next/navigation'

// Component Imports
import BioForm from './BioForm'
import BioView from './BioView'

const BioDetails = () => {
  // Hooks
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const id = searchParams.get('id')
  const isViewMode = mode === 'view'

  // Render the appropriate component based on mode
  return isViewMode ? <BioView /> : <BioForm />
}

export default BioDetails
