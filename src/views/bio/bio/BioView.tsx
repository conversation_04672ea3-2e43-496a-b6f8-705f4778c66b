'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import { useBioForm } from '@views/bio/bio/hooks/useBioForm'

const BioView = () => {
  const { formData, imgSrc, fetchLoading, notification, bioId, setNotification, router } = useBioForm()

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Bio Details' />
        <CardContent>
          <Typography>Loading bio data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Bio Details' />
      <CardContent className='mbe-5'>
        {/* Cover Image Display */}
        {imgSrc && imgSrc !== '/images/avatars/default.png' && (
          <div className='mb-6'>
            <img
              src={imgSrc}
              alt='Bio Cover'
              className='w-full h-48 object-cover rounded-lg'
              style={{ maxHeight: '200px' }}
            />
          </div>
        )}

        <div className='flex max-sm:flex-col items-center gap-6'>
          <div>
            <Typography variant='h4' className='mb-2'>{formData.title}</Typography>
            <Typography variant='body2' color='text.secondary'>
              Status: {formData.active ? 'Active' : 'Inactive'}
            </Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Bio Title
            </Typography>
            <Typography variant='body1'>{formData.title}</Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold' className='mb-3'>
              Description
            </Typography>
            <div
              className='prose max-w-none'
              style={{
                lineHeight: 1.6,
                fontSize: '14px',
                color: 'rgba(0, 0, 0, 0.87)'
              }}
              dangerouslySetInnerHTML={{
                __html: formData.description || '<p style="color: #999; font-style: italic;">No description provided</p>'
              }}
            />
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button variant='contained' color='primary' onClick={() => router.push(`/bio?tab=bio&id=${bioId}&mode=edit`)}>
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/bio?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default BioView
