'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'

import { useBioForm } from '@views/bio/bio/hooks/useBioForm'

const BioView = () => {
  const { formData, imgSrc, fetchLoading, notification, bioId, setNotification, router } = useBioForm()

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Bio Details' />
        <CardContent>
          <Typography>Loading bio data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Bio Details' />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Bio Logo' />
          <div>
            <Typography variant='h6'>{formData.title}</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Bio Title
            </Typography>
            <Typography variant='body1'>{formData.title}</Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Description
            </Typography>
            <Typography variant='body1'>{formData.description || 'No description provided'}</Typography>
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button variant='contained' color='primary' onClick={() => router.push(`/bio?tab=bio&id=${bioId}&mode=edit`)}>
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/bio?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default BioView
