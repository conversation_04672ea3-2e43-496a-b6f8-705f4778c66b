'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Chip from '@mui/material/Chip'
import LinearProgress from '@mui/material/LinearProgress'

import { useSkillsForm } from './hooks/useSkillsForm'

const SkillsView = () => {
  const { formData, imgSrc, loading, fetchLoading, notification, skillId, setNotification, router } = useSkillsForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title='View Skill' />
        <CardContent>
          <Typography>Loading skill data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='View Skill' subheader='View skill details' />
      <Divider />
      <CardContent>
        <div className='flex max-sm:flex-col items-center gap-6 mb-6'>
          <img
            height={100}
            width={100}
            className='rounded'
            src={imgSrc || '/images/avatars/default.png'}
            alt='Skill Logo'
          />
          <div className='flex flex-col'>
            <Typography variant='h6'>{formData.name}</Typography>
            <Typography variant='body2' color='text.secondary'>
              {formData.level}
            </Typography>
          </div>
        </div>

        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Tags
            </Typography>
            {formData.tags && formData.tags.length > 0 ? (
              <div className='flex flex-wrap gap-1 mt-1'>
                {formData.tags.map((tag, index) => (
                  // @ts-ignore
                  <Chip key={index} label={tag} color='primary' variant='outlined' />
                ))}
              </div>
            ) : (
              <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                No tags specified
              </Typography>
            )}
          </Grid>

          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Proficiency
            </Typography>
            <div className='mt-2'>
              <LinearProgress
                variant='determinate'
                value={formData.achievedPercentage || 0}
                sx={{ height: 10, borderRadius: 5 }}
              />
              <Typography variant='body2' className='mt-1 text-right'>
                {formData.achievedPercentage || 0}%
              </Typography>
            </div>
          </Grid>

          {formData.description && (
            <Grid item xs={12}>
              <Typography variant='subtitle1' fontWeight='bold'>
                Description
              </Typography>
              <Typography variant='body1' sx={{ mt: 1 }}>
                {formData.description}
              </Typography>
            </Grid>
          )}
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => {
              router.push(`/skills?id=${skillId}&mode=edit`)
            }}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/skills')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default SkillsView
