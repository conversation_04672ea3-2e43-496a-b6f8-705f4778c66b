'use client'

import {
  Box,
  Button,
  Chip,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Slider,
  TextField,
  Typography
} from '@mui/material'

import { useEntityForm } from '@/hooks/useEntityForm'
import EntityForm from '@/components/EntityForm'
import type { SkillEntity } from '@/types/entities'

// Empty skill template for new skills
export const emptySkill: SkillEntity = {
  name: '',
  logo: '',
  description: '',
  achievedPercentage: 0,
  tags: [],
  level: 'Beginner',
  active: false
}

const SkillsForm = () => {
  const {
    formData,
    fileInputs,
    filePreviews,
    loading,
    fetchLoading,
    notification,
    isEditMode,
    isViewMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    setNotification,
    reset,
    router,
    // @ts-ignore
    setValue
  } = useEntityForm<SkillEntity>({
    entityName: 'Skill',
    emptyEntity: emptySkill,
    apiEndpoint: 'skill',
    redirectPath: '/skills',
    fileFields: ['logo']
  })

  // Skill level options
  const skillLevels = ['Beginner', 'Intermediate', 'Advanced', 'Expert']

  // Common tag options (can be customized)
  const commonTags = ['Frontend', 'Backend', 'Database', 'DevOps', 'Mobile', 'Design', 'Other']

  // @ts-ignore
  return (
    <EntityForm
      title={isEditMode ? 'Edit Skill' : isViewMode ? 'View Skill' : 'Add New Skill'}
      subheader={isEditMode ? 'Update skill information' : isViewMode ? 'View skill details' : 'Create a new skill'}
      isEditMode={isEditMode}
      isViewMode={isViewMode}
      loading={loading}
      fetchLoading={fetchLoading}
      notification={notification}
      setNotification={setNotification}
      //@ts-ignore
      handleSubmit={handleSubmit}
      onSubmit={onSubmit}
      reset={() => reset(formData)}
      router={router}
      cancelPath='/skills'
    >
      <div className='flex max-sm:flex-col items-center gap-6 mb-6'>
        <img
          height={100}
          width={100}
          className='rounded'
          src={filePreviews.logo || '/images/avatars/default.png'}
          alt='Skill Logo'
        />
        {!isViewMode && (
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button component='label' size='small' variant='contained' htmlFor='skill-logo-upload'>
                Upload Logo
                <input
                  hidden
                  type='file'
                  value={fileInputs.logo}
                  accept='image/png, image/jpeg, image/svg+xml'
                  onChange={handleFileInputChange('logo')}
                  id='skill-logo-upload'
                  disabled={isViewMode}
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='error'
                onClick={handleFileInputReset('logo')}
                disabled={isViewMode}
              >
                Reset
              </Button>
            </div>
            <Typography>Allowed JPG, GIF, PNG or SVG. Max size of 800K</Typography>
          </div>
        )}
      </div>

      <Grid container spacing={5}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label='Skill Name'
            {...register('name', { required: true })}
            error={Boolean(errors.name)}
            helperText={errors.name ? 'Skill name is required' : ''}
            disabled={isViewMode}
            value={formData.name}
            onChange={e => handleFormChange('name', e.target.value)}
            margin='normal'
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin='normal'>
            <InputLabel id='skill-level-label'>Skill Level</InputLabel>
            <Select
              labelId='skill-level-label'
              id='skill-level'
              value={formData.level}
              label='Skill Level'
              disabled={isViewMode}
              //@ts-ignore
              onChange={e => handleFormChange('level', e.target.value)}
              {...register('level', { required: true })}
              error={Boolean(errors.level)}
            >
              {skillLevels.map(level => (
                <MenuItem key={level} value={level}>
                  {level}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin='normal'>
            <InputLabel id='skill-tags-label'>Tags</InputLabel>
            <Select
              labelId='skill-tags-label'
              id='skill-tags'
              multiple
              value={formData.tags || []}
              label='Tags'
              disabled={isViewMode}
              //@ts-ignore
              onChange={e => {
                const newValue = e.target.value

                //@ts-ignore
                handleFormChange('tags', newValue)
                // @ts-ignore
                setValue('tags', newValue, { shouldValidate: true })
              }}
              renderValue={selected => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {(selected as string[]).map(value => (
                    // @ts-ignore
                    <Chip key={value} label={value} />
                  ))}
                </Box>
              )}
              {...register('tags', { required: true })}
              error={Boolean(errors.tags)}
            >
              {commonTags.map(tag => (
                <MenuItem key={tag} value={tag}>
                  {tag}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={6}>
          <Typography id='proficiency-slider' gutterBottom>
            Proficiency: {formData.achievedPercentage}%
          </Typography>
          <Slider
            value={formData.achievedPercentage}
            onChange={(e, newValue) => handleFormChange('achievedPercentage', newValue as number)}
            aria-labelledby='proficiency-slider'
            valueLabelDisplay='auto'
            step={5}
            marks
            min={0}
            max={100}
            disabled={isViewMode}
            sx={{ mt: 2 }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label='Description'
            {...register('description')}
            multiline
            rows={4}
            disabled={isViewMode}
            value={formData.description}
            onChange={e => handleFormChange('description', e.target.value)}
            margin='normal'
          />
        </Grid>
      </Grid>
    </EntityForm>
  )
}

export default SkillsForm
