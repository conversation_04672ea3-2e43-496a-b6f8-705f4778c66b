'use client'

import Typography from '@mui/material/Typography'

import LinearProgress from '@mui/material/LinearProgress'

import Chip from '@mui/material/Chip'

import { type ColumnConfig } from '@/components/EntityTable'
import EntityList from '@/components/EntityList'
import type { SkillEntity } from '@/types/entities'

const SkillsList = () => {
  // Define columns for the skills table
  const columns: ColumnConfig<SkillEntity>[] = [
    {
      header: 'Skill',
      field: skill => (
        <div className='flex items-center gap-3'>
          {skill.logo ? (
            <img src={skill.logo} alt={skill.name} width={34} height={34} className='rounded' />
          ) : (
            <div
              className='flex items-center justify-center bg-primary-lightOpacity text-primary rounded'
              style={{ width: 34, height: 34 }}
            >
              {skill.name.charAt(0)}
            </div>
          )}
          <div className='flex flex-col'>
            <Typography color='text.primary' className='font-medium'>
              {skill.name}
            </Typography>
            <Typography variant='body2'>{skill.level}</Typography>
          </div>
        </div>
      )
    },
    {
      header: 'Tag',
      field: 'tags',
      // @ts-ignore
      renderCell: skill => <Chip label={skill.tag} color='primary' variant='outlined' size='small' />
    },
    {
      header: 'Proficiency',
      field: skill => (
        <div style={{ width: '100%', maxWidth: 200 }}>
          <Typography variant='body2' color='text.secondary' gutterBottom>
            {skill.achievedPercentage}%
          </Typography>
          <LinearProgress
            variant='determinate'
            value={skill.achievedPercentage}
            color={skill.achievedPercentage >= 80 ? 'success' : skill.achievedPercentage >= 50 ? 'primary' : 'warning'}
          />
        </div>
      )
    },
    {
      header: 'Description',
      field: 'description',
      renderCell: skill => (
        <Typography noWrap style={{ maxWidth: 250 }} title={skill.description}>
          {skill.description}
        </Typography>
      )
    }
  ]

  // Function to get the display name of a skill
  const getSkillName = (skill: SkillEntity) => skill.name

  return (
    <EntityList
      entityName='Skill'
      entityNamePlural='Skills'
      apiEndpoint='skill'
      basePath='skills?tab=details'
      columns={columns}
      getItemName={getSkillName}
    />
  )
}

export default SkillsList
