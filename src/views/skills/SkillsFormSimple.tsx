'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import Divider from '@mui/material/Divider'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Slider from '@mui/material/Slider'
import Box from '@mui/material/Box'
import Chip from '@mui/material/Chip'

import SimpleCropper from '@/components/SimpleCropper'
import { useSkillsForm } from './hooks/useSkillsForm'

const SkillsFormSimple = () => {
  const {
    formData,
    fileInput,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    skillLevels,
    commonTags,
    isEditMode,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useSkillsForm()

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Skill' : 'Add New Skill'} />
        <CardContent className='flex justify-center items-center p-8'>
          <CircularProgress />
          <Typography className='ml-4'>Loading skill data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Skill' : 'Add New Skill'}
        subheader={isEditMode ? 'Update skill information' : 'Create a new skill'}
      />
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='flex max-sm:flex-col items-center gap-6 mb-6'>
            <img
              height={100}
              width={100}
              className='rounded'
              src={imgSrc || '/images/avatars/default.png'}
              alt='Skill Logo'
            />
            <div className='flex flex-grow flex-col gap-4'>
              <div className='flex flex-col sm:flex-row gap-4'>
                <Button component='label' size='small' variant='contained' htmlFor='skill-logo-upload'>
                  Upload Logo
                  <input
                    hidden
                    type='file'
                    value={fileInput}
                    accept='image/png, image/jpeg, image/svg+xml'
                    onChange={handleFileInputChange}
                    id='skill-logo-upload'
                  />
                </Button>
                <Button
                  size='small'
                  variant='outlined'
                  color='primary'
                  onClick={handleOpenCropper}
                  disabled={!imgSrc || imgSrc === '/images/avatars/default.png'}
                >
                  Crop Image
                </Button>
                <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                  Reset
                </Button>
              </div>
              <Typography>Allowed JPG, GIF, PNG or SVG. Max size of 800K</Typography>
            </div>
          </div>

          <Grid container spacing={5}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name='name'
                label='Skill Name'
                value={formData.name || ''}
                onChange={e => handleFormChange('name', e.target.value)}
                required
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal'>
                <InputLabel id='skill-level-label'>Skill Level</InputLabel>
                <Select
                  labelId='skill-level-label'
                  id='skill-level'
                  value={formData.level || 'Beginner'}
                  label='Skill Level'
                  onChange={e => handleFormChange('level', e.target.value)}
                >
                  {skillLevels.map(level => (
                    <MenuItem key={level} value={level}>
                      {level}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal'>
                <InputLabel id='skill-tags-label'>Tags</InputLabel>
                <Select
                  labelId='skill-tags-label'
                  id='skill-tags'
                  multiple
                  value={formData.tags || []}
                  label='Tags'
                  onChange={e => {
                    const newValue = e.target.value

                    console.log('Select onChange called with:', newValue)
                    handleFormChange('tags', newValue)
                  }}
                  renderValue={selected => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map(value => (
                        // @ts-ignore
                        <Chip key={value} label={value} />
                      ))}
                    </Box>
                  )}
                >
                  {commonTags.map(tag => (
                    <MenuItem key={tag} value={tag}>
                      {tag}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography id='proficiency-slider' gutterBottom>
                Proficiency: {formData.achievedPercentage}%
              </Typography>
              <Slider
                value={formData.achievedPercentage || 0}
                onChange={(e, newValue) => handleFormChange('achievedPercentage', newValue as number)}
                aria-labelledby='proficiency-slider'
                valueLabelDisplay='auto'
                step={5}
                marks
                min={0}
                max={100}
                sx={{ mt: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name='description'
                label='Description'
                value={formData.description || ''}
                onChange={e => handleFormChange('description', e.target.value)}
                multiline
                rows={4}
                margin='normal'
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update' : 'Save'}
            </Button>
            <Button
              variant='outlined'
              color='secondary'
              onClick={() => {
                // Reset form with a slight delay to prevent infinite loop
                setTimeout(() => {
                  reset(formData)
                  handleFileInputReset()
                }, 0)
              }}
              disabled={loading}
              type='button'
            >
              Reset
            </Button>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => router.push('/skills')}
              disabled={loading}
              type='button'
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default SkillsFormSimple
