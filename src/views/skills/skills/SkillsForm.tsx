'use client'

import { useEffect } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Slider from '@mui/material/Slider'
import Chip from '@mui/material/Chip'
import Box from '@mui/material/Box'

import SimpleCropper from '@/components/SimpleCropper'
import { useSkillsForm } from './hooks/useSkillsForm'

const SkillsForm = () => {
  const {
    formData,
    setFormData,
    fileInput,
    imgSrc,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    isEditMode,
    register,
    control,
    setValue,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,
    setNotification,
    reset,
    router
  } = useSkillsForm()

  // Skill level options
  const skillLevels = ['Beginner', 'Intermediate', 'Advanced', 'Expert']

  // Common tag options
  const commonTags = ['Frontend', 'Backend', 'Database', 'DevOps', 'Mobile', 'Design', 'Other']

  // Log form values on mount and when formData changes
  useEffect(() => {
    if (formData.level) {
      setValue('level', formData.level, {
        shouldValidate: true,
        shouldDirty: true
      })
    }

    if (formData.tags && formData.tags.length > 0) {
      setValue('tags', formData.tags, {
        shouldValidate: true,
        shouldDirty: true
      })
    }

    if (formData.description) {
      setValue('description', formData.description, {
        shouldValidate: true,
        shouldDirty: true
      })
    }
  }, [formData, setValue])

  if (fetchLoading) {
    return (
      <Card>
        <CardHeader title={isEditMode ? 'Edit Skill' : 'Add New Skill'} />
        <CardContent>
          <Typography>Loading skill data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? 'Edit Skill' : 'Add New Skill'}
        subheader={isEditMode ? 'Update skill information' : 'Create a new skill'}
      />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Skill Logo' />
          <div className='flex flex-grow flex-col gap-4'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <Button component='label' size='small' variant='contained' htmlFor='skill-logo-upload'>
                Upload Skill Logo
                <input
                  hidden
                  type='file'
                  value={fileInput}
                  accept='image/png, image/jpeg, image/svg+xml'
                  onChange={handleFileInputChange}
                  id='skill-logo-upload'
                />
              </Button>
              <Button
                size='small'
                variant='outlined'
                color='primary'
                onClick={handleOpenCropper}
                disabled={!imgSrc || imgSrc === '/images/avatars/default.png'}
              >
                Crop Image
              </Button>
              <Button size='small' variant='outlined' color='error' onClick={handleFileInputReset}>
                Reset
              </Button>
            </div>
            <Typography>Allowed JPG, GIF, PNG or SVG. Max size of 800K</Typography>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Skill Name'
                {...register('name', { required: true })}
                error={Boolean(errors.name)}
                helperText={errors.name ? 'Skill name is required' : ''}
                value={formData.name || ''}
                onChange={e => handleFormChange('name', e.target.value)}
                margin='normal'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal'>
                <InputLabel id='skill-level-label'>Skill Level</InputLabel>
                <Select
                  labelId='skill-level-label'
                  id='skill-level'
                  value={formData.level || 'Beginner'}
                  label='Skill Level'
                  {...register('level')}
                  onChange={e => {
                    const newValue = e.target.value

                    handleFormChange('level', newValue)
                  }}
                >
                  {/*<MenuItem value=''>*/}
                  {/*  <em>Select a level</em>*/}
                  {/*</MenuItem>*/}
                  {skillLevels.map(level => (
                    <MenuItem key={level} value={level}>
                      {level}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin='normal'>
                <InputLabel id='skill-tags-label'>Tags</InputLabel>
                <Select
                  labelId='skill-tags-label'
                  id='skill-tags'
                  multiple
                  value={formData.tags || []}
                  label='Tags'
                  {...register('tags')}
                  onChange={e => {
                    const newValue = e.target.value

                    console.log('Selected tags:', newValue)
                    handleFormChange('tags', newValue)
                  }}
                  renderValue={selected => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map(value => (
                        // @ts-ignore
                        <Chip key={value} label={value} />
                      ))}
                    </Box>
                  )}
                >
                  {commonTags.map(tag => (
                    <MenuItem key={tag} value={tag}>
                      {tag}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography id='proficiency-slider' gutterBottom>
                Proficiency: {formData.achievedPercentage}%
              </Typography>
              {/* Register achievedPercentage with react-hook-form */}
              <input
                type='hidden'
                {...register('achievedPercentage', {
                  valueAsNumber: true,
                  required: true
                })}
              />

              <Slider
                value={typeof formData.achievedPercentage === 'number' ? formData.achievedPercentage : 0}
                onChange={(e, newValue) => {
                  const numericValue = Number(newValue)

                  handleFormChange('achievedPercentage', numericValue)
                }}
                aria-labelledby='proficiency-slider'
                valueLabelDisplay='auto'
                step={5}
                marks
                min={0}
                max={100}
                sx={{ mt: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Description'
                {...register('description')}
                multiline
                rows={4}
                value={formData.description || ''}
                onChange={e => handleFormChange('description', e.target.value)}
                margin='normal'
              />
            </Grid>
          </Grid>

          <div className='flex gap-4 flex-wrap mt-6'>
            <Button variant='contained' type='submit' disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update Skill' : 'Save Skill'}
            </Button>
            <Button
              variant='outlined'
              type='reset'
              color='secondary'
              onClick={() => reset(formData)}
              disabled={loading}
            >
              Reset
            </Button>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => router.push('/skills?tab=all')}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Image Cropper */}
      {cropperOpen && tempImgSrc && (
        <SimpleCropper
          image={tempImgSrc}
          open={cropperOpen}
          onClose={() => setCropperOpen(false)}
          onCropComplete={handleCroppedImage}
        />
      )}
    </Card>
  )
}

export default SkillsForm
