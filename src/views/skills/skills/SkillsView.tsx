'use client'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import Divider from '@mui/material/Divider'
import Chip from '@mui/material/Chip'
import LinearProgress from '@mui/material/LinearProgress'
import Box from '@mui/material/Box'

import { useSkillsForm } from './hooks/useSkillsForm'

const SkillsView = () => {
  const { formData, imgSrc, fetchLoading, notification, skillId, setNotification, router } = useSkillsForm()

  if (fetchLoading || !formData || Object.keys(formData).length === 0) {
    return (
      <Card>
        <CardHeader title='Skill Details' />
        <CardContent>
          <Typography>Loading skill data...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader title='Skill Details' />
      <CardContent className='mbe-5'>
        <div className='flex max-sm:flex-col items-center gap-6'>
          <img height={100} width={100} className='rounded' src={imgSrc} alt='Skill Logo' />
          <div>
            <Typography variant='h6'>{formData.name}</Typography>
            <div className='flex gap-2 mt-1'>
              {/*// @ts-ignore*/}
              <Chip label={formData.level} color='primary' size='small' />
              {formData.tags && formData.tags.length > 0 && (
                <div className='flex flex-wrap gap-1'>
                  {formData.tags.map((tag, index) => (
                    // @ts-ignore
                    <Chip key={index} label={tag} variant='outlined' size='small' />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <Divider />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Skill Name
            </Typography>
            <Typography variant='body1'>{formData.name}</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Skill Level
            </Typography>
            <Typography variant='body1'>{formData.level}</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Tags
            </Typography>
            {formData.tags && formData.tags.length > 0 ? (
              <div className='flex flex-wrap gap-1 mt-1'>
                {formData.tags.map((tag, index) => (
                  // @ts-ignore
                  <Chip key={index} label={tag} variant='outlined' size='small' />
                ))}
              </div>
            ) : (
              <Typography variant='body1'>No tags specified</Typography>
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Proficiency
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress
                  variant='determinate'
                  value={formData.achievedPercentage || 0}
                  sx={{ height: 10, borderRadius: 5 }}
                />
              </Box>
              <Box sx={{ minWidth: 35 }}>
                <Typography variant='body2' color='text.secondary'>{`${Math.round(
                  formData.achievedPercentage || 0
                )}%`}</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Typography variant='subtitle1' fontWeight='bold'>
              Description
            </Typography>
            <Typography variant='body1'>{formData.description || 'No description provided'}</Typography>
          </Grid>
        </Grid>

        <div className='flex gap-4 flex-wrap mt-6'>
          <Button
            variant='contained'
            color='primary'
            onClick={() => router.push(`/skills?tab=skills&id=${skillId}&mode=edit`)}
          >
            Edit
          </Button>
          <Button variant='outlined' color='primary' onClick={() => router.push('/skills?tab=all')}>
            Back to List
          </Button>
        </div>
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default SkillsView
