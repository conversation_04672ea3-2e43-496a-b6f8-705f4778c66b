'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'

import type { SkillEntity } from '@/types/entities'
import { extractErrorDetails } from '@/utils/errorHandling'

// @ts-ignore
export const emptySkill: SkillEntity = {
  name: '',
  logo: '',
  description: '',
  achievedPercentage: 0,
  tags: [],
  level: 'Beginner'
}

export const useSkillsForm = () => {
  // Hooks
  const searchParams = useSearchParams()
  const router = useRouter()
  const skillId = searchParams.get('id')
  const mode = searchParams.get('mode')
  const isViewMode = mode === 'view'
  const isEditMode = mode === 'edit' || (!mode && skillId)

  // States
  const [formData, setFormData] = useState<SkillEntity>(emptySkill)
  const [originalData, setOriginalData] = useState<SkillEntity | null>(null) // Store original data for comparison
  const [fileInput, setFileInput] = useState<string>('')
  const [imgSrc, setImgSrc] = useState<string>('/images/avatars/default.png')
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  // @ts-ignore
  const [fetchLoading, setFetchLoading] = useState<boolean>(isEditMode || isViewMode)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }>({
    show: false,
    message: '',
    type: 'success'
  })

  // Skill level options
  const skillLevels = ['Beginner', 'Intermediate', 'Advanced', 'Expert']

  // Common tag options (can be customized)
  const commonTags = ['Frontend', 'Backend', 'Database', 'DevOps', 'Mobile', 'Design', 'Other']

  const {
    register,
    handleSubmit,
    reset,
    control,
    setValue,
    formState: { errors }
  } = useForm<SkillEntity>({
    defaultValues: emptySkill,
    mode: 'onBlur' // Change validation mode to prevent continuous validation
  })

  // Fetch skill data if in edit or view mode
  useEffect(() => {
    if ((isEditMode || isViewMode) && skillId) {
      const fetchSkill = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/skill/${skillId}`)

          if (!response.ok) {
            const errorData = await response.json()

            throw new Error(errorData.message || 'Failed to fetch skill')
          }

          const data = await response.json()

          // Store original data for comparison when updating
          setOriginalData(data)

          // Update all state in a single batch to prevent multiple re-renders
          setFormData(data)

          // Set image if available
          if (data.logo) {
            setImgSrc(data.logo)
          }

          // Reset form with fetched data - use setTimeout to ensure it happens after render
          setTimeout(() => {
            reset(data, {
              keepDirty: false,
              keepErrors: false,
              keepIsSubmitted: false,
              keepTouched: false,
              keepIsValid: false,
              keepSubmitCount: false
            })
          }, 0)
        } catch (err) {
          console.error('Error fetching skill:', err)
          setNotification({
            show: true,
            message: err instanceof Error ? err.message : 'Failed to load skill data',
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchSkill()
    }
  }, [skillId, isEditMode, isViewMode, reset])

  // Image cropper states
  const [cropperOpen, setCropperOpen] = useState<boolean>(false)
  const [tempImgSrc, setTempImgSrc] = useState<string>('')

  // Open the cropper dialog
  const handleOpenCropper = () => {
    if (imgSrc && imgSrc !== '/images/avatars/default.png') {
      setTempImgSrc(imgSrc)
      setCropperOpen(true)
    }
  }

  // Handle cropped image
  const handleCroppedImage = (croppedImageBlob: Blob) => {
    // Create a File object from the Blob
    const croppedFile = new File([croppedImageBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

    // Store the cropped file for FormData
    setLogoFile(croppedFile)

    // Create a preview URL for the cropped image
    const reader = new FileReader()

    reader.onload = () => {
      setImgSrc(reader.result as string)
    }

    reader.readAsDataURL(croppedImageBlob)

    // Close the cropper
    setCropperOpen(false)
  }

  const onSubmit: SubmitHandler<SkillEntity> = async data => {
    try {
      setLoading(true)

      // Create FormData object
      const formDataToSend = new FormData()

      // Process form data
      Object.keys(data).forEach(key => {
        // Skip logo field as we'll handle it separately
        if (key === 'logo') return

        // Handle tags array specially
        if (key === 'tags') {
          // @ts-ignore
          let tagsArray = []

          if (Array.isArray(data[key])) {
            tagsArray = data[key]
          } else if (formData.tags && Array.isArray(formData.tags)) {
            tagsArray = formData.tags
          }

          // @ts-ignore
          formDataToSend.append(key, JSON.stringify(tagsArray))
        } else {
          // Add other fields to FormData
          // @ts-ignore
          formDataToSend.append(key, data[key] as string)
        }
      })

      // Add logo file if available
      if (logoFile) {
        formDataToSend.append('logo', logoFile)
      } else if (imgSrc && imgSrc !== '/images/avatars/default.png') {
        // If we have an existing logo URL and no new file, pass the URL
        formDataToSend.append('logo', imgSrc)
      }

      // Set up request URL and method
      let url = '/api/skill'
      let method = 'POST'

      // If editing, use PATCH method and include ID in URL
      if (isEditMode && skillId) {
        url = `/api/skill/${skillId}`
        method = 'PATCH'
      }

      // Send the request
      const res = await fetch(url, {
        method,
        body: formDataToSend
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || 'Failed to save skill')
      }

      // Handle successful response
      await res.json()

      // Show success notification
      setNotification({
        show: true,
        message: isEditMode ? 'Skill updated successfully' : 'Skill created successfully',
        type: 'success'
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/skills?tab=all')
      }, 1500)
    } catch (error) {
      console.error('Error submitting form:', error)

      // Extract error message using utility function
      const { errorMessage } = extractErrorDetails(error, 'Failed to save skill')

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFormChange = (field: keyof SkillEntity, value: any) => {
    setFormData(prevData => {
      const newData = { ...prevData, [field]: value }

      return newData
    })
  }

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      setLogoFile(files[0])

      // Update file input value
      setFileInput(event.target.value)

      // Create preview
      reader.onload = () => {
        setImgSrc(reader.result as string)
        setTempImgSrc(reader.result as string) // Also store in tempImgSrc for cropper
      }

      reader.readAsDataURL(files[0])
    }
  }

  const handleFileInputReset = () => {
    setFileInput('')
    setImgSrc('/images/avatars/default.png')
    setLogoFile(null)
  }

  return {
    formData,
    originalData,
    fileInput,
    imgSrc,
    logoFile,
    loading,
    fetchLoading,
    notification,
    cropperOpen,
    tempImgSrc,
    skillLevels,
    commonTags,

    isViewMode,
    isEditMode,
    skillId,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,
    handleCroppedImage,
    handleOpenCropper,
    setCropperOpen,

    setNotification,
    reset,
    router
  }
}
