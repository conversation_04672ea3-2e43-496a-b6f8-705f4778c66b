'use client'

import { useEffect, useState } from 'react'

export interface SocialMediaItem {
  id?: string
  name: string
  link: string
  logo: string[]
  description: string
  active: boolean
  createdAt?: Date
  updatedAt?: Date
}

export interface CreateSocialMediaData {
  name: string
  link: string
  logo?: string[] | File
  description: string
  active: boolean
}

export interface UpdateSocialMediaData extends Partial<CreateSocialMediaData> {}

export const useSocialMedia = () => {
  const [socialMediaItems, setSocialMediaItems] = useState<SocialMediaItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch all social media items
  const fetchSocialMedia = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/social-media')

      if (!response.ok) {
        throw new Error('Failed to fetch social media')
      }

      const data = await response.json()

      setSocialMediaItems(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch social media'

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Create new social media item
  const createSocialMedia = async (data: CreateSocialMediaData, logoFile?: File): Promise<SocialMediaItem | null> => {
    try {
      setLoading(true)
      setError(null)

      const formData = new FormData()

      formData.append('name', data.name)
      formData.append('link', data.link)
      formData.append('description', data.description)
      formData.append('active', data.active.toString())

      if (logoFile) {
        formData.append('logo', logoFile)
      }

      const response = await fetch('/api/social-media', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to create social media')
      }

      const newItem = await response.json()

      setSocialMediaItems(prev => [...prev, newItem])
      
return newItem
    } catch (err: any) {
      let errorMessage = 'Failed to create social media'

      if (err.response?.status === 413) {
        errorMessage = 'Image file is too large. Please use a smaller image or try compressing it further.'
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)
      
return null
    } finally {
      setLoading(false)
    }
  }

  // Update social media item
  const updateSocialMedia = async (
    id: string,
    data: UpdateSocialMediaData,
    logoFile?: File
  ): Promise<SocialMediaItem | null> => {
    try {
      setLoading(true)
      setError(null)

      const formData = new FormData()

      if (data.name) formData.append('name', data.name)
      if (data.link) formData.append('link', data.link)
      if (data.description) formData.append('description', data.description)
      if (data.active !== undefined) formData.append('active', data.active.toString())

      if (logoFile) {
        formData.append('logo', logoFile)
      }

      const response = await fetch(`/api/social-media/${id}`, {
        method: 'PATCH',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to update social media')
      }

      const updatedItem = await response.json()

      setSocialMediaItems(prev => prev.map(item => (item.id === id ? updatedItem : item)))
      
return updatedItem
    } catch (err: any) {
      let errorMessage = 'Failed to update social media'

      if (err.response?.status === 413) {
        errorMessage = 'Image file is too large. Please use a smaller image or try compressing it further.'
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)
      
return null
    } finally {
      setLoading(false)
    }
  }

  // Delete social media item
  const deleteSocialMedia = async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/social-media/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete social media')
      }

      setSocialMediaItems(prev => prev.filter(item => item.id !== id))
      
return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete social media'

      setError(errorMessage)
      
return false
    } finally {
      setLoading(false)
    }
  }

  // Toggle active status
  const toggleSocialMediaActive = async (id: string, active: boolean): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/social-media/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ active })
      })

      if (!response.ok) {
        throw new Error('Failed to update social media status')
      }

      const updatedItem = await response.json()

      setSocialMediaItems(prev => prev.map(item => (item.id === id ? updatedItem : item)))
      
return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update social media status'

      setError(errorMessage)
      
return false
    } finally {
      setLoading(false)
    }
  }

  // Load data on mount
  useEffect(() => {
    fetchSocialMedia()
  }, [])

  return {
    socialMediaItems,
    loading,
    error,
    fetchSocialMedia,
    createSocialMedia,
    updateSocialMedia,
    deleteSocialMedia,
    toggleSocialMediaActive
  }
}
