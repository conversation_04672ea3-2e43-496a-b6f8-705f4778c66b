'use client'

import { useEffect, useState } from 'react'

import { useRouter, useSearchParams } from 'next/navigation'

import { useForm } from 'react-hook-form'

type EntityFormProps<T> = {
  entityName: string
  emptyEntity: T
  apiEndpoint: string
  redirectPath: string
  fileFields?: string[]
}

export function useEntityForm<T extends Record<string, any>>({
  entityName,
  emptyEntity,
  apiEndpoint,
  redirectPath,
  fileFields = []
}: EntityFormProps<T>) {
  // States
  const [formData, setFormData] = useState<T>(emptyEntity)
  const [loading, setLoading] = useState<boolean>(false)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }>({ show: false, message: '', type: 'info' })

  // File states for each file field
  const [fileInputs, setFileInputs] = useState<Record<string, string>>({})
  const [fileObjects, setFileObjects] = useState<Record<string, File | null>>({})
  const [filePreviews, setFilePreviews] = useState<Record<string, string>>({})

  // Hooks
  const router = useRouter()
  const searchParams = useSearchParams()

  // Form setup
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
    setValue
  } = useForm<T>({
    // @ts-ignore
    defaultValues: emptyEntity
  })

  // Get query parameters
  const entityId = searchParams.get('id')
  const mode = searchParams.get('mode')

  // Determine if we're in edit or view mode
  const isEditMode = mode === 'edit'
  const isViewMode = mode === 'view'

  // Initialize file states
  useEffect(() => {
    const initialFileInputs: Record<string, string> = {}
    const initialFileObjects: Record<string, File | null> = {}
    const initialFilePreviews: Record<string, string> = {}

    fileFields.forEach(field => {
      initialFileInputs[field] = ''
      initialFileObjects[field] = null

      // Set default preview image
      const fieldValue = formData[field]

      if (fieldValue && typeof fieldValue === 'string') {
        initialFilePreviews[field] = fieldValue
      } else {
        initialFilePreviews[field] = '/images/avatars/default.png'
      }
    })

    setFileInputs(initialFileInputs)
    setFileObjects(initialFileObjects)
    setFilePreviews(initialFilePreviews)
  }, [fileFields])

  // Fetch entity data if in edit or view mode
  useEffect(() => {
    if (entityId && (isEditMode || isViewMode)) {
      const fetchEntity = async () => {
        try {
          setFetchLoading(true)
          const response = await fetch(`/api/${apiEndpoint}/${entityId}`)

          if (!response.ok) {
            // Parse the error response
            const errorData = await response.json()

            throw new Error(errorData.message || `Failed to fetch ${entityName}`)
          }

          const data = await response.json()

          // Create a new previews object instead of mutating the existing one
          const updatedPreviews = { ...filePreviews }

          fileFields.forEach(field => {
            if (data[field] && typeof data[field] === 'string') {
              updatedPreviews[field] = data[field]
            }
          })

          // Batch state updates to prevent re-renders
          // Update form data with fetched data
          setFormData(data)

          // Update file previews
          setFilePreviews(updatedPreviews)

          // Use reset instead of setValue to prevent triggering multiple re-renders
          reset(data)
        } catch (error) {
          console.error(`Error fetching ${entityName}:`, error)
          setNotification({
            show: true,
            message: error instanceof Error ? error.message : `Failed to fetch ${entityName}`,
            type: 'error'
          })
        } finally {
          setFetchLoading(false)
        }
      }

      fetchEntity()
    }
  }, [entityId, isEditMode, isViewMode, reset, apiEndpoint, entityName, fileFields, filePreviews])

  // Handle form submission
  const onSubmit = async (data: T) => {
    try {
      setLoading(true)

      // Create FormData object
      const formData = new FormData()

      // Process form data
      Object.keys(data).forEach(key => {
        // Skip file fields as we'll handle them separately
        if (fileFields.includes(key)) return

        // Handle dates
        if (data[key] instanceof Date) {
          formData.append(key, data[key].toISOString())

          return
        }

        // Add other fields
        if (data[key] !== undefined && data[key] !== null) {
          formData.append(key, data[key].toString())
        }
      })

      // Add file fields if available
      fileFields.forEach(field => {
        if (fileObjects[field]) {
          formData.append(field, fileObjects[field] as File)
        }
      })

      // Set up request URL and method
      let url = `/api/${apiEndpoint}`
      let method = 'POST'

      // If editing, use PATCH method and include ID in URL
      if (isEditMode && entityId) {
        url = `/api/${apiEndpoint}/${entityId}`
        method = 'PATCH'
      }

      // Send the request
      const res = await fetch(url, {
        method,
        body: formData
      })

      if (!res.ok) {
        // Parse the error response
        const errorData = await res.json()

        throw new Error(errorData.message || `Failed to save ${entityName}`)
      }

      // Handle successful response
      await res.json()

      // Show success notification
      setNotification({
        show: true,
        message: isEditMode ? `${entityName} updated successfully` : `${entityName} created successfully`,
        type: 'success'
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push(redirectPath)
      }, 1500)
    } catch (error) {
      console.error(`Error submitting ${entityName} form:`, error)

      // Extract error message
      let errorMessage = `Failed to save ${entityName}`

      if (error instanceof Error) {
        errorMessage = error.message
      }

      // Show error notification
      setNotification({
        show: true,
        message: errorMessage,
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle form field changes
  const handleFormChange = (field: keyof T, value: any) => {
    // Update the form data state
    setFormData(prevData => ({ ...prevData, [field]: value }))

    // Use setValue to update the form control without triggering re-renders
    // This is safer than calling both state updates simultaneously
    setValue(field as any, value, {
      shouldValidate: false,
      shouldDirty: true,
      shouldTouch: true
    })
  }

  // Handle file input changes
  const handleFileInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const reader = new FileReader()
    const { files } = event.target

    if (files && files.length !== 0) {
      // Store the actual file for FormData
      const newFileObjects = { ...fileObjects }

      newFileObjects[field] = files[0]
      setFileObjects(newFileObjects)

      // Update file input value
      const newFileInputs = { ...fileInputs }

      newFileInputs[field] = event.target.value
      setFileInputs(newFileInputs)

      // Create preview
      reader.onload = () => {
        const newFilePreviews = { ...filePreviews }

        newFilePreviews[field] = reader.result as string
        setFilePreviews(newFilePreviews)
      }

      reader.readAsDataURL(files[0])
    }
  }

  // Handle file input reset
  const handleFileInputReset = (field: string) => () => {
    // Reset file input
    const newFileInputs = { ...fileInputs }

    newFileInputs[field] = ''
    setFileInputs(newFileInputs)

    // Reset file object
    const newFileObjects = { ...fileObjects }

    newFileObjects[field] = null
    setFileObjects(newFileObjects)

    // Reset preview to default or original value
    const newFilePreviews = { ...filePreviews }

    if (formData[field] && typeof formData[field] === 'string') {
      newFilePreviews[field] = formData[field] as string
    } else {
      newFilePreviews[field] = '/images/avatars/default.png'
    }

    setFilePreviews(newFilePreviews)
  }

  return {
    formData,
    fileInputs,
    filePreviews,
    loading,
    fetchLoading,
    notification,

    isEditMode,
    isViewMode,
    register,
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleFormChange,
    handleFileInputChange,
    handleFileInputReset,

    setNotification,
    reset,
    router
  }
}
