import { useEffect, useState } from 'react'

interface CurrentUser {
  id: string
  name: string
  email: string
  profileImage?: string
  roles?: Array<{
    id: string
    name: string
    level: string
    permissions: string[]
  }>
  isAdmin?: boolean
}

export const useCurrentUser = () => {
  const [user, setUser] = useState<CurrentUser | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCurrentUser = async () => {
    try {
      setLoading(true)
      setError(null)

      // First get current user ID from auth
      const authResponse = await fetch('/api/auth/me')

      if (!authResponse.ok) {
        throw new Error('Failed to get current user')
      }

      const authData = await authResponse.json()
      const userId = authData.id

      // Then get full user data including profile image
      const userResponse = await fetch(`/api/user/${userId}`)

      if (!userResponse.ok) {
        throw new Error('Failed to fetch user data')
      }

      const userData = await userResponse.json()

      // Get user roles and permissions
      let roles = []
      let isAdmin = false

      try {
        const rolesResponse = await fetch(`/api/user-roles/user/${userId}`)

        if (rolesResponse.ok) {
          const rolesData = await rolesResponse.json()

          roles = rolesData || []

          // Check if user has admin role level
          isAdmin = roles.some(
            (role: any) => role.level === 'admin' || role.level === 'super_admin' || role.level === 'moderator'
          )
        }
      } catch (roleError) {
        console.warn('Failed to fetch user roles:', roleError)
      }

      setUser({
        id: userData.id,
        name: userData.name,
        email: userData.email,
        profileImage: userData.profileImage,
        roles,
        isAdmin
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCurrentUser()
  }, [])

  return { user, loading, error, refetch: fetchCurrentUser }
}
