'use client'

// React Imports
import React, { useState } from 'react'

// MUI Imports
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Switch from '@mui/material/Switch'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import DialogContentText from '@mui/material/DialogContentText'
import Button from '@mui/material/Button'
import Tooltip from '@mui/material/Tooltip'
import Link from '@mui/material/Link'

// Hook Imports
import { useAdminSocialMedia } from '@/hooks/useAdminSocialMedia'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

const AdminSocialMediaPage = () => {
  // Hooks
  const { socialMediaItems, loading, error, deleteSocialMedia, toggleSocialMediaActive } = useAdminSocialMedia()

  // States
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean
    socialMediaId: string | null
    socialMediaName: string
  }>({
    open: false,
    socialMediaId: null,
    socialMediaName: ''
  })

  // Group social media by user
  const groupedSocialMedia = socialMediaItems.reduce((acc: any, item: any) => {
    const userId = item.userDetails?.id || item.user || 'unknown'

    if (!acc[userId]) {
      acc[userId] = {
        user: item.userDetails || { id: userId, name: 'Unknown User', email: '', profileImage: '' },
        socialMedias: []
      }
    }

    acc[userId].socialMedias.push(item)
    
return acc
  }, {})

  // Handle delete click
  const handleDeleteClick = (socialMediaId: string | undefined, socialMediaName: string) => {
    if (!socialMediaId) return
    setDeleteDialog({
      open: true,
      socialMediaId,
      socialMediaName
    })
  }

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    if (deleteDialog.socialMediaId) {
      await deleteSocialMedia(deleteDialog.socialMediaId)
      setDeleteDialog({ open: false, socialMediaId: null, socialMediaName: '' })
    }
  }

  // Handle toggle active status
  const handleToggleActive = async (id: string | undefined, currentActive: boolean) => {
    if (!id) return
    await toggleSocialMediaActive(id, !currentActive)
  }

  if (loading) {
    return <Typography>Loading social media...</Typography>
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>Social Media</th>
              <th>Link</th>
              <th>Description</th>
              <th>Active</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {Object.keys(groupedSocialMedia).length > 0 ? (
              Object.entries(groupedSocialMedia).map(([userId, { user, socialMedias }]: [string, any]) => (
                <React.Fragment key={userId}>
                  {/* User Header Row */}
                  <tr className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-l-4 border-blue-500'>
                    <td colSpan={5} className='!plb-4 !pli-6'>
                      <div className='flex items-center gap-4'>
                        {user.profileImage ? (
                          <img
                            src={user.profileImage}
                            alt={user.name}
                            width={40}
                            height={40}
                            className='rounded-full object-cover shadow-md border-2 border-white'
                          />
                        ) : (
                          <div
                            className='flex items-center justify-center bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full shadow-md border-2 border-white font-bold'
                            style={{ width: 40, height: 40, fontSize: '16px' }}
                          >
                            {user.name?.charAt(0) || 'U'}
                          </div>
                        )}
                        <div className='flex-1'>
                          <Typography variant='h6' className='font-bold text-gray-800 dark:text-gray-100'>
                            {user.name || 'Unknown User'}
                          </Typography>
                          <Typography variant='body2' className='text-blue-600 dark:text-blue-400 font-medium'>
                            {socialMedias.length} social media account{socialMedias.length !== 1 ? 's' : ''}
                          </Typography>
                        </div>
                        <div className='text-right'>
                          <Typography variant='caption' className='text-gray-500 dark:text-gray-400'>
                            {user.email}
                          </Typography>
                        </div>
                      </div>
                    </td>
                  </tr>
                  {/* Social Media Records for this User */}
                  {socialMedias.map((socialMedia: any, index: number) => (
                    <tr
                      key={socialMedia.id}
                      className={`hover:bg-blue-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                    >
                      <td className='!plb-1'>
                        <div className='flex items-center gap-3 ml-8'>
                          {socialMedia.logo ? (
                            <img
                              src={Array.isArray(socialMedia.logo) ? socialMedia.logo[0] : socialMedia.logo}
                              alt={socialMedia.name}
                              width={32}
                              height={32}
                              className='rounded-lg object-cover shadow-sm border border-gray-200'
                            />
                          ) : (
                            <div
                              className='flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-600 rounded-lg shadow-sm border border-blue-200'
                              style={{ width: 32, height: 32, fontSize: '12px', fontWeight: 600 }}
                            >
                              {socialMedia.name.charAt(0)}
                            </div>
                          )}
                          <div>
                            <Typography className='font-semibold text-gray-900'>{socialMedia.name}</Typography>
                            <Typography variant='caption' className='text-gray-600'>
                              Social Media
                            </Typography>
                          </div>
                        </div>
                      </td>
                      <td className='!plb-1'>
                        <Link
                          href={socialMedia.link}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-600 hover:text-blue-800 font-medium'
                          sx={{
                            maxWidth: 200,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            display: 'block'
                          }}
                        >
                          {socialMedia.link}
                        </Link>
                      </td>
                      <td className='!plb-1'>
                        <Typography variant='body2' className='text-gray-700'>
                          {socialMedia.description || 'No description'}
                        </Typography>
                      </td>
                      <td className='!plb-1'>
                        <Switch
                          checked={socialMedia.active}
                          onChange={() => handleToggleActive(socialMedia.id, socialMedia.active)}
                          size='small'
                        />
                      </td>
                      <td className='!plb-1'>
                        <div className='flex gap-1'>
                          <Tooltip title='View'>
                            <IconButton size='small' component={Link} href={socialMedia.link} target='_blank'>
                              <i className='ri-eye-line'></i>
                            </IconButton>
                          </Tooltip>
                          <Tooltip title='Delete'>
                            <IconButton
                              size='small'
                              color='error'
                              onClick={() => handleDeleteClick(socialMedia.id, socialMedia.name)}
                            >
                              <i className='ri-delete-bin-line'></i>
                            </IconButton>
                          </Tooltip>
                        </div>
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td colSpan={5} className='text-center py-4'>
                  <Typography>No social media accounts found</Typography>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, socialMediaId: null, socialMediaName: '' })}
      >
        <DialogTitle>Delete Social Media</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{deleteDialog.socialMediaName}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, socialMediaId: null, socialMediaName: '' })}>
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default AdminSocialMediaPage
