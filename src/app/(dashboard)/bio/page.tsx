import React, { type ReactElement } from 'react'

import dynamic from 'next/dynamic'

import Bio from '@views/bio'

const BioTab = dynamic(() => import('@views/bio/bio'))
const AllTab = dynamic(() => import('@views/bio/all'))

const tabContentList = (): { [key: string]: ReactElement } => ({
  all: <AllTab />,
  bio: <BioTab />
})

const Page = () => {
  return <Bio tabContentList={tabContentList()} />
}

export default Page
