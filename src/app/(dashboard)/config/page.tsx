import React, { type ReactElement } from 'react'

import dynamic from 'next/dynamic'

import Config from '@views/config'

const ConfigTab = dynamic(() => import('@views/config/configs'))
const AllTab = dynamic(() => import('@views/config/all'))

const tabContentList = (): { [key: string]: ReactElement } => ({
  all: <AllTab />,
  config: <ConfigTab />
})

const Page = () => {
  return <Config tabContentList={tabContentList()} />
}

export default Page
