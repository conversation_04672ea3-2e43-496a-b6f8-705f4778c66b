import React, { type ReactElement } from 'react'

import dynamic from 'next/dynamic'

import Roles from '@views/roles'

const RoleTab = dynamic(() => import('@views/roles/RoleDetails'))
const AllTab = dynamic(() => import('@views/roles/all'))

const tabContentList = (): { [key: string]: ReactElement } => ({
  all: <AllTab />,
  role: <RoleTab />
})

const Page = () => {
  return <Roles tabContentList={tabContentList()} />
}

export default Page
