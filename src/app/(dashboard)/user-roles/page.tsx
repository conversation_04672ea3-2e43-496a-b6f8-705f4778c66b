import React, { type ReactElement } from 'react'

import dynamic from 'next/dynamic'

import UserRoles from '@views/user-roles'

const UserRoleTab = dynamic(() => import('@views/user-roles/UserRoleDetails'))
const AllTab = dynamic(() => import('@views/user-roles/all'))

const tabContentList = (): { [key: string]: ReactElement } => ({
  all: <AllTab />,
  'user-role': <UserRoleTab />
})

const Page = () => {
  return <UserRoles tabContentList={tabContentList()} />
}

export default Page
