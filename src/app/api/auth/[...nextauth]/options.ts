import { cookies } from 'next/headers'

import axios from 'axios'

import type { NextAuthOptions } from 'next-auth'

import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'

// Define session expiration time (in seconds)
const SESSION_MAX_AGE = 60 * 60 * 24 // 24 hours

export const options: NextAuthOptions = {
  session: {
    strategy: 'jwt',
    maxAge: SESSION_MAX_AGE // Match this with the cookie expiration
  },
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },

      async authorize(credentials) {
        return await axios
          .post(
            `${process.env.NEXT_PUBLIC_SERVER_URL}/auth/login`,
            {
              email: credentials?.email,
              password: credentials?.password
            },
            {
              headers: {
                'Content-Type': 'application/json'
              }
            }
          )
          .then(res => {
            const { accessToken } = res.data

            cookies().set('accessToken', accessToken, {
              expires: new Date(Date.now() + SESSION_MAX_AGE * 1000), // Match with NextAuth session
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax'
            })

            return res.data.data
          })
          .catch(() => {
            return null
          })
      }
    })
  ],

  pages: {
    signIn: '/login'

    //   signOut: '/auth/logout',
    //   error: '/auth/error', // Error code passed in query string as ?error=
    //   verifyRequest: '/auth/verify-request', // (used for check email message)
    //   newUser: null // Will disable the new company creation screen, you can override it
  }
}
