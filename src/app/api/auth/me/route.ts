import { cookies } from 'next/headers'

import { createErrorResponse } from '@/utils/errorHandling'

export async function GET() {
  try {
    // Get the access token from httpOnly cookie
    const accessToken = cookies().get('accessToken')?.value

    if (!accessToken) {
      return new Response(JSON.stringify({ message: 'No access token found' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }

    // Decode the JWT token to get user info
    // Split the token into parts
    const parts = accessToken.split('.')

    if (parts.length !== 3) {
      throw new Error('Invalid JWT token format')
    }

    // Decode the payload (second part)
    const payload = parts[1]

    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - (payload.length % 4)) % 4)

    // Decode from base64
    const decodedPayload = Buffer.from(paddedPayload, 'base64').toString('utf-8')

    // Parse JSON
    const userPayload = JSON.parse(decodedPayload)

    return new Response(
      JSON.stringify({
        id: userPayload.id,
        name: userPayload.name,
        email: userPayload.email

        // Add any other user fields you need
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error('Error getting current user:', error)
    
return createErrorResponse(error, 'Failed to get current user')
  }
}
