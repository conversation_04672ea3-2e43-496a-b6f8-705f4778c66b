import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/config`

export async function GET(request: Request) {
  try {
    const authHeader = await getAuthHeader()

    if (!authHeader) {
      return new Response(JSON.stringify({ message: 'Unauthorized - No auth header' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }

    // Extract pagination parameters from URL
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '10'

    // Build query string for pagination
    const queryParams = new URLSearchParams({
      page,
      limit
    })

    const res = await axios.get(`${baseUrl}?${queryParams.toString()}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error: unknown | any) {
    console.error('Config API - Error details:', error.response?.data || error.message)
    console.error('Config API - Error status:', error.response?.status)
    console.error('Config API - Error config:', error.config?.url)

    return createErrorResponse(error, 'Failed to fetch configurations')
  }
}

export async function POST(request: Request) {
  try {
    const authHeader = await getAuthHeader()

    if (!authHeader) {
      return new Response(JSON.stringify({ message: 'Unauthorized' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }

    // Handle FormData for file uploads
    const formData = await request.formData()

    const res = await axios.post(baseUrl, formData, {
      headers: {
        ...authHeader,
        'Content-Type': 'multipart/form-data'
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to create configuration')
  }
}
