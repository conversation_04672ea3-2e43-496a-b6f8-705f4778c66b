import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/contact`

export async function GET(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    const res = await axios.get(`${baseUrl}/${id}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to fetch contact')
  }
}

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    // Get form data from the request
    const formData = await request.formData()

    // Create a new FormData object to send to the backend
    const backendFormData = new FormData()

    // Copy all fields from the request formData to the new FormData
    for (const [key, value] of formData.entries()) {
      backendFormData.append(key, value)
    }

    const res = await axios.patch(`${baseUrl}/${id}`, backendFormData, {
      headers: {
        // Don't set Content-Type, axios will set it with the correct boundary
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to update contact')
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    await axios.delete(`${baseUrl}/${id}`, {
      headers: {
        'Content-Type': 'application/json',
        ...authHeader
      }
    })

    return new Response(JSON.stringify({ message: 'Contact deleted successfully' }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to delete contact')
  }
}
