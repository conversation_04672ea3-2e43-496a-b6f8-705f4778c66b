import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/social-media/my-social-media`

export async function GET(request: Request) {
  try {
    const authHeader = await getAuthHeader()

    // Extract query parameters from the request URL
    const url = new URL(request.url)
    const searchParams = url.searchParams

    // Build query string for backend request
    const queryString = searchParams.toString()
    const backendUrl = queryString ? `${baseUrl}?${queryString}` : baseUrl

    const res = await axios.get(backendUrl, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to fetch my social media')
  }
}
