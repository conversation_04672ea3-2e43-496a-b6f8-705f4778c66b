import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/user-roles`

export async function GET(request: Request, { params }: { params: { userId: string } }) {
  const { userId } = params

  try {
    const authHeader = await getAuthHeader()

    const res = await axios.get(`${baseUrl}/user/${userId}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to fetch user roles')
  }
}
