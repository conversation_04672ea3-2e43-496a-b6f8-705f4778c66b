import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/user-roles`

export async function GET(request: Request) {
  try {
    const authHeader = await getAuthHeader()
    const { searchParams } = new URL(request.url)

    // Build query string from search params
    const queryString = searchParams.toString()
    const url = queryString ? `${baseUrl}?${queryString}` : baseUrl

    const res = await axios.get(url, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to fetch user roles')
  }
}

export async function POST(request: Request) {
  try {
    const authHeader = await getAuthHeader()
    const body = await request.json()

    const res = await axios.post(baseUrl, body, {
      headers: {
        ...authHeader,
        'Content-Type': 'application/json'
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to create user role assignment')
  }
}
