import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/contact`

export async function GET(request: Request) {
  try {
    const authHeader = await getAuthHeader()

    // Extract pagination parameters from URL
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '10'

    // Build query string for pagination
    const queryParams = new URLSearchParams({
      page,
      limit
    })

    const res = await axios.get(`${baseUrl}?${queryParams.toString()}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    // Return detailed response for debugging
    return new Response(
      JSON.stringify({
        success: true,
        dataType: Array.isArray(data) ? 'array' : typeof data,
        dataLength: Array.isArray(data) ? data.length : data?.data?.length || 0,
        hasDataProperty: !!data.data,
        isDataArray: Array.isArray(data.data),
        totalProperty: data.total,
        pageProperty: data.page,
        originalData: data
      }),
      {
        status: res.status,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        details: (error as any)?.response?.data || 'No additional details'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}
