import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/user`

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    // Get JSON data from the request
    const { currentPassword, newPassword } = await request.json()

    // Validate input
    if (!currentPassword || !newPassword) {
      return new Response(JSON.stringify({ message: 'Current password and new password are required' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }

    if (newPassword.length < 6) {
      return new Response(JSON.stringify({ message: 'New password must be at least 6 characters long' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }

    const res = await axios.patch(
      `${baseUrl}/${id}/change-password`,
      {
        currentPassword,
        newPassword
      },
      {
        headers: {
          'Content-Type': 'application/json',
          ...authHeader
        }
      }
    )

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to change password')
  }
}
