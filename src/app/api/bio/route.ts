import axios from 'axios'

import { getAuthHeader } from '@/utils/auth'
import { createErrorResponse } from '@/utils/errorHandling'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/bio`

export async function GET(request: Request) {
  try {
    const authHeader = await getAuthHeader()

    // Extract pagination parameters from URL
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page') || '1'
    const limit = searchParams.get('limit') || '10'

    // Build query string for pagination
    const queryParams = new URLSearchParams({
      page,
      limit
    })

    const res = await axios.get(`${baseUrl}?${queryParams.toString()}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to fetch bios')
  }
}

export async function POST(request: Request) {
  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    // Get form data from the request
    const formData = await request.formData()

    // Create a new FormData object to send to the backend
    const backendFormData = new FormData()

    // Copy all fields from the request formData to the new FormData
    for (const [key, value] of formData.entries()) {
      backendFormData.append(key, value)
    }

    // Send the FormData to the backend
    const res = await axios.post(baseUrl, backendFormData, {
      headers: {
        // Don't set Content-Type, axios will set it with the correct boundary
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return createErrorResponse(error, 'Failed to create bio')
  }
}
