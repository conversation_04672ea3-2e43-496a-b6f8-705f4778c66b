import axios, { isAxiosError } from 'axios'

import { getAuthHeader } from '@/utils/auth'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/company`

export async function GET(request: Request) {
  const authHeader = await getAuthHeader()

  // Extract pagination parameters from URL
  const { searchParams } = new URL(request.url)
  const page = searchParams.get('page') || '1'
  const limit = searchParams.get('limit') || '10'

  // Build query string for pagination
  const queryParams = new URLSearchParams({
    page,
    limit
  })

  const res = await axios.get(`${baseUrl}?${queryParams.toString()}`, {
    headers: {
      ...authHeader
    }
  })

  const data = res.data

  return new Response(JSON.stringify(data), {
    status: res.status,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

export async function POST(request: Request) {
  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    // Get form data from the request
    const formData = await request.formData()

    // Create a new FormData object to send to the backend
    const backendFormData = new FormData()

    // Copy all fields from the request formData to the new FormData
    for (const [key, value] of formData.entries()) {
      backendFormData.append(key, value)
    }

    // Send the FormData to the backend
    const res = await axios.post(baseUrl, backendFormData, {
      headers: {
        // Don't set Content-Type, axios will set it with the correct boundary
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error creating company:', error)

    // Extract detailed error message
    let errorMessage = 'Failed to create company'
    let statusCode = 500

    if (isAxiosError(error) && error.response) {
      // Get status code from the response
      statusCode = error.response.status

      // Try to extract the error message from the response
      if (error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        } else {
          errorMessage = `Server error: ${JSON.stringify(error.response.data)}`
        }
      }
    } else if (error instanceof Error) {
      errorMessage = error.message
    }

    return new Response(
      JSON.stringify({
        message: errorMessage,
        status: statusCode,
        timestamp: new Date().toISOString()
      }),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}
