import axios, { isAxiosError } from 'axios'

import { getAuthHeader } from '@/utils/auth'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/company`

export async function GET(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    const res = await axios.get(`${baseUrl}/${id}`, {
      headers: {
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error fetching company:', error)

    // Extract detailed error message
    let errorMessage = 'Failed to fetch company'
    let statusCode = 500

    if (isAxiosError(error) && error.response) {
      // Get status code from the response
      statusCode = error.response.status

      // Try to extract the error message from the response
      if (error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        } else {
          errorMessage = `Server error: ${JSON.stringify(error.response.data)}`
        }
      }
    } else if (error instanceof Error) {
      errorMessage = error.message
    }

    return new Response(
      JSON.stringify({
        message: errorMessage,
        status: statusCode,
        timestamp: new Date().toISOString()
      }),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

export async function PATCH(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    // Get form data from the request
    const formData = await request.formData()

    // Create a new FormData object to send to the backend
    const backendFormData = new FormData()

    // Copy all fields from the request formData to the new FormData
    for (const [key, value] of formData.entries()) {
      backendFormData.append(key, value)
    }

    const res = await axios.patch(`${baseUrl}/${id}`, backendFormData, {
      headers: {
        // Don't set Content-Type, axios will set it with the correct boundary
        ...authHeader
      }
    })

    const data = res.data

    return new Response(JSON.stringify(data), {
      status: res.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error updating company:', error)

    // Extract detailed error message
    let errorMessage = 'Failed to update company'
    let statusCode = 500

    if (isAxiosError(error) && error.response) {
      // Get status code from the response
      statusCode = error.response.status

      // Try to extract the error message from the response
      if (error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        } else {
          errorMessage = `Server error: ${JSON.stringify(error.response.data)}`
        }
      }
    } else if (error instanceof Error) {
      errorMessage = error.message
    }

    return new Response(
      JSON.stringify({
        message: errorMessage,
        status: statusCode,
        timestamp: new Date().toISOString()
      }),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  const { id } = params

  // Get auth header from our utility
  const authHeader = await getAuthHeader()

  // If not authenticated, return 401
  if (!authHeader) {
    return new Response(JSON.stringify({ message: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  try {
    await axios.delete(`${baseUrl}/${id}`, {
      headers: {
        'Content-Type': 'application/json',
        ...authHeader
      }
    })

    return new Response(JSON.stringify({ message: 'Company deleted successfully' }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error deleting company:', error)

    // Extract detailed error message
    let errorMessage = 'Failed to delete company'
    let statusCode = 500

    if (isAxiosError(error) && error.response) {
      // Get status code from the response
      statusCode = error.response.status

      // Try to extract the error message from the response
      if (error.response.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        } else {
          errorMessage = `Server error: ${JSON.stringify(error.response.data)}`
        }
      }
    } else if (error instanceof Error) {
      errorMessage = error.message
    }

    return new Response(
      JSON.stringify({
        message: errorMessage,
        status: statusCode,
        timestamp: new Date().toISOString()
      }),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}
