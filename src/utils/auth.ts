'use server'

import { cookies } from 'next/headers'

import { getServerSession } from 'next-auth/next'

import { options } from '@/app/api/auth/[...nextauth]/options'

/**
 * Checks if the user is authenticated by verifying both NextAuth session and access token
 * @returns Object containing authentication status and token
 */
export async function checkAuth() {
  // Get the NextAuth session
  const session = await getServerSession(options)

  // Get the access token from cookies
  const accessToken = cookies().get('accessToken')?.value

  // User is authenticated if both session and token exist
  const isAuthenticated = !!session && !!accessToken

  return {
    isAuthenticated,
    accessToken,
    session
  }
}

/**
 * Gets the authorization header for API requests
 * @returns Authorization header object or null if not authenticated
 */
export async function getAuthHeader() {
  const { isAuthenticated, accessToken } = await checkAuth()

  if (!isAuthenticated || !accessToken) {
    return null
  }

  return {
    Authorization: `Bearer ${accessToken}`
  }
}
