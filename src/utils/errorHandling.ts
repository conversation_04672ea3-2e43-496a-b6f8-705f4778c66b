import { isAxiosError } from 'axios'

/**
 * Extracts a detailed error message from various error types
 * @param error The error object
 * @param defaultMessage Default message to return if no specific error is found
 * @returns An object containing the error message and status code
 */
export const extractErrorDetails = (
  error: unknown,
  defaultMessage: string
): {
  errorMessage: string
  statusCode: number
} => {
  let errorMessage = defaultMessage
  let statusCode = 500

  if (isAxiosError(error) && error.response) {
    // Get status code from the response
    statusCode = error.response.status

    // Try to extract the error message from the response
    if (error.response.data) {
      if (typeof error.response.data === 'string') {
        errorMessage = error.response.data
      } else if (error.response.data.message) {
        errorMessage = error.response.data.message
      } else if (error.response.data.error) {
        errorMessage = error.response.data.error
      } else {
        errorMessage = `Server error: ${JSON.stringify(error.response.data)}`
      }
    }
  } else if (error instanceof Error) {
    errorMessage = error.message
  }

  return { errorMessage, statusCode }
}

/**
 * Creates a standardized error response
 * @param error The error object
 * @param defaultMessage Default message to return if no specific error is found
 * @returns A Response object with the error details
 */
export const createErrorResponse = (error: unknown, defaultMessage: string): Response => {
  console.error(`Error: ${defaultMessage}`, error)

  const { errorMessage, statusCode } = extractErrorDetails(error, defaultMessage)

  return new Response(
    JSON.stringify({
      message: errorMessage,
      status: statusCode,
      timestamp: new Date().toISOString()
    }),
    {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )
}
