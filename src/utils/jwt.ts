'use client'

/**
 * Decode JWT token without verification (client-side only)
 * Note: This is for extracting payload data only, not for security validation
 */
export function decodeJWT(token: string) {
  try {
    // Split the token into parts
    const parts = token.split('.')

    if (parts.length !== 3) {
      throw new Error('Invalid JWT token format')
    }

    // Decode the payload (second part)
    const payload = parts[1]

    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - (payload.length % 4)) % 4)

    // Decode from base64
    const decodedPayload = atob(paddedPayload)

    // Parse JSON
    return JSON.parse(decodedPayload)
  } catch (error) {
    console.error('Error decoding JWT:', error)
    
return null
  }
}

/**
 * Get cookie value by name
 */
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') {
    return null // Server-side, return null
  }

  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)

  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null
  }

  
return null
}

/**
 * Get current user ID from JWT token stored in cookies
 */
export function getCurrentUserId(): string | null {
  try {
    if (typeof window === 'undefined') {
      return null // Server-side, return null
    }

    const token = getCookie('accessToken')

    console.log({ token })

    if (!token) {
      console.log('No accessToken found in cookies')
      
return null
    }

    console.log('Found token in cookies:', token.substring(0, 20) + '...')
    const payload = decodeJWT(token)

    console.log('Decoded payload:', payload)
    
return payload?.id || null
  } catch (error) {
    console.error('Error getting current user ID:', error)
    
return null
  }
}

/**
 * Get current user data from JWT token stored in cookies
 */
export function getCurrentUser() {
  try {
    if (typeof window === 'undefined') {
      return null // Server-side, return null
    }

    const token = getCookie('accessToken')

    console.log({ token })

    if (!token) {
      return null
    }

    const payload = decodeJWT(token)

    
return payload
      ? {
          id: payload.id,
          name: payload.name,
          email: payload.email
        }
      : null
  } catch (error) {
    console.error('Error getting current user:', error)
    
return null
  }
}
