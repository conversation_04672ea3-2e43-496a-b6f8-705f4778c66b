'use client'

import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import axios from 'axios'
import { signOut } from 'next-auth/react'
import { toast } from 'react-hot-toast'

// Create a custom axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_SERVER_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token to requests
apiClient.interceptors.request.use(
  // @ts-ignore
  (config: AxiosRequestConfig) => {
    // Get token from localStorage or cookies in client-side
    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any

    // Handle authentication errors (401 Unauthorized, 403 Forbidden)
    if (error.response?.status === 401 || error.response?.status === 403) {
      // Prevent infinite loops
      if (!originalRequest._retry) {
        originalRequest._retry = true

        // Show error message
        toast.error('Your session has expired. Please log in again.')

        // Sign out the user and redirect to login page
        await signOut({ redirect: true, callbackUrl: '/login' })

        return Promise.reject(error)
      }
    }

    return Promise.reject(error)
  }
)

export default apiClient
