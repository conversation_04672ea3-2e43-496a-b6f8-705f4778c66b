import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getToken } from 'next-auth/jwt'

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname

  // Define public paths that don't require authentication
  const isPublicPath = path === '/login' || path === '/register'

  // Get the token and session
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  })

  // Check if access token cookie exists
  const accessToken = request.cookies.get('accessToken')?.value

  // User is authenticated if both NextAuth token and access token exist
  const isAuthenticated = !!token && !!accessToken

  // Redirect logic
  if (isPublicPath && isAuthenticated) {
    // If user is on a public path but is authenticated, redirect to dashboard
    return NextResponse.redirect(new URL('/', request.url))
  }

  if (!isPublicPath && !isAuthenticated) {
    // If user is on a protected path but is not authenticated, redirect to login
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

// Configure which routes use this middleware
export const config = {
  matcher: [
    // Match all paths except for:
    // - API routes (/api/...)
    // - Static files routes (/_next/...)
    // - Public files (favicon.ico, etc.)
    '/((?!api|_next/static|_next/image|favicon.ico|images|.*\.svg).*)'
  ]
}
