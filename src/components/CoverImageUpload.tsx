'use client'

import { useState, useRef } from 'react'

import {
  Box,
  Typography,
  IconButton,
  Card,
  CardMedia,
  FormHelperText,
  CircularProgress
} from '@mui/material'
import { styled } from '@mui/system'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import DeleteIcon from '@mui/icons-material/Delete'
import EditIcon from '@mui/icons-material/Edit'

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1
})

const CoverImageContainer = styled(Card)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: 200,
  backgroundColor: theme.palette.grey[100],
  border: `2px dashed ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.grey[50]
  }
}))

const OverlayActions = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing(1),
  opacity: 0,
  transition: 'opacity 0.3s ease',
  '&:hover': {
    opacity: 1
  }
}))

interface CoverImageUploadProps {
  value?: string
  onChange: (file: File | null, preview: string | null) => void
  onRemove?: () => void
  label?: string
  error?: boolean
  helperText?: string
  required?: boolean
  disabled?: boolean
  loading?: boolean
  acceptedFormats?: string[]
  maxSize?: number // in MB
}

const CoverImageUpload = ({
  value,
  onChange,
  onRemove,
  label = 'Cover Image',
  error = false,
  helperText,
  required = false,
  disabled = false,
  loading = false,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  maxSize = 5
}: CoverImageUploadProps) => {
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (file: File) => {
    if (!acceptedFormats.includes(file.type)) {
      alert(`Please select a valid image format: ${acceptedFormats.join(', ')}`)

      return
    }

    if (file.size > maxSize * 1024 * 1024) {
      alert(`File size must be less than ${maxSize}MB`)

      return
    }

    const reader = new FileReader()

    reader.onload = (e) => {
      const preview = e.target?.result as string

      onChange(file, preview)
    }

    reader.readAsDataURL(file)
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]

    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)

    const file = event.dataTransfer.files[0]

    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = () => {
    setDragOver(false)
  }

  const handleRemove = () => {
    onChange(null, null)

    if (onRemove) {
      onRemove()
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <Box>
      {label && (
        <Typography
          variant="body2"
          component="label"
          sx={{
            display: 'block',
            mb: 1,
            fontWeight: 500,
            color: error ? 'error.main' : 'text.primary'
          }}
        >
          {label}
          {required && (
            <Typography component="span" color="error.main" sx={{ ml: 0.5 }}>
              *
            </Typography>
          )}
        </Typography>
      )}

      <CoverImageContainer
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        sx={{
          borderColor: error ? 'error.main' : dragOver ? 'primary.main' : 'grey.300',
          backgroundColor: dragOver ? 'primary.50' : 'grey.100',
          opacity: disabled ? 0.6 : 1,
          cursor: disabled ? 'not-allowed' : 'pointer'
        }}
      >
        {value ? (
          <>
            <CardMedia
              component="img"
              height="100%"
              image={value}
              alt="Cover image"
              sx={{ objectFit: 'cover' }}
            />
            {!disabled && (
              <OverlayActions>
                <IconButton
                  size="small"
                  sx={{ color: 'white', backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleClick()
                  }}
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  size="small"
                  sx={{ color: 'white', backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRemove()
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </OverlayActions>
            )}
            {disabled && value && (
              <OverlayActions sx={{ opacity: 0.7 }}>
                <Typography
                  variant="caption"
                  sx={{
                    color: 'white',
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    padding: '4px 8px',
                    borderRadius: 1
                  }}
                >
                  View Mode
                </Typography>
              </OverlayActions>
            )}
          </>
        ) : (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              p: 2,
              textAlign: 'center'
            }}
          >
            {loading ? (
              <CircularProgress size={40} />
            ) : (
              <>
                <CloudUploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Drop your cover image here or click to browse
                </Typography>
                <Typography variant="caption" color="text.disabled">
                  Supports: {acceptedFormats.map(f => f.split('/')[1]).join(', ')} (Max {maxSize}MB)
                </Typography>
              </>
            )}
          </Box>
        )}
      </CoverImageContainer>

      <VisuallyHiddenInput
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleInputChange}
        disabled={disabled}
      />

      {helperText && (
        <FormHelperText error={error} sx={{ mt: 1 }}>
          {helperText}
        </FormHelperText>
      )}
    </Box>
  )
}

export default CoverImageUpload
