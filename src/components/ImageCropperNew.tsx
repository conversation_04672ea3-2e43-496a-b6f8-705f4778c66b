'use client'

import { useEffect, useRef, useState } from 'react'

import ReactCrop, { type PixelCrop, type Crop } from 'react-image-crop';
import { centerCrop, makeAspectCrop } from 'react-image-crop';

import 'react-image-crop/dist/ReactCrop.css'
import { Box, Button, Dialog, DialogActions, DialogContent, Typography } from '@mui/material'

interface ImageCropperProps {
  image: string
  open: boolean
  onClose: () => void
  onCropComplete: (croppedImageBlob: Blob) => void
  aspect?: number
}

function centerAspectCrop(mediaWidth: number, mediaHeight: number, aspect: number) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90
      },
      aspect,
      mediaWidth,
      mediaHeight
    ),
    mediaWidth,
    mediaHeight
  )
}

const ImageCropperNew = ({ image, open, onClose, onCropComplete, aspect = 1 }: ImageCropperProps) => {
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>()
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    if (open && imgRef.current) {
      const { width, height } = imgRef.current

      setCrop(centerAspectCrop(width, height, aspect))
    }
  }, [open, aspect])

  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const { width, height } = e.currentTarget

    setCrop(centerAspectCrop(width, height, aspect))
  }

  const handleCropImage = async () => {
    try {
      if (completedCrop && imgRef.current) {
        const croppedImageBlob = await getCroppedImg(imgRef.current, completedCrop, 'cropped-image.jpg')

        onCropComplete(croppedImageBlob)
      }
    } catch (e) {
      console.error('Error cropping image:', e)
    }
  }

  function getCroppedImg(image: HTMLImageElement, crop: PixelCrop, fileName: string): Promise<Blob> {
    const canvas = document.createElement('canvas')
    const scaleX = image.naturalWidth / image.width
    const scaleY = image.naturalHeight / image.height
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      throw new Error('No 2d context')
    }

    canvas.width = crop.width * scaleX
    canvas.height = crop.height * scaleY

    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width * scaleX,
      crop.height * scaleY
    )

    return new Promise((resolve, reject) => {
      canvas.toBlob(
        blob => {
          if (!blob) {
            reject(new Error('Canvas is empty'))
            
return
          }

          resolve(blob)
        },
        'image/jpeg',
        1
      )
    })
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='md'
      fullWidth
      PaperProps={{
        sx: {
          height: 'auto',
          maxHeight: '90vh',
          width: '100%',
          maxWidth: '600px'
        }
      }}
    >
      <DialogContent sx={{ padding: 2 }}>
        <Typography variant='h6' gutterBottom>
          Crop Image
        </Typography>
        <Typography variant='body2' gutterBottom>
          Drag to reposition. Resize the box to adjust the crop area.
        </Typography>
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            mt: 2,
            '& .ReactCrop': {
              maxHeight: '60vh',
              maxWidth: '100%'
            }
          }}
        >
          <ReactCrop
            crop={crop}
            onChange={(_, percentCrop) => setCrop(percentCrop)}
            onComplete={c => setCompletedCrop(c)}
            aspect={aspect}
            circularCrop={false}
          >
            <img
              ref={imgRef}
              alt='Crop me'
              src={image}
              style={{ maxWidth: '100%', maxHeight: '60vh' }}
              onLoad={onImageLoad}
            />
          </ReactCrop>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color='primary'>
          Cancel
        </Button>
        <Button
          onClick={handleCropImage}
          color='primary'
          variant='contained'
          disabled={!completedCrop?.width || !completedCrop?.height}
        >
          Crop Image
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ImageCropperNew
