.wrapper {
  position: fixed;
  inset-inline-end: 1.5rem;
  inset-block-end: 3.5rem;
  z-index: 18;
}

.button,
.buttonInner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  outline: 0px;
  border: 0px;
  margin: 0px;
  cursor: pointer;
  vertical-align: middle;
  appearance: none;
  text-decoration: none;
  line-height: 1.2;
  font-size: 0.9375rem;
  letter-spacing: 0.43px;
  font-weight: 500;
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  min-inline-size: 50px;
  text-transform: none;
  padding-block: 0.625rem;
  padding-inline: 1.25rem;
  background: linear-gradient(-45deg, #ffa63d, #ff3d77, #338aff, #3cf0c5);
  background-size: 600%;
  animation: anime 12s linear infinite;
}

.button {
  position: relative;
  &:hover {
    color: white;
    text-decoration: none;
  }

  .buttonInner {
    position: absolute;
    inset: 0;
    z-index: -1;
    filter: blur(12px);
    opacity: 0;
    transition: opacity 200ms ease-in-out;
  }
  &:not(:hover) .buttonInner {
    opacity: 0.8;
  }
}

@keyframes anime {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
