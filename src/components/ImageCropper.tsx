'use client'

import { useCallback, useState } from 'react'

import <PERSON><PERSON><PERSON> from 'react-easy-crop'
import { Box, Button, Dialog, DialogActions, DialogContent, Slider, Typography } from '@mui/material'

// Import CSS from the package
import 'react-easy-crop/react-easy-crop.css'

// Define the Area type for cropped area
type Area = {
  x: number
  y: number
  width: number
  height: number
}

interface ImageCropperProps {
  image: string
  open: boolean
  onClose: () => void
  onCropComplete: (croppedImageBlob: Blob) => void
}

// Function to create an image from a source
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image()

    image.addEventListener('load', () => resolve(image))
    image.addEventListener('error', error => reject(error))
    image.src = url
  })

// Function to get the cropped image
async function getCroppedImg(imageSrc: string, pixelCrop: Area, rotation = 0): Promise<Blob> {
  const image = await createImage(imageSrc)
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    throw new Error('Canvas context is not available')
  }

  // Set canvas dimensions to match the cropped area
  const maxSize = Math.max(image.width, image.height)
  const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2))

  // Set dimensions of the canvas
  canvas.width = pixelCrop.width
  canvas.height = pixelCrop.height

  // Draw the cropped image onto the canvas
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  // Translate to center of canvas
  ctx.translate(canvas.width / 2, canvas.height / 2)
  ctx.rotate((rotation * Math.PI) / 180)
  ctx.translate(-canvas.width / 2, -canvas.height / 2)

  // Draw the image at the correct position
  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    pixelCrop.width,
    pixelCrop.height
  )

  // Convert canvas to blob
  return new Promise(resolve => {
    canvas.toBlob(blob => {
      if (!blob) {
        throw new Error('Canvas is empty')
      }

      resolve(blob)
    }, 'image/jpeg')
  })
}

const ImageCropper = ({ image, open, onClose, onCropComplete }: ImageCropperProps) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null)

  const onCropChange = (crop: { x: number; y: number }) => {
    setCrop(crop)
  }

  const onZoomChange = (zoom: number) => {
    setZoom(zoom)
  }

  const onRotationChange = (rotation: number) => {
    setRotation(rotation)
  }

  const onCropCompleteHandler = useCallback((_: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels)
  }, [])

  const handleCropImage = useCallback(async () => {
    try {
      if (croppedAreaPixels) {
        const croppedImage = await getCroppedImg(image, croppedAreaPixels, rotation)

        onCropComplete(croppedImage)
      } else {
        console.error('No cropped area pixels available')
      }
    } catch (e) {
      console.error('Error cropping image:', e)
    }
  }, [croppedAreaPixels, image, onCropComplete, rotation])

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='md'
      fullWidth
      PaperProps={{
        sx: {
          height: '80vh',
          maxHeight: '600px'
        }
      }}
    >
      <DialogContent
        sx={{
          position: 'relative',
          height: '450px',
          padding: 0,
          backgroundColor: '#333'
        }}
      >
        <Cropper
          image={image}
          crop={crop}
          zoom={zoom}
          rotation={rotation}
          aspect={1}
          onCropChange={onCropChange}
          onZoomChange={onZoomChange}
          onCropComplete={onCropCompleteHandler}
        />
      </DialogContent>
      <Box sx={{ px: 3, pt: 2, pb: 1 }}>
        <Typography variant='body2' gutterBottom>
          Zoom
        </Typography>
        <Slider
          value={zoom}
          min={1}
          max={3}
          step={0.1}
          aria-labelledby='zoom-slider'
          onChange={(_, value) => setZoom(value as number)}
        />
        <Typography variant='body2' gutterBottom sx={{ mt: 2 }}>
          Rotation
        </Typography>
        <Slider
          value={rotation}
          min={0}
          max={360}
          step={1}
          aria-labelledby='rotation-slider'
          onChange={(_, value) => setRotation(value as number)}
        />
      </Box>
      <DialogActions>
        <Button onClick={onClose} color='primary'>
          Cancel
        </Button>
        <Button onClick={handleCropImage} color='primary' variant='contained'>
          Crop Image
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ImageCropper
