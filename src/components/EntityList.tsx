'use client'

import { useEffect, useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Snackbar from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'

// Component Imports
import EntityTable, { type ColumnConfig } from './EntityTable'

type EntityListProps<T extends { id?: string; name?: string }> = {
  entityName: string
  entityNamePlural: string
  apiEndpoint: string
  basePath: string
  columns: ColumnConfig<T>[]
  getItemName?: (item: T) => string
}

function EntityList<T extends { id?: string; name?: string }>({
  entityName,
  entityNamePlural,
  apiEndpoint,
  basePath,
  columns,
  getItemName
}: EntityListProps<T>) {
  // States
  const [items, setItems] = useState<T[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const [notification, setNotification] = useState<{
    show: boolean
    message: string
    type: 'success' | 'error' | 'info' | 'warning'
  }>({ show: false, message: '', type: 'info' })

  // Fetch items function
  const fetchItems = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/${apiEndpoint}`)

      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json()

        throw new Error(errorData.message || `Failed to fetch ${entityNamePlural}`)
      }

      const data = await response.json()

      setItems(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `An error occurred while fetching ${entityNamePlural}`

      setError(errorMessage)
      console.error(`Error fetching ${entityNamePlural}:`, err)
    } finally {
      setLoading(false)
    }
  }

  // Fetch items on component mount
  useEffect(() => {
    fetchItems()
  }, [])

  // Handle successful deletion
  const handleDeleteSuccess = (deletedItemId: string) => {
    setNotification({
      show: true,
      message: `${entityName} deleted successfully`,
      type: 'success'
    })

    // Update local state by removing the deleted item
    setItems(prevItems => prevItems.filter(item => item.id !== deletedItemId))
  }

  return (
    <Card>
      <CardHeader
        title={`All ${entityNamePlural}`}
        action={
          <Button
            variant='contained'
            startIcon={<i className='ri-add-line'></i>}
            component={Link}
            href={`/${basePath}?tab=form`}
          >
            Add {entityName}
          </Button>
        }
      />
      <CardContent>
        <EntityTable
          items={items}
          columns={columns}
          entityName={entityName}
          entityNamePlural={entityNamePlural}
          apiEndpoint={apiEndpoint}
          basePath={basePath}
          loading={loading}
          error={error}
          onDeleteSuccess={handleDeleteSuccess}
          getItemName={getItemName}
        />
      </CardContent>

      {/* Notification */}
      <Snackbar
        open={notification.show}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, show: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, show: false })}
          severity={notification.type}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Card>
  )
}

export default EntityList
