'use client'

import { useState } from 'react'

import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import Button from '@mui/material/Button'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

// Define column configuration type
export type ColumnConfig<T> = {
  header: string
  field: keyof T | ((item: T) => React.ReactNode)
  renderCell?: (item: T) => React.ReactNode
}

type EntityTableProps<T extends { id?: string; name?: string }> = {
  items: T[]
  columns: ColumnConfig<T>[]
  entityName: string
  entityNamePlural: string
  apiEndpoint: string
  basePath: string
  loading: boolean
  error: string | null
  onDeleteSuccess: (deletedItemId: string) => void
  getItemName?: (item: T) => string
}

function EntityTable<T extends { id?: string; name?: string }>({
  items,
  columns,
  entityName,
  entityNamePlural,
  apiEndpoint,
  basePath,
  loading,
  error,
  onDeleteSuccess,
  getItemName = item => item.name || 'this item'
}: EntityTableProps<T>) {
  // States
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean
    itemId: string | null
    itemName: string
  }>({
    open: false,
    itemId: null,
    itemName: ''
  })

  // State for error notification
  const [deleteError, setDeleteError] = useState<string | null>(null)

  // Handle opening delete confirmation dialog
  const handleDeleteClick = (itemId: string | undefined, itemName: string) => {
    if (!itemId) return

    setDeleteDialog({
      open: true,
      itemId,
      itemName
    })
  }

  // Handle closing delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      itemId: null,
      itemName: ''
    })
    setDeleteError(null) // Reset error state when closing dialog
  }

  // Handle confirming delete action
  const handleConfirmDelete = async () => {
    if (!deleteDialog.itemId) return

    const itemIdToDelete = deleteDialog.itemId

    setDeleteError(null) // Reset error state

    try {
      const response = await fetch(`/api/${apiEndpoint}/${itemIdToDelete}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json()

        throw new Error(errorData.message || `Failed to delete ${entityName}`)
      }

      // Notify parent component of successful deletion with item ID
      onDeleteSuccess(itemIdToDelete)
      handleCloseDeleteDialog()
    } catch (err) {
      console.error(`Error deleting ${entityName}:`, err)

      // Extract error message
      let errorMessage = `Failed to delete ${entityName}`

      if (err instanceof Error) {
        errorMessage = err.message
      }

      // Set error message to display in dialog
      setDeleteError(errorMessage)
    }
  }

  if (loading) {
    return <Typography>Loading {entityNamePlural}...</Typography>
  }

  if (error) {
    return <Typography color='error'>{error}</Typography>
  }

  return (
    <>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              {columns.map((column, index) => (
                <th key={index}>{column.header}</th>
              ))}
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {items && items.length > 0 ? (
              items.map(item => (
                <tr key={item.id}>
                  {columns.map((column, index) => (
                    <td key={index} className='!plb-1'>
                      {typeof column.field === 'function'
                        ? column.field(item)
                        : column.renderCell
                          ? column.renderCell(item)
                          : item[column.field] !== undefined
                            ? String(item[column.field])
                            : ''}
                    </td>
                  ))}
                  <td className='!plb-1'>
                    <div className='flex gap-2'>
                      <Tooltip title='View'>
                        <IconButton size='small' component={Link} href={`/${basePath}?id=${item.id}&mode=view`}>
                          <i className='ri-eye-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Edit'>
                        <IconButton size='small' component={Link} href={`/${basePath}?id=${item.id}&mode=edit`}>
                          <i className='ri-pencil-line'></i>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title='Delete'>
                        <IconButton
                          size='small'
                          color='error'
                          onClick={() => handleDeleteClick(item.id, getItemName(item))}
                        >
                          <i className='ri-delete-bin-line'></i>
                        </IconButton>
                      </Tooltip>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length + 1} className='text-center py-4'>
                  <Typography>No {entityNamePlural} found</Typography>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
      >
        <DialogTitle id='delete-dialog-title'>Delete {entityName}</DialogTitle>
        <DialogContent>
          <DialogContentText id='delete-dialog-description'>
            Are you sure you want to delete <strong>{deleteDialog.itemName}</strong>? This action cannot be undone.
          </DialogContentText>
          {deleteError && (
            <Typography color='error' sx={{ mt: 2 }}>
              Error: {deleteError}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color='primary'>
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default EntityTable
