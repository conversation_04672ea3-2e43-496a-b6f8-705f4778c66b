'use client'

import { useRef, useState } from 'react'

import ReactCrop, { type PixelCrop, type Crop } from 'react-image-crop';

import 'react-image-crop/dist/ReactCrop.css'
import { Button, Dialog, DialogActions, DialogContent } from '@mui/material'

interface SimpleCropperProps {
  image: string
  open: boolean
  onClose: () => void
  onCropComplete: (croppedImageBlob: Blob) => void
}

const SimpleCropper = ({ image, open, onClose, onCropComplete }: SimpleCropperProps) => {
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>()
  const imgRef = useRef<HTMLImageElement>(null)

  const handleCropImage = async () => {
    try {
      if (completedCrop && imgRef.current) {
        const canvas = document.createElement('canvas')
        const scaleX = imgRef.current.naturalWidth / imgRef.current.width
        const scaleY = imgRef.current.naturalHeight / imgRef.current.height
        const ctx = canvas.getContext('2d')

        if (!ctx) {
          throw new Error('No 2d context')
        }

        // For circular crop, we need a square canvas
        const size = Math.min(completedCrop.width, completedCrop.height) * scaleX

        canvas.width = size
        canvas.height = size

        // Create circular clipping path
        ctx.beginPath()
        ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2)
        ctx.clip()

        // Calculate centering offset if the crop is not square
        const xOffset = (completedCrop.width * scaleX - size) / 2
        const yOffset = (completedCrop.height * scaleY - size) / 2

        // Draw the image with the circular clipping path applied
        ctx.drawImage(
          imgRef.current,
          completedCrop.x * scaleX + xOffset,
          completedCrop.y * scaleY + yOffset,
          size,
          size,
          0,
          0,
          size,
          size
        )

        canvas.toBlob(
          blob => {
            if (!blob) {
              console.error('Canvas is empty')
              
return
            }

            onCropComplete(blob)
          },
          'image/jpeg',
          1
        )
      }
    } catch (e) {
      console.error('Error cropping image:', e)
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth='md' fullWidth>
      <DialogContent>
        <ReactCrop
          crop={crop}
          onChange={(_, percentCrop) => setCrop(percentCrop)}
          onComplete={c => setCompletedCrop(c)}
          circularCrop={true}
          aspect={1}
        >
          <img ref={imgRef} alt='Crop me' src={image} style={{ maxWidth: '100%' }} />
        </ReactCrop>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleCropImage} variant='contained' color='primary'>
          Crop Image
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default SimpleCropper
