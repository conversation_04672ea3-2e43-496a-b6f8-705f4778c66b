// MUI Imports
import Chip from '@mui/material/Chip'

import { useTheme } from '@mui/material'

// Third-party Imports
import PerfectScrollbar from 'react-perfect-scrollbar'

// Type Imports
import type { VerticalMenuContextProps } from '@menu/components/vertical-menu/Menu'

// Component Imports
import { Menu, MenuItem, MenuSection, SubMenu } from '@menu/vertical-menu'

// Hook Imports
import useVerticalNav from '@menu/hooks/useVerticalNav'

// Styled Component Imports
import StyledVerticalNavExpandIcon from '@menu/styles/vertical/StyledVerticalNavExpandIcon'

// Style Imports
import menuItemStyles from '@core/styles/vertical/menuItemStyles'
import menuSectionStyles from '@core/styles/vertical/menuSectionStyles'

type RenderExpandIconProps = {
  open?: boolean
  transitionDuration?: VerticalMenuContextProps['transitionDuration']
}

const RenderExpandIcon = ({ open, transitionDuration }: RenderExpandIconProps) => (
  <StyledVerticalNavExpandIcon open={open} transitionDuration={transitionDuration}>
    <i className='ri-arrow-right-s-line' />
  </StyledVerticalNavExpandIcon>
)

const VerticalMenu = ({ scrollMenu }: { scrollMenu: (container: any, isPerfectScrollbar: boolean) => void }) => {
  // Hooks
  // @ts-ignore
  const theme = useTheme()
  const { isBreakpointReached, transitionDuration } = useVerticalNav()

  const ScrollWrapper = isBreakpointReached ? 'div' : PerfectScrollbar

  return (
    // eslint-disable-next-line lines-around-comment
    /* Custom scrollbar instead of browser scroll, remove if you want browser scroll only */
    <ScrollWrapper
      {...(isBreakpointReached
        ? {
            className: 'bs-full overflow-y-auto overflow-x-hidden',
            onScroll: container => scrollMenu(container, false)
          }
        : {
            options: { wheelPropagation: false, suppressScrollX: true },
            onScrollY: container => scrollMenu(container, true)
          })}
    >
      {/* Incase you also want to scroll NavHeader to scroll with Vertical Menu, remove NavHeader from above and paste it below this comment */}
      {/* Vertical Menu */}
      <Menu
        menuItemStyles={menuItemStyles(theme as any)}
        renderExpandIcon={({ open }) => <RenderExpandIcon open={open} transitionDuration={transitionDuration} />}
        renderExpandedMenuItemIcon={{ icon: <i className='ri-circle-line' /> }}
        menuSectionStyles={menuSectionStyles(theme as any)}
      >
        <SubMenu
          label='Dashboards'
          icon={<i className='ri-dashboard-3-line' />}
          // @ts-ignore
          suffix={<Chip label='5' size='small' color='error' />}
        >
          <MenuItem href='/'>Analytics</MenuItem>
        </SubMenu>

        <MenuSection label='Info & Data'>
          <MenuItem icon={<i className='ri-contacts-book-line' />} href='/bio'>
            Bio
          </MenuItem>

          <MenuItem icon={<i className='ri-contacts-book-line' />} href='/contact'>
            Contact
          </MenuItem>

          <MenuItem icon={<i className='ri-school-line' />} href='/education'>
            Education
          </MenuItem>

          <MenuItem icon={<i className='ri-home-office-line' />} href='/company'>
            Company
          </MenuItem>

          <MenuItem icon={<i className='ri-honour-line' />} href='/skills'>
            Skills
          </MenuItem>
          <MenuItem icon={<i className='ri-building-2-line' />} href='/projects'>
            Projects
          </MenuItem>

          <MenuItem icon={<i className='ri-focus-2-line' />} href='/objectives'>
            Objectives
          </MenuItem>

          <MenuItem icon={<i className='ri-creative-commons-by-line' />} href='/interest'>
            Interest
          </MenuItem>
          <MenuItem icon={<i className='ri-tools-line' />} href='/config'>
            Configuration
          </MenuItem>
        </MenuSection>

        <MenuSection label='User Management'>
          <MenuItem icon={<i className='ri-shield-user-line' />} href='/roles'>
            Roles
          </MenuItem>
          <MenuItem icon={<i className='ri-user-settings-line' />} href='/user-roles'>
            User Roles
          </MenuItem>
        </MenuSection>

        <MenuSection label='Content Management'>
          <MenuItem icon={<i className='ri-share-line' />} href='/admin/social-media'>
            Social Media
          </MenuItem>
        </MenuSection>
      </Menu>
    </ScrollWrapper>
  )
}

export default VerticalMenu
