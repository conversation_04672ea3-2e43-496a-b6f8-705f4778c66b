'use client'

import { useEffect, useRef } from 'react'

import dynamic from 'next/dynamic'

import { Box, FormHelperText, Typography } from '@mui/material'

// Dynamically import ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false })

import 'react-quill/dist/quill.snow.css'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  error?: boolean
  helperText?: string
  label?: string
  required?: boolean
  disabled?: boolean
  height?: string | number
}

const RichTextEditor = ({
  value,
  onChange,
  placeholder = 'Enter text...',
  error = false,
  helperText,
  label,
  required = false,
  disabled = false,
  height = 200
}: RichTextEditorProps) => {
  const quillRef = useRef<any>(null)

  // Quill modules configuration
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ align: [] }],
      ['link', 'image'],
      [{ color: [] }, { background: [] }],
      ['blockquote', 'code-block'],
      ['clean']
    ]
  }

  const formats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'list',
    'bullet',
    'indent',
    'align',
    'link',
    'image',
    'color',
    'background',
    'blockquote',
    'code-block'
  ]

  useEffect(() => {
    // Custom styles for the editor
    const style = document.createElement('style')

    style.textContent = `
      .ql-editor {
        min-height: ${typeof height === 'number' ? height + 'px' : height};
        font-family: inherit;
        font-size: 14px;
        line-height: 1.5;
      }
      .ql-toolbar {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;
        border-bottom: none;
      }
      .ql-container {
        border-bottom: 1px solid #ccc;
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;
        border-top: none;
      }
      ${error ? `
        .ql-toolbar,
        .ql-container {
          border-color: #f44336;
        }
      ` : ''}
      ${disabled ? `
        .ql-toolbar {
          pointer-events: none;
          opacity: 0.6;
        }
        .ql-editor {
          background-color: #f5f5f5;
          color: rgba(0, 0, 0, 0.38);
        }
      ` : ''}
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [height, error, disabled])

  return (
    <Box>
      {label && (
        <Typography
          variant="body2"
          component="label"
          sx={{
            display: 'block',
            mb: 1,
            fontWeight: 500,
            color: error ? 'error.main' : 'text.primary'
          }}
        >
          {label}
          {required && (
            <Typography component="span" color="error.main" sx={{ ml: 0.5 }}>
              *
            </Typography>
          )}
        </Typography>
      )}
      
      <Box
        sx={{
          '& .ql-editor': {
            minHeight: typeof height === 'number' ? `${height}px` : height
          }
        }}
      >
        <ReactQuill
          theme="snow"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          modules={modules}
          formats={formats}
          readOnly={disabled}
        />
      </Box>

      {helperText && (
        <FormHelperText error={error} sx={{ mt: 1 }}>
          {helperText}
        </FormHelperText>
      )}
    </Box>
  )
}

export default RichTextEditor
