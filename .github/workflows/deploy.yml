name: Deploy to Production

on:
  push:
    branches: ["dev-v2"]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Install SSH client
        run: |
          sudo apt-get update
          sudo apt-get install -y openssh-client

      - name: Setup SSH keys
        run: |
          mkdir -p ~/.ssh
          chmod 700 ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "${{ secrets.SSH_KNOWN_HOSTS }}" > ~/.ssh/known_hosts
          chmod 644 ~/.ssh/known_hosts

      - name: Test SSH Connection
        run: ssh -o StrictHostKeyChecking=no ubuntu@${{ secrets.EC2_HOST }} "echo 'Connection successful'"

      - name: Deploy to EC2 instance
        run: |
          ssh -o StrictHostKeyChecking=no ubuntu@${{ secrets.EC2_HOST }} << 'EOF'
          set -e
          
          cd ~/portfolio
          
          echo "Checking out the latest code..."
          git fetch origin dev-v2
          git reset --hard origin/dev-v2
          
          echo "Overwriting .env.local from .env.example..."
          cp .env.example .env.local
          
          echo "Stopping any running containers..."
          docker compose down --remove-orphans
          
          echo "Rebuilding and starting app with Docker Compose..."
          docker compose up -d --build
          EOF