#!/bin/bash

# Quick EC2 Setup Script for Instance: i-0b7684f90645bb84f (*************)
# Optimized for t3.large instance in ap-south-1 region

set -e

echo "🚀 Setting up EC2 instance i-0b7684f90645bb84f for Admin Backend deployment..."
echo "Instance: t3.large in ap-south-1 (Mumbai)"
echo "Public IP: *************"
echo "=================================================="

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script with sudo or as root"
    exit 1
fi

# Update system packages
log "📦 Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
log "📦 Installing essential packages..."
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release htop jq

# Install Node.js 20
log "📦 Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt install -y nodejs

# Verify Node.js installation
log "✅ Node.js version: $(node --version)"
log "✅ NPM version: $(npm --version)"

# Install Docker
log "🐳 Installing Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker ubuntu
systemctl start docker
systemctl enable docker

# Install Docker Compose
log "🐳 Installing Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verify Docker installation
log "✅ Docker version: $(docker --version)"
log "✅ Docker Compose version: $(docker-compose --version)"

# Create application directory
log "📁 Creating application directory..."
mkdir -p /opt/admin-backend
chown ubuntu:ubuntu /opt/admin-backend

# Install PM2 for process management
log "📦 Installing PM2..."
npm install -g pm2

# Setup firewall for t3.large instance
log "🔥 Configuring UFW firewall..."
ufw allow ssh
ufw allow 8080/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Create swap file (2GB for t3.large)
log "💾 Creating 2GB swap file for t3.large instance..."
if [ ! -f /swapfile ]; then
    fallocate -l 2G /swapfile
    chmod 600 /swapfile
    mkswap /swapfile
    swapon /swapfile
    echo '/swapfile none swap sw 0 0' >> /etc/fstab
fi

# Install and configure Nginx
log "🌐 Installing and configuring Nginx..."
apt install -y nginx
systemctl start nginx
systemctl enable nginx

# Create Nginx configuration for admin-backend
log "🌐 Creating Nginx configuration..."
cat > /etc/nginx/sites-available/admin-backend << 'EOF'
server {
    listen 80;
    server_name ************* _;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Main application proxy
    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:8080/api/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API endpoints
    location /api {
        proxy_pass http://localhost:8080/api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/admin-backend /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t
systemctl reload nginx

# Setup log rotation
log "📝 Setting up log rotation..."
cat > /etc/logrotate.d/admin-backend << 'EOF'
/opt/admin-backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
}
EOF

# Create logs directory
mkdir -p /opt/admin-backend/logs
chown ubuntu:ubuntu /opt/admin-backend/logs

# Setup monitoring script
log "📊 Creating monitoring script..."
cat > /usr/local/bin/admin-backend-monitor.sh << 'EOF'
#!/bin/bash
# Monitoring script for admin-backend on EC2 i-0b7684f90645bb84f

APP_URL="http://localhost:8080/api/health"
LOG_FILE="/opt/admin-backend/logs/monitor.log"

check_health() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    if curl -f -s "$APP_URL" > /dev/null; then
        echo "[$timestamp] ✅ Application is healthy" >> "$LOG_FILE"
        return 0
    else
        echo "[$timestamp] ❌ Application health check failed" >> "$LOG_FILE"
        return 1
    fi
}

# Run health check
if ! check_health; then
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] 🔄 Attempting to restart application..." >> "$LOG_FILE"
    cd /opt/admin-backend
    /usr/local/bin/docker-compose -f docker-compose.prod.yml restart
fi
EOF

chmod +x /usr/local/bin/admin-backend-monitor.sh

# Setup cron job for monitoring (every 5 minutes)
log "⏰ Setting up monitoring cron job..."
(crontab -u ubuntu -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/admin-backend-monitor.sh") | crontab -u ubuntu -

# Create deployment user
log "👤 Creating deployment user..."
useradd -m -s /bin/bash deploy || true
usermod -aG docker deploy
mkdir -p /home/<USER>/.ssh
chown deploy:deploy /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Set proper permissions
chown -R ubuntu:ubuntu /opt/admin-backend

# Install AWS CLI (useful for this region)
log "☁️ Installing AWS CLI..."
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf aws awscliv2.zip

log "✅ EC2 setup completed successfully!"
log "📋 Instance Information:"
log "   Instance ID: i-0b7684f90645bb84f"
log "   Public IP: *************"
log "   Region: ap-south-1"
log "   Instance Type: t3.large"
log ""
log "📋 Next steps:"
log "   1. Add your GitHub Actions public key to /home/<USER>/.ssh/authorized_keys"
log "   2. Set up your GitHub repository secrets:"
log "      - EC2_HOST: *************"
log "      - EC2_USERNAME: deploy"
log "      - EC2_PRIVATE_KEY: (your private key)"
log "   3. Copy .env.production to /opt/admin-backend/.env"
log "   4. Push to main branch to trigger deployment"
log ""
log "🌐 Your server will be accessible at: http://*************"
log "🏥 Health check will be at: http://*************/api/health"
