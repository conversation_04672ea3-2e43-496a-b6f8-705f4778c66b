import {
  Call<PERSON><PERSON>ler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { IDataServices } from '@app/repository';
import { VisitorLoggingQueueService } from '@app/common/queues';

@Injectable()
export class VisitorLoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(VisitorLoggingInterceptor.name);

  constructor(
    private readonly dataServices: IDataServices,
    private readonly visitorQueue: VisitorLoggingQueueService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    const startTime = Date.now();
    const visitedAt = new Date();

    return next.handle().pipe(
      tap({
        next: async () => {
          try {
            await this.logVisitor(request, response, startTime, visitedAt);
          } catch (error) {
            this.logger.error('Failed to log visitor', error);
          }
        },
        error: async (error) => {
          try {
            await this.logVisitor(
              request,
              response,
              startTime,
              visitedAt,
              error,
            );
          } catch (logError) {
            this.logger.error('Failed to log visitor on error', logError);
          }
        },
      }),
    );
  }

  private async logVisitor(
    request: Request,
    response: Response,
    startTime: number,
    visitedAt: Date,
    error?: any,
  ): Promise<void> {
    try {
      // Check if visitor logging is enabled
      const isLoggingEnabled = await this.isVisitorLoggingEnabled();
      if (!isLoggingEnabled) {
        return;
      }

      // Skip logging for certain endpoints (health checks, static files, etc.)
      if (this.shouldSkipLogging(request.url)) {
        return;
      }

      const responseTime = Date.now() - startTime;
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent') || '';
      const referer = request.get('Referer') || '';
      console.log(' 🚀🚀🚀', referer);

      // Extract user from request if authenticated
      const user = (request as any).user?.id || null;

      // Get location info (you can integrate with IP geolocation service)
      const locationInfo = await this.getLocationFromIp(ipAddress);

      const visitorLogData = {
        endpoint: request.url,
        method: request.method,
        ipAddress,
        userAgent,
        referer,
        user,
        visitedAt,
        responseTime,
        statusCode: error ? 500 : response.statusCode,
        country: locationInfo?.country,
        city: locationInfo?.city,
      };

      // Queue the visitor log for asynchronous processing
      await this.visitorQueue.addVisitorLog(visitorLogData);

      this.logger.debug(
        `Visitor log queued: ${request.method} ${request.url} from ${ipAddress}`,
      );
    } catch (error) {
      this.logger.error('Error in visitor logging:', error);
    }
  }

  private async isVisitorLoggingEnabled(): Promise<boolean> {
    try {
      // Get the current user's config or default config
      const config =
        (await this.dataServices.configs.findOne({
          isDefault: true,
        })) ||
        (await this.dataServices.configs.findOne({
          active: true,
        }));

      return config?.isVisitorLogOn || false;
    } catch (error) {
      this.logger.error('Failed to check visitor logging config:', error);
      return false; // Default to disabled if config check fails
    }
  }

  private shouldSkipLogging(url: string): boolean {
    const skipPatterns = [
      '/health',
      '/metrics',
      '/favicon.ico',
      '/robots.txt',
      '/sitemap.xml',
      '/api/docs',
      '/swagger',
      '/.well-known',
      '/static/',
      '/assets/',
      '/public/',
    ];

    return skipPatterns.some((pattern) => url.includes(pattern));
  }

  private getClientIp(request: Request): string {
    const forwarded = request.get('X-Forwarded-For');
    const realIp = request.get('X-Real-IP');
    const clientIp = request.get('X-Client-IP');

    let ip = '';

    if (forwarded) {
      ip = forwarded.split(',')[0].trim();
    } else {
      ip =
        realIp ||
        clientIp ||
        request.connection.remoteAddress ||
        request.ip ||
        'unknown';
    }

    // Clean IPv6-mapped IPv4 addresses
    if (ip.startsWith('::ffff:')) {
      ip = ip.substring(7); // Remove '::ffff:' prefix
    }

    // Handle IPv6 localhost
    if (ip === '::1') {
      ip = '127.0.0.1';
    }

    return ip;
  }

  private async getLocationFromIp(
    ipAddress: string,
  ): Promise<{ country?: string; city?: string }> {
    try {
      // Skip for local/private IPs
      if (this.isPrivateIp(ipAddress)) {
        return { country: 'Local', city: 'Local' };
      }

      // You can integrate with IP geolocation services like:
      // - ipapi.co
      // - ipgeolocation.io
      // - maxmind
      // For now, returning empty to avoid external dependencies

      // Example implementation with ipapi.co:
      // const response = await fetch(`https://ipapi.co/${ipAddress}/json/`);
      // const data = await response.json();
      // return { country: data.country_name, city: data.city };

      return {};
    } catch (error) {
      this.logger.warn(`Failed to get location for IP ${ipAddress}:`, error);
      return {};
    }
  }

  private isPrivateIp(ip: string): boolean {
    const privateRanges = [
      /^127\./, // *********/8 (localhost)
      /^10\./, // 10.0.0.0/8 (private)
      /^172\.(1[6-9]|2\d|3[01])\./, // **********/12 (Docker default range)
      /^192\.168\./, // ***********/16 (private)
      /^::1$/, // IPv6 localhost
      /^fc00:/, // IPv6 private
      /^::ffff:/, // IPv6-mapped IPv4 (shouldn't happen after cleaning, but just in case)
    ];

    return privateRanges.some((range) => range.test(ip)) || ip === 'unknown';
  }
}
