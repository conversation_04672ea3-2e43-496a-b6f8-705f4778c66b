import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from '@app/db/portfolio-db/schemas/user.schema';

export type BioDocument = Bio & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class Bio extends Document {
  @Prop({ type: String, trim: true })
  title: string;

  @Prop({ type: String, trim: true })
  logo: string;

  @Prop({ type: String, trim: true })
  description: string;

  @Prop({ type: Boolean, default: true })
  active: boolean;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  user: User;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

const schema = SchemaFactory.createForClass(Bio);
export const BioSchema = schema;
