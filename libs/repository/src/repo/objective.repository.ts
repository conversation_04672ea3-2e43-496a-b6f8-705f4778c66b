import mongoose, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Pa<PERSON>ateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IObjectiveRepository } from '@app/repository/abstract';
import { ObjectiveEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { ObjectiveDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class ObjectiveRepository implements IObjectiveRepository {
  constructor(
    private readonly objectiveRepository: PaginateModel<ObjectiveDocument>,
  ) {}

  isConnected(): void {
    if (this.objectiveRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: ObjectiveEntity): Promise<ObjectiveEntity> {
    const insert = await new this.objectiveRepository(
      this.toSchema(item),
    ).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<ObjectiveEntity> {
    return this.toEntity(
      await this.objectiveRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<ObjectiveEntity>): Promise<ObjectiveEntity> {
    return this.toEntity(
      await this.objectiveRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<ObjectiveEntity>,
    queryOptions: Partial<MongoDBQueryOptions<ObjectiveEntity>> = {},
  ): Promise<ObjectiveEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.objectiveRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<ObjectiveEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: ObjectiveEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<ObjectiveDocument> =
      await this.objectiveRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<ObjectiveEntity>,
    item: Partial<ObjectiveEntity>,
  ): Promise<ObjectiveEntity> {
    return this.toEntity(
      await this.objectiveRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<ObjectiveEntity>,
    items: Partial<ObjectiveEntity>[],
  ): Promise<ObjectiveEntity[]> {
    await this.objectiveRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<ObjectiveEntity>): Promise<ObjectiveEntity> {
    const document = await this.objectiveRepository.findOne(
      this.toFilter(filter),
    );
    await this.objectiveRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(
    filter: Partial<ObjectiveEntity>,
  ): Promise<ObjectiveEntity[]> {
    const documents = await this.objectiveRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.objectiveRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<ObjectiveEntity>): Promise<number> {
    return this.objectiveRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<ObjectiveEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<ObjectiveEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: ObjectiveDocument): ObjectiveEntity {
    if (!document) return null;

    const objective = new ObjectiveEntity();
    objective.id = document._id.toString();
    objective.tag = document.tag;
    objective.description = document.description;
    objective.logo = document.logo;
    objective.active = document.active;
    objective.user = mapUserWithoutPassword(document.user);
    objective.createdAt = document.createdAt;
    objective.updatedAt = document.updatedAt;

    return objective;
  }

  private toSchema(objectiveEntity: ObjectiveEntity): ObjectiveDocument {
    return new this.objectiveRepository({
      ...objectiveEntity,
      user:
        objectiveEntity.user &&
        new mongoose.Types.ObjectId(objectiveEntity.user.id),
    });
  }
}
