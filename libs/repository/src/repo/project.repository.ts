import mongoose, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ateR<PERSON>ult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { ProjectEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { ProjectDocument } from '@app/db/portfolio-db/schemas';
import { IProjectRepository } from '@app/repository';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class ProjectRepository implements IProjectRepository {
  constructor(
    private readonly projectRepository: PaginateModel<ProjectDocument>,
  ) {}

  isConnected(): void {
    if (this.projectRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: ProjectEntity): Promise<ProjectEntity> {
    const insert = await new this.projectRepository(this.toSchema(item)).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<ProjectEntity> {
    return this.toEntity(
      await this.projectRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<ProjectEntity>): Promise<ProjectEntity> {
    return this.toEntity(
      await this.projectRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }

  async findAll(
    filter: Partial<ProjectEntity>,
    queryOptions: Partial<MongoDBQueryOptions<ProjectEntity>> = {},
  ): Promise<ProjectEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.projectRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<ProjectEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: ProjectEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<ProjectDocument> =
      await this.projectRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<ProjectEntity>,
    item: Partial<ProjectEntity>,
  ): Promise<ProjectEntity> {
    return this.toEntity(
      await this.projectRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<ProjectEntity>,
    items: Partial<ProjectEntity>[],
  ): Promise<ProjectEntity[]> {
    await this.projectRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<ProjectEntity>): Promise<ProjectEntity> {
    const document = await this.projectRepository.findOne(
      this.toFilter(filter),
    );
    await this.projectRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }

  async deleteMany(filter: Partial<ProjectEntity>): Promise<ProjectEntity[]> {
    const documents = await this.projectRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.projectRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<ProjectEntity>): Promise<number> {
    return this.projectRepository.countDocuments(this.toFilter(filter));
  }

  private toFilter(document: Partial<ProjectEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<ProjectEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'skill',
        options: { lean: true },
      },
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: ProjectDocument): ProjectEntity {
    if (!document) return null;

    const project = new ProjectEntity();
    project.id = document._id.toString();
    project.name = document.name;
    project.coverImages = document.coverImages;
    project.projectDescription = document.projectDescription;
    project.startedAt = document.startedAt;
    project.endedAt = document.endedAt;
    project.githubLink = document.githubLink;
    project.projectLiveLink = document.projectLiveLink;
    project.skill = document.skill;
    project.companyWebsite = document.companyWebsite;
    project.details = document.details;
    project.active = document.active;
    project.user = mapUserWithoutPassword(document.user);
    project.createdAt = document.createdAt;
    project.updatedAt = document.updatedAt;

    return project;
  }

  private toSchema(projectEntity: ProjectEntity): ProjectDocument {
    // Ensure skill is always an array before processing
    let skillArray = [];

    if (projectEntity.skill) {
      // If skill is a single item (not an array), convert it to an array
      if (!Array.isArray(projectEntity.skill)) {
        skillArray = [projectEntity.skill];
      } else {
        skillArray = projectEntity.skill;
      }

      // Map each skill item to the appropriate format
      skillArray = skillArray.map((item) => {
        if (typeof item === 'string') {
          return new mongoose.Types.ObjectId(item);
        } else if (item && item.id) {
          return new mongoose.Types.ObjectId(item.id);
        }
        return item;
      });
    }

    return new this.projectRepository({
      ...projectEntity,
      skill: skillArray,
      user:
        projectEntity.user &&
        new mongoose.Types.ObjectId(projectEntity.user.id),
    });
  }
}
