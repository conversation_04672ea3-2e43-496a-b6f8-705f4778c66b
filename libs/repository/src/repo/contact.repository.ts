import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, <PERSON><PERSON>ateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IContactRepository } from '@app/repository/abstract';
import { ContactEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { ContactDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class ContactRepository implements IContactRepository {
  constructor(
    private readonly contactRepository: PaginateModel<ContactDocument>,
  ) {}

  isConnected(): void {
    if (this.contactRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: ContactEntity): Promise<ContactEntity> {
    const insert = await new this.contactRepository(this.toSchema(item)).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<ContactEntity> {
    return this.toEntity(
      await this.contactRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<ContactEntity>): Promise<ContactEntity> {
    return this.toEntity(
      await this.contactRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<ContactEntity>,
    queryOptions: Partial<MongoDBQueryOptions<ContactEntity>> = {},
  ): Promise<ContactEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.contactRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async update(
    filter: Partial<ContactEntity>,
    item: Partial<ContactEntity>,
  ): Promise<ContactEntity> {
    return this.toEntity(
      await this.contactRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<ContactEntity>,
    items: Partial<ContactEntity>[],
  ): Promise<ContactEntity[]> {
    await this.contactRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<ContactEntity>): Promise<ContactEntity> {
    const document = await this.contactRepository.findOne(
      this.toFilter(filter),
    );
    await this.contactRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(filter: Partial<ContactEntity>): Promise<ContactEntity[]> {
    const documents = await this.contactRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.contactRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<ContactEntity>): Promise<number> {
    return this.contactRepository.countDocuments(this.toFilter(filter));
  }

  async findAllWithPagination(
    filter: Partial<ContactEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: ContactEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<ContactDocument> =
      await this.contactRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    const entities = result.docs.map((item) => this.toEntity(item));
    console.log('Converted to entities:', entities.length);

    return {
      data: entities,
      total: result.totalDocs,
      page: result.page || page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }
  private toFilter(document: Partial<ContactEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && {
        user:
          typeof user === 'string'
            ? new mongoose.Types.ObjectId(user)
            : new mongoose.Types.ObjectId(user.id),
      }),
    };
  }

  private toUpdate(item: Partial<ContactEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: ContactDocument): ContactEntity {
    if (!document) return null;

    console.log('Converting document to entity:', document._id);
    console.log('Document user field:', document.user);

    const contact = new ContactEntity();
    contact.id = document._id.toString();
    contact.name = document.name;
    contact.description = document.description;
    contact.link = document.link;
    contact.logo = document.logo;
    contact.type = document.type;
    contact.active = document.active;
    contact.user = mapUserWithoutPassword(document.user);
    contact.createdAt = document.createdAt;
    contact.updatedAt = document.updatedAt;

    console.log('Converted contact user:', contact.user);
    return contact;
  }

  private toSchema(contactEntity: ContactEntity): ContactDocument {
    return new this.contactRepository({
      ...contactEntity,
      user:
        contactEntity.user &&
        new mongoose.Types.ObjectId(contactEntity.user.id),
    });
  }
}
