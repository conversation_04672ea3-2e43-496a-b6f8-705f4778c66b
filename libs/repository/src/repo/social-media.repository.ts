import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, Pa<PERSON>ateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { SocialMediaEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { SocialMediaDocument } from '@app/db/portfolio-db/schemas';
import { ISocialMediaRepository } from '@app/repository';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class SocialMediaRepository implements ISocialMediaRepository {
  constructor(
    private readonly socialMediaRepository: PaginateModel<SocialMediaDocument>,
  ) {}

  isConnected(): void {
    if (this.socialMediaRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: SocialMediaEntity): Promise<SocialMediaEntity> {
    const insert = await new this.socialMediaRepository(
      this.toSchema(item),
    ).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<SocialMediaEntity> {
    return this.toEntity(
      await this.socialMediaRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(
    filter: Partial<SocialMediaEntity>,
  ): Promise<SocialMediaEntity> {
    return this.toEntity(
      await this.socialMediaRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<SocialMediaEntity>,
    queryOptions: Partial<MongoDBQueryOptions<SocialMediaEntity>> = {},
  ): Promise<SocialMediaEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.socialMediaRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<SocialMediaEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: SocialMediaEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<SocialMediaDocument> =
      await this.socialMediaRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<SocialMediaEntity>,
    item: Partial<SocialMediaEntity>,
  ): Promise<SocialMediaEntity> {
    return this.toEntity(
      await this.socialMediaRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<SocialMediaEntity>,
    items: Partial<SocialMediaEntity>[],
  ): Promise<SocialMediaEntity[]> {
    await this.socialMediaRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(
    filter: Partial<SocialMediaEntity>,
  ): Promise<SocialMediaEntity> {
    const document = await this.socialMediaRepository.findOne(
      this.toFilter(filter),
    );
    await this.socialMediaRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(
    filter: Partial<SocialMediaEntity>,
  ): Promise<SocialMediaEntity[]> {
    const documents = await this.socialMediaRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.socialMediaRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<SocialMediaEntity>): Promise<number> {
    return this.socialMediaRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<SocialMediaEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<SocialMediaEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: SocialMediaDocument): SocialMediaEntity {
    if (!document) return null;

    const socialMedia = new SocialMediaEntity();
    socialMedia.id = document._id.toString();
    socialMedia.name = document.name;
    socialMedia.description = document.description;
    socialMedia.logo = document.logo;
    socialMedia.link = document.link;
    socialMedia.active = document.active;
    socialMedia.user = mapUserWithoutPassword(document.user);
    socialMedia.createdAt = document.createdAt;
    socialMedia.updatedAt = document.updatedAt;

    return socialMedia;
  }

  private toSchema(socialMediaEntity: SocialMediaEntity): SocialMediaDocument {
    return new this.socialMediaRepository({
      ...socialMediaEntity,
      user:
        socialMediaEntity.user &&
        new mongoose.Types.ObjectId(socialMediaEntity.user.id),
    });
  }
}
