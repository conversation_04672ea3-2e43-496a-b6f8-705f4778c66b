import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, Pa<PERSON>ateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IEducationRepository } from '@app/repository/abstract';
import { EducationEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { EducationDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class EducationRepository implements IEducationRepository {
  constructor(
    private readonly educationRepository: PaginateModel<EducationDocument>,
  ) {}

  isConnected(): void {
    if (this.educationRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: EducationEntity): Promise<EducationEntity> {
    const insert = await new this.educationRepository(
      this.toSchema(item),
    ).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<EducationEntity> {
    return this.toEntity(
      await this.educationRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<EducationEntity>): Promise<EducationEntity> {
    return this.toEntity(
      await this.educationRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<EducationEntity>,
    queryOptions: Partial<MongoDBQueryOptions<EducationEntity>> = {},
  ): Promise<EducationEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.educationRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<EducationEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: EducationEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<EducationDocument> =
      await this.educationRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<EducationEntity>,
    item: Partial<EducationEntity>,
  ): Promise<EducationEntity> {
    return this.toEntity(
      await this.educationRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<EducationEntity>,
    items: Partial<EducationEntity>[],
  ): Promise<EducationEntity[]> {
    await this.educationRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<EducationEntity>): Promise<EducationEntity> {
    const document = await this.educationRepository.findOne(
      this.toFilter(filter),
    );
    await this.educationRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(
    filter: Partial<EducationEntity>,
  ): Promise<EducationEntity[]> {
    const documents = await this.educationRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.educationRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<EducationEntity>): Promise<number> {
    return this.educationRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<EducationEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<EducationEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: EducationDocument): EducationEntity {
    if (!document) return null;

    const education = new EducationEntity();
    education.id = document._id.toString();
    education.name = document.name;
    education.degree = document.degree;
    education.link = document.link;
    education.startedAt = document.startedAt;
    education.endedAt = document.endedAt;
    education.location = document.location;
    education.logo = document.logo;
    education.active = document.active;
    education.user = mapUserWithoutPassword(document.user);
    education.createdAt = document.createdAt;
    education.updatedAt = document.updatedAt;

    return education;
  }

  private toSchema(educationEntity: EducationEntity): EducationDocument {
    return new this.educationRepository({
      ...educationEntity,
      user:
        educationEntity.user &&
        new mongoose.Types.ObjectId(educationEntity.user.id),
    });
  }
}
