import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, Pa<PERSON>ateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import {
  BioEntity,
  IBioRepository,
  mapUserWithoutPassword,
} from '@app/repository';
import { BioDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class BioRepository implements IBioRepository {
  constructor(private readonly bioRepository: PaginateModel<BioDocument>) {}

  isConnected(): void {
    if (this.bioRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: BioEntity): Promise<BioEntity> {
    const insert = await new this.bioRepository(this.toSchema(item)).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<BioEntity> {
    return this.toEntity(
      await this.bioRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<BioEntity>): Promise<BioEntity> {
    return this.toEntity(
      await this.bioRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<BioEntity>,
    queryOptions: Partial<MongoDBQueryOptions<BioEntity>> = {},
  ): Promise<BioEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.bioRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<BioEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: BioEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<BioDocument> =
      await this.bioRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<BioEntity>,
    item: Partial<BioEntity>,
  ): Promise<BioEntity> {
    return this.toEntity(
      await this.bioRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<BioEntity>,
    items: Partial<BioEntity>[],
  ): Promise<BioEntity[]> {
    await this.bioRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<BioEntity>): Promise<BioEntity> {
    const document = await this.bioRepository.findOne(this.toFilter(filter));
    await this.bioRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(filter: Partial<BioEntity>): Promise<BioEntity[]> {
    const documents = await this.bioRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.bioRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<BioEntity>): Promise<number> {
    return this.bioRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<BioEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<BioEntity>) {
    const { user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: BioDocument): BioEntity {
    if (!document) return null;

    const bio = new BioEntity();
    bio.id = document._id.toString();
    bio.title = document.title;
    bio.logo = document.logo;
    bio.description = document.description;
    bio.active = document.active;
    bio.user = mapUserWithoutPassword(document.user);
    bio.createdAt = document.createdAt;
    bio.updatedAt = document.updatedAt;

    return bio;
  }

  private toSchema(bioEntity: BioEntity): BioDocument {
    return new this.bioRepository({
      ...bioEntity,
      user: bioEntity.user && new mongoose.Types.ObjectId(bioEntity.user.id),
    });
  }
}
