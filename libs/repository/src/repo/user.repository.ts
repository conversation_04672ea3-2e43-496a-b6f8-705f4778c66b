import mongoose, { PaginateModel } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IUserRepository } from '@app/repository/abstract';
import { UserEntity } from '@app/repository/entities';
import { UserDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class UserRepository implements IUserRepository {
  constructor(private readonly userRepository: PaginateModel<UserDocument>) {}

  isConnected(): void {
    if (this.userRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: UserEntity): Promise<UserEntity> {
    const insert = await new this.userRepository(this.toSchema(item)).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<UserEntity> {
    return this.toEntity(
      await this.userRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<UserEntity>): Promise<UserEntity> {
    return this.toEntity(
      await this.userRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }

  async findAll(
    filter: Partial<UserEntity>,
    queryOptions: Partial<MongoDBQueryOptions<UserEntity>> = {},
  ): Promise<UserEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.userRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findByEmail(email: string): Promise<UserEntity> {
    return this.toEntity(
      await this.userRepository.findOne({ email: email }).select('+password'),
    );
  }
  async update(
    filter: Partial<UserEntity>,
    item: Partial<UserEntity>,
  ): Promise<UserEntity> {
    return this.toEntity(
      await this.userRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<UserEntity>,
    items: Partial<UserEntity>[],
  ): Promise<UserEntity[]> {
    await this.userRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<UserEntity>): Promise<UserEntity> {
    const document = await this.userRepository.findOne(this.toFilter(filter));
    await this.userRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(filter: Partial<UserEntity>): Promise<UserEntity[]> {
    const documents = await this.userRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.userRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<UserEntity>): Promise<number> {
    return this.userRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<UserEntity>) {
    const { id, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
    };
  }

  private toUpdate(item: Partial<UserEntity>) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id, ...rest } = item;
    return {
      ...rest,
    };
  }

  private toPopulate() {
    return [];
  }

  private toEntity(document: UserDocument): UserEntity {
    if (!document) return null;

    const user = new UserEntity();
    user.id = document._id.toString();
    user.name = document.name;
    user.email = document.email;
    user.status = document.status;
    user.dob = document.dob;
    user.phone = document.phone;
    user.coverImage = document.coverImage;
    user.profileImage = document.profileImage;
    user.createdAt = document.createdAt;
    user.updatedAt = document.updatedAt;
    // Only include password if it exists in document (for authentication purposes)
    // This will be excluded in API responses
    if (document.password) {
      user.password = document.password;
    }

    return user;
  }

  private toSchema(userEntity: UserEntity): UserDocument {
    return new this.userRepository({
      ...userEntity,
    });
  }
}
