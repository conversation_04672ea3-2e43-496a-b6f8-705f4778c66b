import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, Pa<PERSON>ateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { ISkillRepository } from '@app/repository/abstract';
import { SkillEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { SkillDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class SkillRepository implements ISkillRepository {
  constructor(private readonly skillRepository: PaginateModel<SkillDocument>) {}

  isConnected(): void {
    if (this.skillRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: SkillEntity): Promise<SkillEntity> {
    const insert = await new this.skillRepository(this.toSchema(item)).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<SkillEntity> {
    return this.toEntity(
      await this.skillRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<SkillEntity>): Promise<SkillEntity> {
    return this.toEntity(
      await this.skillRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<SkillEntity>,
    queryOptions: Partial<MongoDBQueryOptions<SkillEntity>> = {},
  ): Promise<SkillEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.skillRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<SkillEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: SkillEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<SkillDocument> =
      await this.skillRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<SkillEntity>,
    item: Partial<SkillEntity>,
  ): Promise<SkillEntity> {
    return this.toEntity(
      await this.skillRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<SkillEntity>,
    items: Partial<SkillEntity>[],
  ): Promise<SkillEntity[]> {
    await this.skillRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<SkillEntity>): Promise<SkillEntity> {
    const document = await this.skillRepository.findOne(this.toFilter(filter));
    await this.skillRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(filter: Partial<SkillEntity>): Promise<SkillEntity[]> {
    const documents = await this.skillRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.skillRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<SkillEntity>): Promise<number> {
    return this.skillRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<SkillEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<SkillEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: SkillDocument): SkillEntity {
    if (!document) return null;

    const skill = new SkillEntity();
    skill.id = document._id.toString();
    skill.name = document.name;
    skill.logo = document.logo;
    skill.description = document.description;
    skill.achievedPercentage = document.achievedPercentage;
    skill.tags = document.tags;
    skill.level = document.level;
    skill.active = document.active;
    skill.user = mapUserWithoutPassword(document.user);
    skill.createdAt = document.createdAt;
    skill.updatedAt = document.updatedAt;

    return skill;
  }

  private toSchema(skillEntity: SkillEntity): SkillDocument {
    return new this.skillRepository({
      ...skillEntity,
      user:
        skillEntity.user && new mongoose.Types.ObjectId(skillEntity.user.id),
    });
  }
}
