import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, PaginateResult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { CompanyDocument } from '@app/db/portfolio-db/schemas';
import {
  CompanyEntity,
  ICompanyRepository,
  mapUserWithoutPassword,
} from '@app/repository';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class CompanyRepository implements ICompanyRepository {
  constructor(
    private readonly companyRepository: PaginateModel<CompanyDocument>,
  ) {}

  isConnected(): void {
    if (this.companyRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: CompanyEntity): Promise<CompanyEntity> {
    const insert = await new this.companyRepository(this.toSchema(item)).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<CompanyEntity> {
    return this.toEntity(
      await this.companyRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<CompanyEntity>): Promise<CompanyEntity> {
    return this.toEntity(
      await this.companyRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }

  async findAll(
    filter: Partial<CompanyEntity>,
    queryOptions: Partial<MongoDBQueryOptions<CompanyEntity>> = {},
  ): Promise<CompanyEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.companyRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<CompanyEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: CompanyEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log(
      'Repository findAllWithPagination called with filter:',
      filter,
      'page:',
      page,
      'limit:',
      limit,
    );
    const mongoFilter = this.toFilter(filter);
    console.log('Converted to mongo filter:', mongoFilter);

    const result: PaginateResult<CompanyDocument> =
      await this.companyRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    console.log('Pagination result:', {
      totalDocs: result.totalDocs,
      page: result.page,
      totalPages: result.totalPages,
      limit: result.limit,
    });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<CompanyEntity>,
    item: Partial<CompanyEntity>,
  ): Promise<CompanyEntity> {
    return this.toEntity(
      await this.companyRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<CompanyEntity>,
    items: Partial<CompanyEntity>[],
  ): Promise<CompanyEntity[]> {
    await this.companyRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<CompanyEntity>): Promise<CompanyEntity> {
    const document = await this.companyRepository.findOne(
      this.toFilter(filter),
    );
    await this.companyRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }

  async deleteMany(filter: Partial<CompanyEntity>): Promise<CompanyEntity[]> {
    const documents = await this.companyRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.companyRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<CompanyEntity>): Promise<number> {
    return this.companyRepository.countDocuments(this.toFilter(filter));
  }

  private toFilter(document: Partial<CompanyEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<CompanyEntity>) {
    const { user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: CompanyDocument): CompanyEntity {
    if (!document) return null;

    const company = new CompanyEntity();
    company.id = document._id.toString();
    company.name = document.name;
    company.companyDescription = document.companyDescription;
    company.companyWebsite = document.companyWebsite;
    company.designation = document.designation;
    company.salary = document.salary;
    company.currency = document.currency;
    company.startedAt = document.startedAt;
    company.endedAt = document.endedAt;
    company.socialMedia = document.socialMedia;
    company.workDescription = document.workDescription;
    company.isCurrent = document.isCurrent;
    company.isPromoted = document.isPromoted;
    company.isActive = document.isActive;
    company.logo = document.logo;
    company.workArea = document.workArea;
    company.country = document.country;
    company.address = document.address;
    company.active = document.active;
    company.phone = document.phone;
    company.user = mapUserWithoutPassword(document.user);
    company.createdAt = document.createdAt;
    company.updatedAt = document.updatedAt;

    return company;
  }

  private toSchema(companyEntity: CompanyEntity): CompanyDocument {
    return new this.companyRepository({
      ...companyEntity,
      user:
        companyEntity.user &&
        new mongoose.Types.ObjectId(companyEntity.user.id),
    });
  }
}
