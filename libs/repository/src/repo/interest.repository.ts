import mongoose, { <PERSON><PERSON><PERSON><PERSON>ode<PERSON>, <PERSON><PERSON>ateR<PERSON>ult } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IInterestRepository } from '@app/repository/abstract';
import { InterestEntity } from '@app/repository/entities';
import { mapUserWithoutPassword } from '@app/repository/utils';
import { InterestDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class InterestRepository implements IInterestRepository {
  constructor(
    private readonly interestRepository: PaginateModel<InterestDocument>,
  ) {}

  isConnected(): void {
    if (this.interestRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: InterestEntity): Promise<InterestEntity> {
    const insert = await new this.interestRepository(
      this.toSchema(item),
    ).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<InterestEntity> {
    return this.toEntity(
      await this.interestRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<InterestEntity>): Promise<InterestEntity> {
    return this.toEntity(
      await this.interestRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }
  async findAll(
    filter: Partial<InterestEntity>,
    queryOptions: Partial<MongoDBQueryOptions<InterestEntity>> = {},
  ): Promise<InterestEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.interestRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findAllWithPagination(
    filter: Partial<InterestEntity>,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: InterestEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const mongoFilter = this.toFilter(filter);
    const result: PaginateResult<InterestDocument> =
      await this.interestRepository.paginate(mongoFilter, {
        page,
        limit,
        populate: this.toPopulate(),
        lean: false,
      });

    return {
      data: result.docs.map((doc) => this.toEntity(doc)),
      total: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
    };
  }

  async update(
    filter: Partial<InterestEntity>,
    item: Partial<InterestEntity>,
  ): Promise<InterestEntity> {
    return this.toEntity(
      await this.interestRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<InterestEntity>,
    items: Partial<InterestEntity>[],
  ): Promise<InterestEntity[]> {
    await this.interestRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<InterestEntity>): Promise<InterestEntity> {
    const document = await this.interestRepository.findOne(
      this.toFilter(filter),
    );
    await this.interestRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }
  async deleteMany(filter: Partial<InterestEntity>): Promise<InterestEntity[]> {
    const documents = await this.interestRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.interestRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<InterestEntity>): Promise<number> {
    return this.interestRepository.countDocuments(this.toFilter(filter));
  }
  private toFilter(document: Partial<InterestEntity>) {
    const { id, user, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toUpdate(item: Partial<InterestEntity>) {
    const { id, user, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user.id) }),
    };
  }

  private toPopulate() {
    return [
      {
        path: 'user',
        options: { lean: true },
      },
    ];
  }

  private toEntity(document: InterestDocument): InterestEntity {
    if (!document) return null;

    const interest = new InterestEntity();
    interest.id = document._id.toString();
    interest.logo = document.logo;
    interest.title = document.title;
    interest.description = document.description;
    interest.active = document.active;
    interest.user = mapUserWithoutPassword(document.user);
    interest.createdAt = document.createdAt;
    interest.updatedAt = document.updatedAt;

    return interest;
  }

  private toSchema(interestEntity: InterestEntity): InterestDocument {
    return new this.interestRepository({
      ...interestEntity,
      user:
        interestEntity.user &&
        new mongoose.Types.ObjectId(interestEntity.user.id),
    });
  }
}
