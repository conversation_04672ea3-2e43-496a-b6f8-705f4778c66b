import mongoose, { PaginateModel } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IUserRoleRepository } from '@app/repository/abstract';
import { UserRoleEntity } from '@app/repository/entities';
import { UserRoleDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class UserRoleRepository implements IUserRoleRepository {
  constructor(
    private readonly userRoleRepository: PaginateModel<UserRoleDocument>,
  ) {}

  isConnected(): void {
    if (this.userRoleRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: UserRoleEntity): Promise<UserRoleEntity> {
    // Create a complete user role entity with defaults for missing fields
    const completeUserRole: UserRoleEntity = {
      user: item.user,
      role: item.role,
      additionalPermissions: item.additionalPermissions || [],
      deniedPermissions: item.deniedPermissions || [],
      isActive: item.isActive !== undefined ? item.isActive : true,
      isDefault: item.isDefault || false,
      expiresAt: item.expiresAt,
      assignedAt: item.assignedAt || new Date(),
      assignedBy: item.assignedBy,
      updatedBy: item.updatedBy,
      notes: item.notes,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const insert = await new this.userRoleRepository(
      this.toSchema(completeUserRole),
    ).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<UserRoleEntity> {
    return this.toEntity(
      await this.userRoleRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<UserRoleEntity>): Promise<UserRoleEntity> {
    return this.toEntity(
      await this.userRoleRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }

  async findAll(
    filter: Partial<UserRoleEntity>,
    queryOptions: Partial<MongoDBQueryOptions<UserRoleEntity>> = {},
  ): Promise<UserRoleEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.userRoleRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findByUser(
    userId: string,
    includeExpired: boolean = false,
  ): Promise<UserRoleEntity[]> {
    const filter: any = {
      user: new mongoose.Types.ObjectId(userId),
      isActive: true,
    };

    if (!includeExpired) {
      filter.$or = [
        { expiresAt: { $exists: false } },
        { expiresAt: null },
        { expiresAt: { $gt: new Date() } },
      ];
    }

    return (
      await this.userRoleRepository
        .find(filter)
        .populate(this.toPopulate())
        .sort({ isDefault: -1, createdAt: -1 })
    ).map((item) => this.toEntity(item));
  }

  async findByRole(
    roleId: string,
    includeExpired: boolean = false,
  ): Promise<UserRoleEntity[]> {
    const filter: any = {
      role: new mongoose.Types.ObjectId(roleId),
      isActive: true,
    };

    if (!includeExpired) {
      filter.$or = [
        { expiresAt: { $exists: false } },
        { expiresAt: null },
        { expiresAt: { $gt: new Date() } },
      ];
    }

    return (
      await this.userRoleRepository
        .find(filter)
        .populate(this.toPopulate())
        .sort({ createdAt: -1 })
    ).map((item) => this.toEntity(item));
  }

  async findUserRole(userId: string, roleId: string): Promise<UserRoleEntity> {
    return this.toEntity(
      await this.userRoleRepository
        .findOne({
          user: new mongoose.Types.ObjectId(userId),
          role: new mongoose.Types.ObjectId(roleId),
        })
        .populate(this.toPopulate()),
    );
  }

  async findDefaultUserRole(userId: string): Promise<UserRoleEntity> {
    return this.toEntity(
      await this.userRoleRepository
        .findOne({
          user: new mongoose.Types.ObjectId(userId),
          isDefault: true,
          isActive: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async getUsersWithRole(
    roleId: string,
    includeExpired: boolean = false,
  ): Promise<UserRoleEntity[]> {
    return this.findByRole(roleId, includeExpired);
  }

  async findExpiredUserRoles(): Promise<UserRoleEntity[]> {
    return (
      await this.userRoleRepository
        .find({
          expiresAt: { $exists: true, $ne: null, $lt: new Date() },
          isActive: true,
        })
        .populate(this.toPopulate())
        .sort({ expiresAt: -1 })
    ).map((item) => this.toEntity(item));
  }

  async findUserRolesWithPermission(
    permission: string,
  ): Promise<UserRoleEntity[]> {
    return (
      await this.userRoleRepository
        .find({
          additionalPermissions: permission,
          isActive: true,
          $or: [
            { expiresAt: { $exists: false } },
            { expiresAt: null },
            { expiresAt: { $gt: new Date() } },
          ],
        })
        .populate(this.toPopulate())
        .sort({ createdAt: -1 })
    ).map((item) => this.toEntity(item));
  }

  async findActiveUserRolesByUser(userId: string): Promise<UserRoleEntity[]> {
    return this.findByUser(userId, false);
  }

  async bulkAssignRoles(
    assignments: Partial<UserRoleEntity>[],
  ): Promise<UserRoleEntity[]> {
    const results: UserRoleEntity[] = [];

    for (const assignment of assignments) {
      const completeUserRole: UserRoleEntity = {
        user: assignment.user!,
        role: assignment.role!,
        additionalPermissions: assignment.additionalPermissions || [],
        deniedPermissions: assignment.deniedPermissions || [],
        isActive:
          assignment.isActive !== undefined ? assignment.isActive : true,
        isDefault: assignment.isDefault || false,
        expiresAt: assignment.expiresAt,
        assignedAt: assignment.assignedAt || new Date(),
        assignedBy: assignment.assignedBy,
        updatedBy: assignment.updatedBy,
        notes: assignment.notes,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const insert = await new this.userRoleRepository(
        this.toSchema(completeUserRole),
      ).save();
      const populated = await insert.populate(this.toPopulate());
      results.push(this.toEntity(populated.toObject()));
    }

    return results;
  }

  async update(
    filter: Partial<UserRoleEntity>,
    item: Partial<UserRoleEntity>,
  ): Promise<UserRoleEntity> {
    return this.toEntity(
      await this.userRoleRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<UserRoleEntity>,
    items: Partial<UserRoleEntity>[],
  ): Promise<UserRoleEntity[]> {
    await this.userRoleRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<UserRoleEntity>): Promise<UserRoleEntity> {
    const document = await this.userRoleRepository.findOne(
      this.toFilter(filter),
    );
    await this.userRoleRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }

  async deleteMany(filter: Partial<UserRoleEntity>): Promise<UserRoleEntity[]> {
    const documents = await this.userRoleRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.userRoleRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<UserRoleEntity>): Promise<number> {
    return this.userRoleRepository.countDocuments(this.toFilter(filter));
  }

  private toFilter(document: Partial<UserRoleEntity>) {
    const { id, user, role, assignedBy, updatedBy, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(user && { user: new mongoose.Types.ObjectId(user) }),
      ...(role && { role: new mongoose.Types.ObjectId(role) }),
      ...(assignedBy && {
        assignedBy: new mongoose.Types.ObjectId(assignedBy),
      }),
      ...(updatedBy && { updatedBy: new mongoose.Types.ObjectId(updatedBy) }),
    };
  }

  private toUpdate(item: Partial<UserRoleEntity>) {
    const { id, user, role, assignedBy, updatedBy, ...rest } = item;
    return {
      ...rest,
      ...(user && { user: new mongoose.Types.ObjectId(user) }),
      ...(role && { role: new mongoose.Types.ObjectId(role) }),
      ...(assignedBy && {
        assignedBy: new mongoose.Types.ObjectId(assignedBy),
      }),
      ...(updatedBy && { updatedBy: new mongoose.Types.ObjectId(updatedBy) }),
    };
  }

  private toPopulate() {
    return [
      { path: 'user', select: 'name email profileImage' },
      { path: 'role', select: 'name description level permissions priority' },
      { path: 'assignedBy', select: 'name email profileImage' },
      { path: 'updatedBy', select: 'name email profileImage' },
    ];
  }

  private toEntity(document: UserRoleDocument): UserRoleEntity {
    if (!document) return null;

    const userRole = new UserRoleEntity();
    userRole.id = document._id.toString();

    // Handle user field - preserve populated data if available
    if (document.user) {
      if (typeof document.user === 'object' && document.user._id) {
        // User is populated
        userRole.user = document.user._id.toString();
        (userRole as any).userDetails = {
          id: document.user._id.toString(),
          name: document.user.name,
          email: document.user.email,
          profileImage: document.user.profileImage,
        };
      } else {
        // User is just an ID
        userRole.user = document.user.toString();
      }
    }

    // Handle role field - preserve populated data if available
    if (document.role) {
      if (typeof document.role === 'object' && document.role._id) {
        // Role is populated
        userRole.role = document.role._id.toString();
        (userRole as any).roleDetails = {
          id: document.role._id.toString(),
          name: document.role.name,
          level: document.role.level,
          permissions: document.role.permissions || [],
        };
      } else {
        // Role is just an ID
        userRole.role = document.role.toString();
      }
    }

    userRole.additionalPermissions = document.additionalPermissions || [];
    userRole.deniedPermissions = document.deniedPermissions || [];
    userRole.isActive = document.isActive;
    userRole.isDefault = document.isDefault;
    userRole.expiresAt = document.expiresAt;
    userRole.assignedAt = document.assignedAt;
    userRole.assignedBy = document.assignedBy?.toString();
    userRole.updatedBy = document.updatedBy?.toString();
    userRole.notes = document.notes;
    userRole.createdAt = document.createdAt;
    userRole.updatedAt = document.updatedAt;

    return userRole;
  }

  private toSchema(userRoleEntity: UserRoleEntity): UserRoleDocument {
    return new this.userRoleRepository({
      ...userRoleEntity,
      user: userRoleEntity.user
        ? new mongoose.Types.ObjectId(userRoleEntity.user)
        : undefined,
      role: userRoleEntity.role
        ? new mongoose.Types.ObjectId(userRoleEntity.role)
        : undefined,
      assignedBy: userRoleEntity.assignedBy
        ? new mongoose.Types.ObjectId(userRoleEntity.assignedBy)
        : undefined,
      updatedBy: userRoleEntity.updatedBy
        ? new mongoose.Types.ObjectId(userRoleEntity.updatedBy)
        : undefined,
    });
  }
}
