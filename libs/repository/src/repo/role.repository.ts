import mongoose, { PaginateModel } from 'mongoose';
import { InternalServerErrorException } from '@nestjs/common';
import { IRoleRepository } from '@app/repository/abstract';
import { RoleEntity } from '@app/repository/entities';
import { RoleDocument } from '@app/db/portfolio-db/schemas';
import { MongoDBQueryOptions } from '@app/repository/interface';

export class RoleRepository implements IRoleRepository {
  constructor(private readonly roleRepository: PaginateModel<RoleDocument>) {}

  isConnected(): void {
    if (this.roleRepository.db.readyState !== 1) {
      throw new InternalServerErrorException('Database connection failed!');
    }
  }

  async insert(item: RoleEntity): Promise<RoleEntity> {
    // Create a complete role entity with defaults for missing fields
    const completeRole: RoleEntity = {
      name: item.name,
      description: item.description || '',
      permissions: item.permissions || [],
      level: item.level || 'user',
      priority: item.priority || 0,
      isActive: item.isActive !== undefined ? item.isActive : true,
      isDefault: item.isDefault || false,
      createdBy: item.createdBy,
      updatedBy: item.updatedBy,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const insert = await new this.roleRepository(
      this.toSchema(completeRole),
    ).save();
    return this.toEntity((await insert.populate(this.toPopulate())).toObject());
  }

  async findById(id: string): Promise<RoleEntity> {
    return this.toEntity(
      await this.roleRepository.findById(id).populate(this.toPopulate()),
    );
  }

  async findOne(filter: Partial<RoleEntity>): Promise<RoleEntity> {
    return this.toEntity(
      await this.roleRepository
        .findOne(this.toFilter(filter))
        .populate(this.toPopulate()),
    );
  }

  async findAll(
    filter: Partial<RoleEntity>,
    queryOptions: Partial<MongoDBQueryOptions<RoleEntity>> = {},
  ): Promise<RoleEntity[]> {
    const { projection = null, options = {} } = queryOptions;

    return (
      await this.roleRepository
        .find(this.toFilter(filter), projection, { ...options })
        .populate(this.toPopulate())
        .lean()
    ).map((item) => this.toEntity(item));
  }

  async findByLevel(level: string): Promise<RoleEntity[]> {
    return (
      await this.roleRepository
        .find({ level, isActive: true })
        .populate(this.toPopulate())
        .sort({ priority: -1 })
    ).map((item) => this.toEntity(item));
  }

  async update(
    filter: Partial<RoleEntity>,
    item: Partial<RoleEntity>,
  ): Promise<RoleEntity> {
    return this.toEntity(
      await this.roleRepository
        .findOneAndUpdate(this.toFilter(filter), this.toUpdate(item), {
          new: true,
        })
        .populate(this.toPopulate()),
    );
  }

  async updateMany(
    filter: Partial<RoleEntity>,
    items: Partial<RoleEntity>[],
  ): Promise<RoleEntity[]> {
    await this.roleRepository
      .updateMany(
        this.toFilter(filter),
        items.map((item) => this.toUpdate(item)),
      )
      .populate(this.toPopulate());
    return this.findAll(filter);
  }

  async deleteOne(filter: Partial<RoleEntity>): Promise<RoleEntity> {
    const document = await this.roleRepository.findOne(this.toFilter(filter));
    await this.roleRepository.deleteOne(this.toFilter(filter));
    return this.toEntity(document);
  }

  async deleteMany(filter: Partial<RoleEntity>): Promise<RoleEntity[]> {
    const documents = await this.roleRepository
      .find(this.toFilter(filter))
      .populate(this.toPopulate());
    await this.roleRepository.deleteMany(this.toFilter(filter));
    return documents.map((item) => this.toEntity(item));
  }

  async count(filter: Partial<RoleEntity>): Promise<number> {
    return this.roleRepository.countDocuments(this.toFilter(filter));
  }

  private toFilter(document: Partial<RoleEntity>) {
    const { id, createdBy, updatedBy, ...items } = document;

    return {
      ...items,
      ...(id && { _id: new mongoose.Types.ObjectId(id) }),
      ...(createdBy && { createdBy: new mongoose.Types.ObjectId(createdBy) }),
      ...(updatedBy && { updatedBy: new mongoose.Types.ObjectId(updatedBy) }),
    };
  }

  private toUpdate(item: Partial<RoleEntity>) {
    const { createdBy, updatedBy, ...rest } = item;
    return {
      ...rest,
      ...(createdBy && { createdBy: new mongoose.Types.ObjectId(createdBy) }),
      ...(updatedBy && { updatedBy: new mongoose.Types.ObjectId(updatedBy) }),
    };
  }

  private toPopulate() {
    return [
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' },
    ];
  }

  private toEntity(document: RoleDocument): RoleEntity {
    if (!document) return null;

    const role = new RoleEntity();
    role.id = document._id.toString();
    role.name = document.name;
    role.description = document.description;
    role.permissions = document.permissions || [];
    role.level = document.level as 'admin' | 'user' | 'moderator' | 'viewer';
    role.priority = document.priority;
    role.isActive = document.isActive;
    role.isDefault = document.isDefault;

    role.createdBy = document.createdBy?.toString();
    role.updatedBy = document.updatedBy?.toString();
    role.createdAt = document.createdAt;
    role.updatedAt = document.updatedAt;

    return role;
  }

  private toSchema(roleEntity: RoleEntity): RoleDocument {
    return new this.roleRepository({
      ...roleEntity,
      createdBy: roleEntity.createdBy
        ? new mongoose.Types.ObjectId(roleEntity.createdBy)
        : undefined,
      updatedBy: roleEntity.updatedBy
        ? new mongoose.Types.ObjectId(roleEntity.updatedBy)
        : undefined,
    });
  }
}
