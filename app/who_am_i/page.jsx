/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who-am-i/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 6:52:54 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */

"use client";
import {redirect} from 'next/navigation';
import {useRouter} from 'next/router';
import {useEffect} from 'react';
import ComingSoon from '@/components/ComingSoon/ComingSoon';

const Page = () => {
  const { push } = useRouter;

  useEffect(() => {
    redirect("/who_am_i/experience");
  }, []);

  return (
    <>
      <ComingSoon />
    </>
  );
};

export default Page;
