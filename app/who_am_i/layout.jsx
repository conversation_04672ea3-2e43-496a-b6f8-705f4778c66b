/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/who-am-i/layout.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Thursday, March 16th 2023, 6:53:02 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */
import React from 'react';
import LeftDrawer from '@/components/shared/LeftDrawer/LeftDrawer';

const Layout = ({ children }) => {
  
  return (
    <>
      <div className="main-box flex flex-col">
        <LeftDrawer />
        <main className="min-h-full body-main basis-10/12">{children}</main>
      </div>
    </>
  );
};

export default Layout;
