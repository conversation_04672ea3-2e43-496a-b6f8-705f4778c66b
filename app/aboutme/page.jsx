/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/aboutme/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Friday, July 14th 2023, 8:31:49 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */

"use client";
import {useEffect, useState} from 'react';
import './index.css';

import LeftSideBox from '@/components/AboutMe/LeftSideBox/LeftSideBox';
import RightSideBox from '@/components/AboutMe/RightSideBox/RightSideBox';
import AboutMeSideMenu from '@/components/AboutMeSideMenu/AboutMeSideMenu';
import ProfessionalInfoIcon from '/Assets/icons/info-personal.svg';
import PersonalInfoIcon from '/Assets/icons/info-professional.svg';

// TODO implement basic info from api
export const about = {
  sections: {
    "professional-info": {
      title: "professional-info",
      icon: ProfessionalInfoIcon,
      info: {
        experience: {
          title: "experience",
          description:
            "<br>Over the past 5 years, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. <br><br> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
        },
        skills: {
          title: "skills",
          description:
            "<br>As a front-end developer, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. <br><br> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
        },
      },
    },
    "personal-info": {
      title: "personal-info",
      icon: PersonalInfoIcon,
      info: {
        bio: {
          title: "bio",
          description:
            "<br> About me <br> I have 5 years of experience in web development lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. <br><br> Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat  nulla pariatur. Excepteur sint occaecat  officia deserunt mollit anim id est laborum.",
        },
        interests: {
          title: "interests",
          description:
            "<br>My main interest lies in learning and exploring new technologies to continuously enhance my knowledge. I have a strong desire to stay updated with the latest advancements in various fields, enabling me to adapt to a rapidly evolving digital landscape.",
        },
        education: {
          title: "education",
          description:
            "<br>I have always been passionate about lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. <br><br> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
          files: {
            "high-school": "I have been in 'Las viñas'...",
            university: "The university...",
          },
        },
      },
    },

  },
};

export const contact = {
  direct: {
    title: "contacts",
    sources: ["<EMAIL>", "+*************"],
  },
  social: {
    github: {
      title: "Github profile",
      url: "https://github.com/",
      user: "username",
    },
    facebook: {
      title: "Facebook profile",
      url: "https://facebook.com/",
      user: "username",
    },
    twitter: {
      title: "Twitter account",
      url: "https://twitter.com/",
      user: "username",
    },
  },
  find_me_also_in: {
    title: "find-me-also-in",
    sources: [
      {
        title: "YouTube channel",
        url: "https://www.youtube.com/",
        user: "username",
      },
      {
        title: "GuruShots profile",
        url: "https://gurushots.com/",
        user: "username",
      },
      {
        title: "Instagram account",
        url: "https://instagram.com/",
        user: "tanzim077",
      },
      {
        title: "Twitch profile",
        url: "https://twitch.com/",
        user: "username",
      },
    ],
  },
};

export const gists = {
  1: "a6712f4e7dade3d694b336ce77ad4f31",
  // 2: "83861a67e377633ee8368df01ee3a355",
  // 2: "694c1f32332788a2ac7f37b09e5aa40e",
};

const AboutMe = () => {
  const [currentSection, setCurrentSection] = useState("professional-info");
  const [folder, setFolder] = useState("experience");
  const [loading, setLoading] = useState(true);

  const focusCurrentSection = (section) => {
    setCurrentSection(about.sections[section].title);
    setFolder(Object.keys(about.sections[section].info)[0]);

    const foldersElement = document.getElementById("folders-" + section);

    const professionalFoldersElement = document.getElementById(
      "folders-professional-info"
    );
    const professionalSectionArrowElement = document.getElementById(
      "section-arrow-professional-info"
    );
    const personalFoldersElement = document.getElementById(
      "folders-personal-info"
    );
    const personalSectionArrowElement = document.getElementById(
      "section-arrow-personal-info"
    );

    if (foldersElement) {
      foldersElement.classList.toggle("hidden");
    }
    if (
      foldersElement !== personalFoldersElement &&
      !personalFoldersElement.classList.contains("hidden")
    ) {
      personalFoldersElement.classList.toggle("hidden");
      personalSectionArrowElement.classList.toggle("rotate-90");
    }
    if (
      foldersElement !== professionalFoldersElement &&
      !professionalFoldersElement.classList.contains("hidden")
    ) {
      professionalFoldersElement.classList.toggle("hidden");
      professionalSectionArrowElement.classList.toggle("rotate-90");
    }

    const sectionArrowElement = document.getElementById(
      "section-arrow-" + section
    );
    if (sectionArrowElement) {
      sectionArrowElement.classList.toggle("rotate-90");
    }
  };

  const focusCurrentFolder = (folder) => {
    setFolder(folder);
    const currentSectionInfo = about.sections[currentSection].info;
    const currentSectionHasFolder = currentSectionInfo[folder];
    if (!currentSectionHasFolder) {
      const section = Object.keys(about.sections).find(
        (section) => about.sections[section].info[folder.title]
      );
      setCurrentSection(section);
    }
  };

  const toggleFiles = () => {
    const fileElement = document.getElementById("file-" + folder);
    if (fileElement) {
      fileElement.classList.toggle("hidden");
    }
  };

  const showContacts = () => {
    const contactsElement = document.getElementById("contacts");
    if (contactsElement) {
      contactsElement.classList.toggle("hidden");
    }

    const sectionArrowElement = document.getElementById("section-arrow");
    if (sectionArrowElement) {
      sectionArrowElement.classList.toggle("rotate-90");
    }
  };

  useEffect(() => {
    setLoading(false);
}, []);

  if (loading) {
    return (
      <div id="about-me" className="page" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <div className="text-menu-text">Loading...</div>
      </div>
    );
  }

  return (
    <div id="about-me" className="page">
      <div id="mobile-page-title">
        <h2>_about-me</h2>
      </div>

      <AboutMeSideMenu
        currentFolder={folder}
        currentSection={currentSection}
        focusCurrentSection={focusCurrentSection}
        focusCurrentFolder={focusCurrentFolder}
        showContacts={showContacts}
      />

      <div
        className={`flex flex-col lg:grid h-full w-full ${
          folder === "experience" ||
          folder === "education" ||
          folder === "bio" ||
          folder === "skills" 
            ? "lg:grid-cols-1"
            : "lg:grid-cols-2"
        }`}
      >
        {/* Your content goes here */}
        <LeftSideBox currentSection={currentSection} folder={folder} />

        {folder !== "experience" &&
        folder !== "education" &&
        folder !== "bio" &&
        folder !== "skills" ? (
          <RightSideBox />
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default AboutMe;
