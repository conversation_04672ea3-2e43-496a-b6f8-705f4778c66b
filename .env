#SERVER
; API_URL=http://ec2-13-233-121-63.ap-south-1.compute.amazonaws.com/api/
API_URL=http://localhost:8080/api/
API_VERSION=v1
BASEPATH=""
; NEXT_PUBLIC_APP_URL=http://ec2-43-204-238-211.ap-south-1.compute.amazonaws.com/${BASEPATH}
; NEXT_PUBLIC_SERVER_URL=http://ec2-43-204-238-211.ap-south-1.compute.amazonaws.com/${BASEPATH}
NEXT_PUBLIC_APP_URL=http://localhost:8080/${BASEPATH}
NEXT_PUBLIC_SERVER_URL=http://localhost:8080/api/v1

NEXT_PUBLIC_DOCS_URL=https://demos.themeselection.com/materio-mui-nextjs-admin-template/documentation
NEXT_PUBLIC_PRO_URL=https://demos.themeselection.com/materio-mui-nextjs-admin-template/demo-1

NEXT_PUBLIC_REPO_NAME=materio-mui-nextjs-admin-template-free
NEXTAUTH_SECRET=dasdwewsd232
; NEXTAUTH_URL=http://ec2-43-204-238-211.ap-south-1.compute.amazonaws.com
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_emailjs_public_key
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dztzlaqdr
